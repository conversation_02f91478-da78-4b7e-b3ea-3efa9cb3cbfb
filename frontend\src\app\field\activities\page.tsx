'use client';
import { useState, useEffect } from 'react';
import { 
  ClockIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  CalendarIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  DocumentTextIcon
} from '@heroicons/react/24/solid';
import { api } from '@/services/api';
import { toast } from 'react-hot-toast';

interface Activity {
  _id: string;
  title: string;
  description: string;
  status: 'pending' | 'inprogress' | 'ongoing' | 'completed' | 'overdue';
  dueDate: string;
  startDate?: string;
  endDate?: string;
  assignmentDate?: string;
  createdAt: string;
  budget?: number;
  location?: string;
  targetOutcome?: string;
  priority?: 'high' | 'medium' | 'low';
  project: {
    _id: string;
    name: string;
    budget?: number;
  };
  assignedTo: {
    fullName: string;
    email: string;
  };
  assignedBy?: {
    fullName: string;
    email: string;
  };
  progress?: number;
  hasReport?: boolean;
}

export default function ActivitiesPage() {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'ongoing' | 'completed' | 'overdue'>('all');
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);

  useEffect(() => {
    const fetchActivities = async () => {
      setLoading(true);
      try {
        console.log('🔄 Fetching assigned activities...');
        const response = await api.getAssignedActivities();
        console.log('✅ Activities loaded:', response);
        
        const activitiesData = Array.isArray(response.data)
          ? response.data
          : (response.data as any)?.data || [];
        
        // Process activities with real data only
        const processedActivities = activitiesData.map((activity: any) => ({
          ...activity,
          progress: activity.progress || (activity.status === 'completed' ? 100 : 0),
          hasReport: activity.hasReport || false,
          status: activity.status || 'pending'
        }));
        
        setActivities(processedActivities);
      } catch (err: any) {
        console.error('❌ Failed to load activities:', err);
        const errorMessage = err?.response?.data?.message || err?.message || 'Failed to load activities';
        setError(errorMessage);
        toast.error(`Error: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'inprogress':
      case 'ongoing':
        return <ClockIcon className="w-5 h-5 text-blue-500" />;
      case 'overdue':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'inprogress':
      case 'ongoing':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'No date set';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Invalid date';
    }
  };

  const formatDateRange = (startDate: string | null | undefined, endDate: string | null | undefined) => {
    if (!startDate && !endDate) return 'No dates set';
    if (!startDate) return `End: ${formatDate(endDate)}`;
    if (!endDate) return `Start: ${formatDate(startDate)}`;

    const start = formatDate(startDate);
    const end = formatDate(endDate);

    if (start === end) return start;
    return `${start} - ${end}`;
  };

  const formatStatusText = (status: string) => {
    switch (status) {
      case 'inprogress':
        return 'In Progress';
      case 'all':
        return 'All';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const filteredActivities = activities.filter(activity => 
    filter === 'all' || activity.status === filter
  );

  const getActivityStats = () => {
    return {
      total: activities.length,
      pending: activities.filter(a => a.status === 'pending').length,
      ongoing: activities.filter(a => a.status === 'ongoing').length,
      completed: activities.filter(a => a.status === 'completed').length,
      overdue: activities.filter(a => a.status === 'overdue').length,
    };
  };

  const stats = getActivityStats();

  if (loading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600"></div>
          <span className="ml-3 text-gray-600">Loading activities...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error Loading Activities</h3>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">My Activities</h1>
        <div className="text-sm text-gray-600">
          Total: {stats.total} activities
        </div>
      </div>

      {/* Stats Cards */}
      {/* <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-gray-800">{stats.total}</div>
          <div className="text-sm text-gray-600">Total</div>
        </div>
        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-gray-500">{stats.pending}</div>
          <div className="text-sm text-gray-600">Pending</div>
        </div>
        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-blue-600">{stats.ongoing}</div>
          <div className="text-sm text-gray-600">Ongoing</div>
        </div>
        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          <div className="text-sm text-gray-600">Completed</div>
        </div>
        <div className="bg-white rounded-lg shadow p-4">
          <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
          <div className="text-sm text-gray-600">Overdue</div>
        </div>
      </div> */}

      {/* Filter Buttons */}
      <div className="flex space-x-2 mb-6">
        {['all', 'pending', 'inprogress', 'ongoing', 'completed', 'overdue'].map((status) => (
          <button
            key={status}
            onClick={() => setFilter(status as any)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === status
                ? 'bg-sky-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            {formatStatusText(status)}
          </button>
        ))}
      </div>

      {/* Activities List */}
      {filteredActivities.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <div className="text-gray-400 mb-4">
            <ClockIcon className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Activities Found</h3>
          <p className="text-gray-600">
            {filter === 'all'
              ? "You don't have any activities assigned yet."
              : `No ${filter} activities found.`}
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Activity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Project
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timeline
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Budget
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredActivities.map((activity) => (
                  <tr
                    key={activity._id}
                    className="hover:bg-gray-50 transition-colors duration-200"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 mr-3">
                          {getStatusIcon(activity.status)}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {activity.title || 'Untitled Activity'}
                          </div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {activity.description || 'No description available'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {activity.project?.name || 'No Project'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(activity.status)}`}>
                        {formatStatusText(activity.status)}
                      </span>
                      {activity.hasReport && (
                        <div className="mt-1">
                          <span className="inline-flex px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            ✓ Reported
                          </span>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              activity.status === 'completed' ? 'bg-blue-500' :
                              activity.status === 'inprogress' || activity.status === 'ongoing' ? 'bg-yellow-500' :
                              activity.status === 'overdue' ? 'bg-red-500' :
                              'bg-gray-300'
                            }`}
                            style={{ width: `${activity.progress || 0}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">
                          {activity.progress || 0}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {activity.startDate || activity.endDate ? (
                          <div className="font-medium">
                            {formatDateRange(activity.startDate, activity.endDate)}
                          </div>
                        ) : activity.dueDate ? (
                          <div className="font-medium">Due: {formatDate(activity.dueDate)}</div>
                        ) : (
                          <div className="text-gray-500">No dates set</div>
                        )}
                      </div>
                      {activity.location && (
                        <div className="text-sm text-gray-500 mt-1">
                          📍 {activity.location}
                        </div>
                      )}
                      {activity.priority && (
                        <div className="mt-1">
                          <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                            activity.priority === 'high' ? 'bg-red-100 text-red-800' :
                            activity.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {activity.priority.charAt(0).toUpperCase() + activity.priority.slice(1)}
                          </span>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {activity.budget ? `MWK ${activity.budget.toLocaleString()}` : 'N/A'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => setSelectedActivity(activity)}
                        className="text-sky-600 hover:text-sky-900 transition-colors duration-200"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Activity Details Modal */}
      {selectedActivity && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-xl font-bold text-gray-800">
                  {selectedActivity.title || 'Untitled Activity'}
                </h2>
                <button
                  onClick={() => setSelectedActivity(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-700 mb-2">Description</h3>
                  <p className="text-gray-600">
                    {selectedActivity.description || 'No description available'}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-gray-700 mb-2">Project</h3>
                    <p className="text-gray-600">{selectedActivity.project?.name || 'No Project'}</p>
                  </div>

                  <div>
                    <h3 className="font-semibold text-gray-700 mb-2">Status</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedActivity.status)}`}>
                      {formatStatusText(selectedActivity.status)}
                    </span>
                  </div>

                  {selectedActivity.startDate && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Start Date</h3>
                      <p className="text-gray-600">{formatDate(selectedActivity.startDate)}</p>
                    </div>
                  )}

                  {selectedActivity.endDate && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">End Date</h3>
                      <p className="text-gray-600">{formatDate(selectedActivity.endDate)}</p>
                    </div>
                  )}

                  {selectedActivity.dueDate && !selectedActivity.endDate && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Due Date</h3>
                      <p className="text-gray-600">{formatDate(selectedActivity.dueDate)}</p>
                    </div>
                  )}

                  {selectedActivity.assignmentDate && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Assigned Date</h3>
                      <p className="text-gray-600">{formatDate(selectedActivity.assignmentDate)}</p>
                    </div>
                  )}

                  <div>
                    <h3 className="font-semibold text-gray-700 mb-2">Created</h3>
                    <p className="text-gray-600">{formatDate(selectedActivity.createdAt)}</p>
                  </div>

                  {selectedActivity.budget && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Budget</h3>
                      <p className="text-gray-600">MWK {selectedActivity.budget.toLocaleString()}</p>
                    </div>
                  )}

                  {selectedActivity.location && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Location</h3>
                      <p className="text-gray-600">{selectedActivity.location}</p>
                    </div>
                  )}

                  {selectedActivity.priority && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Priority</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        selectedActivity.priority === 'high' ? 'bg-red-100 text-red-800' :
                        selectedActivity.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {selectedActivity.priority.charAt(0).toUpperCase() + selectedActivity.priority.slice(1)}
                      </span>
                    </div>
                  )}

                  {selectedActivity.assignedBy && (
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Assigned By</h3>
                      <p className="text-gray-600">{selectedActivity.assignedBy.fullName}</p>
                    </div>
                  )}
                </div>

                {selectedActivity.targetOutcome && (
                  <div className="mt-4">
                    <h3 className="font-semibold text-gray-700 mb-2">Target Outcome</h3>
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">{selectedActivity.targetOutcome}</p>
                  </div>
                )}

                <div>
                  <h3 className="font-semibold text-gray-700 mb-2">Progress</h3>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-300 ${
                        selectedActivity.status === 'completed' ? 'bg-blue-500' :
                        selectedActivity.status === 'inprogress' || selectedActivity.status === 'ongoing' ? 'bg-yellow-500' :
                        selectedActivity.status === 'overdue' ? 'bg-red-500' :
                        'bg-gray-300'
                      }`}
                      style={{ width: `${selectedActivity.progress || 0}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{selectedActivity.progress || 0}% Complete</p>
                </div>

                {selectedActivity.hasReport && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2" />
                      <span className="text-green-800 font-medium">Report Submitted</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setSelectedActivity(null)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
