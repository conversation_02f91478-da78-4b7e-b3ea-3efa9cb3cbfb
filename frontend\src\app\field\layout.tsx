"use client";
import React, { useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  HomeIcon,
  UserCircleIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  ClipboardDocumentListIcon,
  PaperAirplaneIcon,
} from "@heroicons/react/24/solid";
import CollapsibleSidebar, { getGreeting } from "@/components/CollapsibleSidebar";
import NotificationBell from "@/components/NotificationBell";
import api from '@/utils/api';

interface FieldLayoutProps {
  children: React.ReactNode;
}

const navLinks = [
  { name: "Dashboard", href: "/field/dashboard", icon: HomeIcon },
  { name: "Activities", href: "/field/activities", icon: ClipboardDocumentListIcon },
  { name: "Submit Report", href: "/field/submit-report", icon: PaperAirplaneIcon },
  { name: "My Reports", href: "/field/reports", icon: DocumentTextIcon },
];

export default function FieldLayout({ children }: FieldLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [user, setUser] = useState<{ name: string, profilePicture?: string } | null>(null);
  const [userName, setUserName] = useState('Field Officer');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    api.get('/api/v1/user/me', { withCredentials: true })
      .then(res => {
        // The API returns data in res.data.data structure
        const userData = res.data?.data || res.data;
        setUser(userData);
        // Get the actual name from the user data
        const actualName = userData?.fullName || userData?.name || userData?.firstName || 'Field Officer';
        setUserName(actualName);
      })
      .catch(err => {
        console.error('Failed to fetch user profile:', err);

        // Try localStorage as fallback
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            const user = JSON.parse(storedUser);
            const actualName = user.fullName || user.name || user.firstName || 'Field Officer';
            setUserName(actualName);
          } catch (error) {
            console.error('Failed to parse stored user data:', error);
            setUserName('Field Officer');
          }
        } else {
          setUserName('Field Officer');
        }
      });
  }, []);

  const getPageTitle = () => {
    if (pathname.endsWith('/dashboard')) return 'Dashboard';
    if (pathname.endsWith('/activities')) return 'Activities';
    if (pathname.endsWith('/reports')) return 'Reports';
    if (pathname.endsWith('/profile')) return 'Profile';
    return 'Field Officer';
  };

  return (
    <div className="min-h-screen bg-[#f4f6fa]">
      <style jsx global>{`
        /* Custom scrollbar styles for field officer pages */
        ::-webkit-scrollbar {
          height: 6px;
          width: 6px;
        }

        ::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #555;
        }

        /* Firefox scrollbar */
        * {
          scrollbar-width: thin;
          scrollbar-color: #888 #f1f1f1;
        }
      `}</style>
      <CollapsibleSidebar
        navLinks={navLinks}
        userName={userName}
        userRole="Field Officer"
        onCollapseChange={setIsSidebarCollapsed}
      >
        {/* Header */}
        <header className="sticky top-0 z-10 flex items-center justify-between bg-white border-b border-gray-100 px-6 py-3">
          <div className="text-lg font-bold text-sky-700 tracking-tight">
            {getGreeting()}, {userName}!
          </div>
          <div className="flex items-center gap-3">
            <NotificationBell userRole="fieldOfficer" />
            <button
              onClick={() => router.push('/field/profile')}
              className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden hover:bg-gray-300 transition-colors"
            >
              <UserCircleIcon className="w-6 h-6 text-sky-400" />
            </button>
          </div>
        </header>
        <main className="p-6">{children}</main>
      </CollapsibleSidebar>
    </div>
  );
}
