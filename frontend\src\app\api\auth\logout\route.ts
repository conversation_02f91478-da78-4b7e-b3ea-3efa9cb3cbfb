import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  try {
    console.log('🚪 Logout request received');

    // Get the token from cookies to pass to backend
    const token = request.headers.get('cookie')?.split('; ')
      .find(row => row.startsWith('token='))
      ?.split('=')[1];

    // Call backend logout endpoint to update user online status
    if (token) {
      try {
        await axios.post('http://localhost:7000/api/v1/auth/logout', {}, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        });
        console.log('✅ Backend logout successful');
      } catch (backendError) {
        console.error('❌ Backend logout failed:', backendError);
        // Continue with frontend logout even if backend fails
      }
    }

    // Create response
    const response = NextResponse.json({
      message: 'Logout successful',
    });

    // Clear the token cookie
    response.cookies.set('token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0, // Expire immediately
      path: '/',
    });

    console.log('✅ Token cookie cleared');
    return response;
  } catch (error: any) {
    console.error('❌ Logout error:', error);
    return NextResponse.json({ message: 'Logout failed' }, { status: 500 });
  }
}
