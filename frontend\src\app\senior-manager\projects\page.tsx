"use client";

import React, { useState } from "react";
import { Tab } from "@headlessui/react";
import ProjectCreationForm from "./components/ProjectCreationForm";
import ProjectPlanningForm from "./components/ProjectPlanningForm";
import ProjectAssignmentForm from "./components/ProjectAssignmentForm";
import ProjectsList from "./components/ProjectsList";
import { useRouter } from 'next/navigation';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

type WorkflowStep = "create" | "plan" | "assign" | "complete";

export default function ProjectsPage() {
  const [currentStep, setCurrentStep] = useState<WorkflowStep>("create");
  const [currentProjectId, setCurrentProjectId] = useState<string>("");
  const [currentProjectTitle, setCurrentProjectTitle] = useState<string>("");
  const router = useRouter();

  const handleProjectCreated = (projectId: string, projectTitle: string) => {
    setCurrentProjectId(projectId);
    setCurrentProjectTitle(projectTitle);
    setCurrentStep("plan");
  };

  const handlePlanCreated = () => {
    setCurrentStep("assign");
  };

  const handleAssignmentComplete = () => {
    setCurrentStep("complete");
  };

  const resetWorkflow = () => {
    setCurrentStep("create");
    setCurrentProjectId("");
    setCurrentProjectTitle("");
  };



  const getStepTitle = () => {
    switch (currentStep) {
      case "create": return "Step 1: Create Project";
      case "plan": return "Step 2: Project Planning";
      case "assign": return "Step 3: Assign to Project Manager";
      case "complete": return "Project Management Workflow Complete";
      default: return "Project Management";
    }
  };

  const renderWorkflowStep = () => {
    switch (currentStep) {
      case "create":
        return <ProjectCreationForm onProjectCreated={handleProjectCreated} />;

      case "plan":
        return (
          <ProjectPlanningForm
            projectId={currentProjectId}
            projectTitle={currentProjectTitle}
            onPlanCreated={handlePlanCreated}
          />
        );

      case "assign":
        return (
          <ProjectAssignmentForm
            projectId={currentProjectId}
            projectTitle={currentProjectTitle}
            onAssignmentComplete={handleAssignmentComplete}
          />
        );

      case "complete":
        return (
          <div className="max-w-2xl mx-auto p-6 bg-green-50 border border-green-200 rounded-md">
            <div className="text-center">
              <div className="text-green-600 text-6xl mb-4">✓</div>
              <h2 className="text-2xl font-semibold text-green-800 mb-4">
                Project Successfully Created and Assigned!
              </h2>
              <p className="text-green-700 mb-6">
                The project "{currentProjectTitle}" has been created with a complete plan and assigned to a project manager.
                The assigned manager has received all project details and can now begin execution.
              </p>
              <div className="space-x-4">
                <button
                  onClick={resetWorkflow}
                  className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Create Another Project
                </button>
                <button
                  onClick={() => router.push("/senior-manager/dashboard")}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Projects Management</h1>
        <p className="text-gray-600">Create new projects and manage existing ones</p>
      </div>

      <Tab.Group>
        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 mb-8">
          <Tab
            className={({ selected }) =>
              classNames(
                "w-full rounded-lg py-2.5 text-sm font-medium leading-5",
                "ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2",
                selected
                  ? "bg-white text-blue-700 shadow"
                  : "text-blue-100 hover:bg-white/[0.12] hover:text-white"
              )
            }
          >
            Create Project
          </Tab>
          <Tab
            className={({ selected }) =>
              classNames(
                "w-full rounded-lg py-2.5 text-sm font-medium leading-5",
                "ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2",
                selected
                  ? "bg-white text-blue-700 shadow"
                  : "text-blue-100 hover:bg-white/[0.12] hover:text-white"
              )
            }
          >
            Projects
          </Tab>
        </Tab.List>

        <Tab.Panels>
          <Tab.Panel>
            {/* Project Creation Workflow */}
            {currentStep !== "create" ? (
              <div className="mb-8">
                {/* Workflow Progress Indicator */}
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900">{getStepTitle()}</h2>
                  <div className="flex items-center space-x-2">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      currentStep === "create" ? "bg-blue-600 text-white" : "bg-green-600 text-white"
                    }`}>
                      1
                    </div>
                    <div className={`w-16 h-1 ${currentStep === "plan" || currentStep === "assign" || currentStep === "complete" ? "bg-green-600" : "bg-gray-300"}`}></div>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      currentStep === "plan" ? "bg-blue-600 text-white" :
                      currentStep === "assign" || currentStep === "complete" ? "bg-green-600 text-white" : "bg-gray-300 text-gray-600"
                    }`}>
                      2
                    </div>
                    <div className={`w-16 h-1 ${currentStep === "assign" || currentStep === "complete" ? "bg-green-600" : "bg-gray-300"}`}></div>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      currentStep === "assign" ? "bg-blue-600 text-white" :
                      currentStep === "complete" ? "bg-green-600 text-white" : "bg-gray-300 text-gray-600"
                    }`}>
                      3
                    </div>
                  </div>
                </div>

                {/* Step descriptions */}
                {currentStep !== "complete" && (
                  <div className="text-sm text-gray-600 mb-6">
                    <span className="font-medium">Current Step:</span> {
                      currentStep === "create" ? "Create project with title, description, objectives, goals, beneficiaries, location, timeline, category, and budget" :
                      currentStep === "plan" ? "Add project activities, deliverables, milestones, resources, risks, and assumptions" :
                      currentStep === "assign" ? "Select and assign a project manager to receive the compiled project details"
                      : ""
                    }
                  </div>
                )}
              </div>
            ) : null}

            {/* Current Step Content */}
            {renderWorkflowStep()}
          </Tab.Panel>

          <Tab.Panel>
            {/* Projects List */}
            <ProjectsList />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>

    </div>
  );
}
