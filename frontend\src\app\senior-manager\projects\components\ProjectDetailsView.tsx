"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import ExcelJS from "exceljs";
import { exportProjectPDF } from "@/utils/exportProjectPDF";
import { saveAs } from "file-saver";


// Types
interface Goal {
  title: string;
  target: string;
  progress?: number;
}

interface Objective {
  title: string;
  goals: Goal[];
}

interface InputItem {
  name: string;
  quantity: number;
  date?: string;
}

interface OutputItem {
  name: string;
  value: number;
  date?: string;
}

interface KPI {
  name: string;
  target: string;
}

interface Project {
  _id: string;
  name: string;
  sector: string;
  district: string;
  startDate: string;
  endDate: string;
  duration?: string;
  assignedTo?: {
    _id: string;
    fullName: string;
  };
  kpis?: KPI[];
  justification?: string;
  budget?: number | string;
  status?: string;
}

interface ProjectPlan {
  expectedOutcome: string;
  objectives: Objective[];
  inputs: InputItem[];
  outputs: OutputItem[];
}

function Section({
  title,
  children,
  defaultOpen = false,
}: {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}) {
  const [open, setOpen] = React.useState(defaultOpen);
  return (
    <div className="mb-4 border rounded-lg bg-white shadow">
      <button
        className="w-full flex justify-between items-center px-4 py-3 font-semibold text-blue-700 text-lg focus:outline-none"
        onClick={() => setOpen((o) => !o)}
      >
        <span>{title}</span>
        <span>{open ? "-" : "+"}</span>
      </button>
      {open && <div className="px-4 pb-4">{children}</div>}
    </div>
  );
}

export default function ProjectDetailsView({ project }: { project: Project | null }) {
  const [plan, setPlan] = useState<ProjectPlan | null>(null);

  useEffect(() => {
    const fetchPlan = async () => {
      if (!project?._id) return;
      try {
        const response = await axios.get(`http://localhost:7000/api/v1/project-plan/plan/${project._id}`);
        setPlan(response.data);
      } catch (error) {
        console.error("Error fetching project plan:", error);
      }
    };
    fetchPlan();
  }, [project]);

  if (!project) {
    return <div className="text-center text-gray-500 mt-20">Select a project to see the details.</div>;
  }

  const durationText = `${new Date(project.startDate).toLocaleDateString()} - ${new Date(project.endDate).toLocaleDateString()}`;

  // Export PDF function
  const exportPDF = () => {
    if (!project || !plan) return;

    const doc = new jsPDF();
    doc.setFontSize(18);
    doc.text(`Project: ${project.name}`, 14, 20);
    doc.setFontSize(12);
    doc.text(`Sector: ${project.sector}`, 14, 28);
    doc.text(`District: ${project.district}`, 14, 36);
    doc.text(`Duration: ${durationText}`, 14, 44);
    doc.text(`Status: ${project.status ?? "N/A"}`, 14, 52);
    doc.text(`Budget: ${project.budget ?? "N/A"}`, 14, 60);

    if (project.kpis?.length) {
      (doc as any).autoTable({
        startY: 68,
        head: [["KPI", "Target"]],
        body: project.kpis.map((kpi) => [kpi.name, kpi.target]),
        theme: "striped",
      });
    }

    let finalY = (doc as any).lastAutoTable?.finalY ?? 68;
    doc.text(`Expected Outcome:`, 14, finalY + 10);
    doc.text(plan.expectedOutcome, 14, finalY + 18, { maxWidth: 180 });

    let y = finalY + 30;
    plan.objectives.forEach((obj, idx) => {
      doc.setFont(undefined, "bold");
      doc.text(`Objective ${idx + 1}: ${obj.title}`, 14, y);
      y += 8;
      doc.setFont(undefined, "normal");
      obj.goals.forEach((goal) => {
        doc.text(`- Goal: ${goal.title} (Target: ${goal.target})`, 18, y);
        y += 7;
      });
      y += 4;
    });

    if (plan.inputs?.length) {
      (doc as any).autoTable({
        startY: y,
        head: [["Input Name", "Quantity", "Date"]],
        body: plan.inputs.map((i) => [i.name, i.quantity.toString(), i.date ?? "N/A"]),
        theme: "grid",
      });
      y = (doc as any).lastAutoTable.finalY + 10;
    }

    if (plan.outputs?.length) {
      (doc as any).autoTable({
        startY: y,
        head: [["Output Name", "Value", "Date"]],
        body: plan.outputs.map((o) => [o.name, o.value.toString(), o.date ?? "N/A"]),
        theme: "grid",
      });
    }

    doc.save(`${project.name}_details.pdf`);
  };

  // Export Excel function
  const exportExcel = async () => {
    if (!project || !plan) return;

    const workbook = new ExcelJS.Workbook();

    // Overview Sheet
    const overviewSheet = workbook.addWorksheet("Overview");
    overviewSheet.addRows([
      ["Field", "Value"],
      ["Project Title", project.name],
      ["Sector", project.sector],
      ["District", project.district],
      ["Duration", durationText],
      ["Status", project.status ?? "N/A"],
      ["Budget", project.budget ?? "N/A"],
    ]);

    // KPIs Sheet
    if (project.kpis?.length) {
      const kpiSheet = workbook.addWorksheet("KPIs");
      kpiSheet.addRow(["KPI", "Target"]);
      project.kpis.forEach((kpi) => {
        kpiSheet.addRow([kpi.name, kpi.target]);
      });
    }

    // Objectives & Goals Sheet
    const objSheet = workbook.addWorksheet("Objectives & Goals");
    objSheet.addRow(["Objective", "Goal", "Goal Target"]);
    plan.objectives.forEach((obj) => {
      obj.goals.forEach((goal) => {
        objSheet.addRow([obj.title, goal.title, goal.target]);
      });
    });

    // Inputs Sheet
    if (plan.inputs?.length) {
      const inputsSheet = workbook.addWorksheet("Inputs");
      inputsSheet.addRow(["Input Name", "Quantity", "Date"]);
      plan.inputs.forEach((i) => {
        inputsSheet.addRow([i.name, i.quantity.toString(), i.date ?? "N/A"]);
      });
    }

    // Outputs Sheet
    if (plan.outputs?.length) {
      const outputsSheet = workbook.addWorksheet("Outputs");
      outputsSheet.addRow(["Output Name", "Value", "Date"]);
      plan.outputs.forEach((o) => {
        outputsSheet.addRow([o.name, o.value.toString(), o.date ?? "N/A"]);
      });
    }

    // Write to buffer and save
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
    saveAs(blob, `${project.name}_details.xlsx`);
  };

  return (
    <div className="max-w-4xl mx-auto py-8 px-2 md:px-0">
      <h1 className="text-3xl font-bold text-blue-900 mb-6">Project Details</h1>

      {/* 1. PROJECT OVERVIEW */}
      <Section title="1. Project Overview" defaultOpen>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="font-semibold">Project Title:</span> {project.name}
          </div>
          <div>
            <span className="font-semibold">Sector:</span> {project.sector}
          </div>
          <div>
            <span className="font-semibold">District:</span> {project.district}
          </div>
          <div>
            <span className="font-semibold">Start - End Date:</span> {durationText}
          </div>
          <div>
            <span className="font-semibold">Duration:</span> {project.duration ?? "N/A"}
          </div>
          <div>
            <span className="font-semibold">Assigned To:</span> {project.assignedTo?.fullName ?? "N/A"}
          </div>
          <div>
            <span className="font-semibold">Status:</span> {project.status}
          </div>
          <div>
            <span className="font-semibold">Budget:</span> {project.budget ?? "N/A"}
          </div>
        </div>

        {project.kpis?.length > 0 && (
          <div className="mt-4">
            <span className="font-semibold block">KPIs:</span>
            <ul className="list-disc ml-6 space-y-1">
              {project.kpis.map((kpi, idx) => (
                <li key={idx}>
                  <strong>{kpi.name}</strong> — Target: {kpi.target}
                </li>
              ))}
            </ul>
          </div>
        )}

        {project.justification && (
          <div className="mt-4">
            <span className="font-semibold block">Justification:</span>
            <p className="text-gray-800">{project.justification}</p>
          </div>
        )}
      </Section>

      {/* PROJECT PLAN */}
      <Section title="2. Project Plan">
        {!plan ? (
          <p className="text-gray-500">No project plan available.</p>
        ) : (
          <>
            <div className="mb-4">
              <span className="font-semibold">Expected Outcome:</span> {plan.expectedOutcome}
            </div>

            {plan.objectives?.length > 0 && (
              <div>
                <div className="font-semibold mb-2">Objectives:</div>
                <ul className="list-disc ml-6 mb-2 space-y-2">
                  {plan.objectives.map((obj, idx) => (
                    <li key={idx}>
                      <span className="font-semibold">{obj.title}</span>
                      <ul className="list-[circle] ml-6">
                        {(obj.goals ?? []).map((goal, index) => (
                          <li key={index}>
                            Goal: {goal.title} (Target: {goal.target})
                          </li>
                        ))}
                      </ul>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* INPUTS */}
            {plan.inputs?.length > 0 && (
              <div className="mt-6">
                <div className="font-semibold mb-1">Inputs:</div>
                <table className="min-w-full text-sm">
                  <thead>
                    <tr className="text-left text-gray-600">
                      <th className="py-2">Input Name</th>
                      <th className="py-2">Quantity</th>
                    
                    </tr>
                  </thead>
                  <tbody>
                    {plan.inputs.map((input, idx) => (
                      <tr key={idx}>
                        <td>{input.name}</td>
                        <td>{input.quantity}</td>

                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* OUTPUTS */}
            {plan.outputs?.length > 0 && (
              <div className="mt-6">
                <div className="font-semibold mb-1">Outputs:</div>
                <table className="min-w-full text-sm">
                  <thead>
                    <tr className="text-left text-gray-600">
                      <th className="py-2">Output Name</th>
                      <th className="py-2">Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {plan.outputs.map((output, idx) => (
                      <tr key={idx}>
                        <td>{output.name}</td>
                        <td>{output.value}</td>
                      
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </>
        )}
      </Section>

      {/* EXPORT */}
      <Section title="3. Export Options">
        <div className="flex gap-4">
        <button
  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
  onClick={() => {
    if (project && plan) exportProjectPDF(project, plan);
              }}
                    >
                     Export PDF
                    </button>

          <button
            onClick={exportExcel}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Export Excel
          </button>
        </div>
      </Section>
    </div>
  );
}
