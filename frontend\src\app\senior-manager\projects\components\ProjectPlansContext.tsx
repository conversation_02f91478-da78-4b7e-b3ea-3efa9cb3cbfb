"use client";
import React, { createContext, useContext, useState } from "react";

// Plan structure should match ProjectPlanView
export interface ProjectPlan {
  projectName: string;
  objectives: string[];
  goals: string[];
  budget: { category: string; amount: number }[];
  activities: string[];
  expectedOutcome: string;
  status?: string; // e.g., 'Draft', 'Sent'
}

interface ProjectPlansContextType {
  plans: ProjectPlan[];
  addPlan: (plan: ProjectPlan) => void;
  updatePlan: (idx: number, plan: ProjectPlan) => void;
}

const ProjectPlansContext = createContext<ProjectPlansContextType | undefined>(undefined);

export function ProjectPlansProvider({ children }: { children: React.ReactNode }) {
  const [plans, setPlans] = useState<ProjectPlan[]>([]);

  const addPlan = (plan: ProjectPlan) => setPlans((prev) => [...prev, plan]);
  const updatePlan = (idx: number, plan: ProjectPlan) => setPlans((prev) => prev.map((p, i) => (i === idx ? plan : p)));

  return (
    <ProjectPlansContext.Provider value={{ plans, addPlan, updatePlan }}>
      {children}
    </ProjectPlansContext.Provider>
  );
}

export function useProjectPlans() {
  const ctx = useContext(ProjectPlansContext);
  if (!ctx) throw new Error("useProjectPlans must be used within a ProjectPlansProvider");
  return ctx;
} 