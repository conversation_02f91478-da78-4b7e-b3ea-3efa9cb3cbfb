import React, { useState, useEffect } from "react";
import { User } from "@/types/types";
import api from "@/utils/api";
import axios from "axios";
import { XMarkIcon } from "@heroicons/react/24/outline";

const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000';

interface SimpleEditUserModalProps {
  user: User;
  setIsEditUserModalOpen: (isOpen: boolean) => void;
  callBack: () => void;
}

const SimpleEditUserModal: React.FC<SimpleEditUserModalProps> = ({
  user,
  setIsEditUserModalOpen,
  callBack,
}) => {
  const [formData, setFormData] = useState({
    fullName: user.fullName || user.name || "",
    email: user.email || "",
    phoneNumber: user.phoneNumber || user.phone_number || "",
    district: typeof user.district === 'object' ? user.district?._id : user.district || "",
  });

  const [districts, setDistricts] = useState<any[]>([]);
  const [isUpdatingUser, setIsUpdatingUser] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  useEffect(() => {
    fetchDistricts();
  }, []);

  const fetchDistricts = async () => {
    try {
      const res = await axios.get(`${baseUrl}/api/v1/district`);
      const districts = Array.isArray(res.data) ? res.data : res.data?.districts || res.data?.data || [];
      setDistricts(districts);
      console.log("Districts loaded:", districts);
    } catch (error: any) {
      console.error("Error fetching districts:", error);
      console.error("Error details:", error?.response?.data);

      let errorMessage = "Failed to load districts. Please check your connection.";
      if (error?.response?.status === 401) {
        errorMessage = "Authentication failed. Please log in again.";
      } else if (error?.response?.status === 403) {
        errorMessage = "Access denied. Admin privileges required.";
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setError(errorMessage);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdatingUser(true);
    setError("");
    setSuccess("");

    // Validate required fields
    if (!formData.fullName || !formData.email || !formData.phoneNumber || !formData.district) {
      setError("Please fill in all required fields");
      setIsUpdatingUser(false);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError("Please enter a valid email address");
      setIsUpdatingUser(false);
      return;
    }

    try {
      const response = await api.patch(`/api/v1/user/${user._id || user.id}`, formData);
      
      if (response.data && (response.data.status === 'success' || response.data._id)) {
        setSuccess("User updated successfully!");
        await callBack();
        
        // Close modal after a short delay to show success message
        setTimeout(() => {
          setIsEditUserModalOpen(false);
        }, 1500);
      }
    } catch (error: any) {
      console.error("Error updating user:", error);

      // Safe error message extraction
      let errorMessage = "Failed to update user. Please try again.";

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.status === 400) {
        errorMessage = "User with this email already exists";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsUpdatingUser(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md relative">
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-red-600 transition-colors"
          onClick={() => setIsEditUserModalOpen(false)}
        >
          <XMarkIcon className="w-6 h-6" />
        </button>
        
        <h2 className="text-xl font-bold text-gray-800 mb-6">Edit User</h2>
        
        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
            {error}
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-lg text-sm">
            {success}
          </div>
        )}

        <form onSubmit={handleUpdateUser} className="space-y-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
              value={formData.fullName}
              onChange={handleInputChange}
              name="fullName"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
              value={formData.email}
              onChange={handleInputChange}
              name="email"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Contact (Phone) *
            </label>
            <input
              type="tel"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              name="phoneNumber"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Branch (District) *
            </label>
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
              value={formData.district}
              onChange={handleInputChange}
              name="district"
              required
            >
              <option value="">Select District</option>
              {districts.map((district: any) => (
                <option key={district._id} value={district._id}>
                  {district.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              className="px-4 py-2 rounded-lg border border-gray-300 bg-gray-50 text-gray-700 hover:bg-gray-100 font-semibold transition-colors"
              onClick={() => setIsEditUserModalOpen(false)}
              disabled={isUpdatingUser}
            >
              Cancel
            </button>
            <button
              disabled={isUpdatingUser}
              type="submit"
              className="px-6 py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 shadow transition-colors disabled:opacity-50"
            >
              {isUpdatingUser ? "Updating..." : "Update User"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SimpleEditUserModal;
