//{
//"compilerOptions": {
//"target": "ES2017",
// "lib": ["dom", "dom.iterable", "esnext"],
//"allowJs": true,
//"skipLibCheck": true,
//"strict": true,
//"noEmit": true,
//"esModuleInterop": true,
//"module": "esnext",
//"moduleResolution": "bundler",
//"resolveJsonModule": true,
//"isolatedModules": true,
//"jsx": "preserve",
//"incremental": true,
//"plugins": [
//{
//"name": "next"
// }
//],
//"paths": {
//"@/*": ["./src/*"]
//}
//},
//"include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
// "exclude": ["node_modules"]
//}
{
  "compilerOptions": {
    "jsx": "preserve",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    },
    "target": "ES2017",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": false,
    "noEmit": true,
    "incremental": true,
    "module": "esnext",
    "esModuleInterop": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "next-env.d.ts",
    ".next/types/**/*.ts",
    "**/*.ts",
    "**/*.tsx"
, "next.config.js"  ],
  "exclude": [
    "node_modules"
  ]
}
