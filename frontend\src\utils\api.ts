import axios from 'axios';

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});


// Add a request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bear<PERSON> ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 errors globally
    if (error.response?.status === 401) {
      console.error('🔐 Global 401 error - clearing auth and redirecting to login');

      // Clear stored authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('userId');
      localStorage.removeItem('userRole');

      // Only redirect if we're not already on the login page
      if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

api.getRecentActivities = async () => {
  const res = await api.get('/api/v1/recentActivities/recent');
  return res.data.data; // return just the activities array
};

// Team Management Functions
api.createTeam = async (teamData) => {
  const response = await api.post('/api/v1/team', teamData);
  return response.data;
};

api.getAllTeams = async (params) => {
  const response = await api.get('/api/v1/team', { params });
  return response.data;
};

api.getTeamById = async (teamId) => {
  const response = await api.get(`/api/v1/team/${teamId}`);
  return response.data;
};

api.updateTeam = async (teamId, teamData) => {
  const response = await api.put(`/api/v1/team/${teamId}`, teamData);
  return response.data;
};

api.deleteTeam = async (teamId) => {
  const response = await api.delete(`/api/v1/team/${teamId}`);
  return response.data;
};

api.assignTeamToManager = async (teamId, assignmentData) => {
  const response = await api.post(`/api/v1/team/${teamId}/assign`, assignmentData);
  return response.data;
};

// Password Reset Functions
api.forgotPassword = async (email) => {
  const response = await api.post('/api/v1/auth/forgot-password', { email });
  return response.data;
};

api.resetPassword = async (token, newPassword) => {
  const response = await api.post('/api/v1/auth/reset-password', { token, newPassword });
  return response.data;
};

export default api;