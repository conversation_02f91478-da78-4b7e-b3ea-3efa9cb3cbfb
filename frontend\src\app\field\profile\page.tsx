'use client';
import { useState, useEffect, useRef } from 'react';
import { api } from '@/services/api';
import {
  UserCircleIcon,
  PencilIcon,
  XMarkIcon,
  KeyIcon,
} from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';
import PasswordChangeModal from '@/components/PasswordChangeModal';

/* ------------------------------------------------------------------ */
/* Types                                                              */
/* ------------------------------------------------------------------ */
interface Option {
  _id: string;
  name: string;
}
interface Profile {
  name: string;
  email: string;
  phone: string;
  location: string;        // name shown to user
  locationId?: string;     // ObjectId
  department: string;
  departmentId?: string;
  position: string;
  bio: string;
  experience: number;
  skills: string[];
  profilePicture?: string;
}

/* ------------------------------------------------------------------ */
/* Component                                                          */
/* ------------------------------------------------------------------ */
export default function ProfilePage() {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [editing, setEditing] = useState(false);
  const [draft, setDraft] = useState<Profile | null>(null);
  const [picFile, setPicFile] = useState<File | null>(null);

  const [districts, setDistricts] = useState<Option[]>([]);
  const [departments, setDepartments] = useState<Option[]>([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  const fileRef = useRef<HTMLInputElement | null>(null);

  /* ------------------------------------------------------------------ */
  /* Fetch profile + dropdown data on mount                             */
  /* ------------------------------------------------------------------ */
  useEffect(() => {
    (async () => {
      try {
        /* 1. fetch user */
        const raw: any = await api.getProfile();
        const mapped: Profile = {
          name: raw.name ?? raw.fullName ?? '',
          email: raw.email ?? '',
          phone: raw.phone_number ?? raw.phoneNumber ?? '',
          location:
            typeof raw.district === 'object'
              ? raw.district.name
              : raw.district ?? '',
          locationId:
            typeof raw.district === 'object' ? raw.district._id : raw.districtId,
          department:
            typeof raw.department === 'object'
              ? raw.department.name
              : raw.department ?? '',
          departmentId:
            typeof raw.department === 'object'
              ? raw.department._id
              : raw.departmentId,
          position: raw.role?.name ?? '',
          bio: raw.bio ?? '',
          experience: raw.experience ?? 0,
          skills: raw.skills ?? [],
          profilePicture: raw.profilePicture ?? '',
        };
        setProfile(mapped);

        /* 2. fetch options in parallel */
        const [dRes, depRes] = await Promise.all([
          api.getDistricts(),
          api.getDepartments(),
        ]);

        setDistricts(dRes.data ?? []);
        setDepartments(depRes.data ?? []);
      } catch (err: any) {
        console.error(err);
        setError(
          err?.response?.data?.message ||
            'Could not fetch profile – please try again.',
        );
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  /* ------------------------------------------------------------------ */
  /* Edit helpers                                                       */
  /* ------------------------------------------------------------------ */
  const startEdit = () => {
    if (!profile) return;
    setDraft({ ...profile });
    setPicFile(null);
    setEditing(true);
  };

  const cancelEdit = () => {
    setEditing(false);
    setDraft(null);
    setPicFile(null);
  };

  const setDraftField = (field: keyof Profile, value: any) => {
    if (!draft) return;
    setDraft({ ...draft, [field]: value });
  };

  const handlePicChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setPicFile(file);
      const url = URL.createObjectURL(file);
      setDraft(prev => (prev ? { ...prev, profilePicture: url } : null));
    }
  };

  const saveChanges = async () => {
    if (!draft) return;
    try {
      let payload: FormData | Record<string, any>;
      if (picFile) {
        const fd = new FormData();
        fd.append('profilePicture', picFile);
        Object.entries(draft).forEach(([k, v]) => {
          if (k !== 'profilePicture') {
            fd.append(
              k,
              Array.isArray(v) ? JSON.stringify(v) : String(v ?? ''),
            );
          }
        });
        payload = fd;
      } else {
        payload = { ...draft };
      }
      await api.updateProfile(payload);

      // Refresh profile data from server to ensure sync
      const updatedProfile = await api.getProfile();
      const mapped: Profile = {
        name: updatedProfile.name ?? updatedProfile.fullName ?? '',
        email: updatedProfile.email ?? '',
        phone: updatedProfile.phone_number ?? updatedProfile.phoneNumber ?? '',
        location:
          typeof updatedProfile.district === 'object'
            ? updatedProfile.district.name
            : updatedProfile.district ?? '',
        locationId:
          typeof updatedProfile.district === 'object' ? updatedProfile.district._id : updatedProfile.districtId,
        department:
          typeof updatedProfile.department === 'object'
            ? updatedProfile.department.name
            : updatedProfile.department ?? '',
        departmentId:
          typeof updatedProfile.department === 'object'
            ? updatedProfile.department._id
            : updatedProfile.departmentId,
        position: updatedProfile.role?.name ?? '',
        bio: updatedProfile.bio ?? '',
        experience: updatedProfile.experience ?? 0,
        skills: updatedProfile.skills ?? [],
        profilePicture: updatedProfile.profilePicture ?? '',
      };
      setProfile(mapped);

      toast.success('Profile updated!');
      setEditing(false);
      setPicFile(null);
      setDraft(null);
    } catch (err: any) {
      console.error(err);
      toast.error(
        err?.response?.data?.message || 'Update failed – please try again.',
      );
    }
  };

  /* ------------------------------------------------------------------ */
  /* Render states                                                      */
  /* ------------------------------------------------------------------ */
  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        Loading profile…
      </div>
    );
  }
  if (error) {
    return (
      <div className="flex h-screen w-full items-center justify-center text-red-600">
        {error}
      </div>
    );
  }
  if (!profile) return null;

  const view = editing && draft ? draft : profile;

  /* ------------------------------------------------------------------ */
  /* UI                                                                 */
  /* ------------------------------------------------------------------ */
  return (
    <div className="min-h-screen w-full bg-white">
      <div
        className="relative mx-auto w-full max-w-2xl overflow-auto rounded-2xl bg-white shadow-xl"
        style={{ minHeight: 540, maxHeight: '90vh' }}
      >
        <div className="flex flex-col gap-8 p-8 pb-0 md:flex-row">
          {/* Avatar -------------------------------------------------- */}
          <div className="flex min-w-[160px] flex-col items-center md:items-start">
            <div className="group relative mb-2 h-28 w-28">
              {view.profilePicture ? (
                <img
                  src={view.profilePicture.startsWith('http') ? view.profilePicture : `http://localhost:7000${view.profilePicture}`}
                  alt=""
                  className="h-28 w-28 rounded-full border-4 border-white object-cover shadow"
                />
              ) : (
                <div className="flex h-28 w-28 items-center justify-center rounded-full border-4 border-white bg-sky-100 shadow">
                  <UserCircleIcon className="h-20 w-20 text-sky-400" />
                </div>
              )}

              {editing && (
                <label className="absolute bottom-0 right-0 cursor-pointer rounded-full bg-sky-500 p-2 text-white transition hover:bg-sky-600">
                  <PencilIcon className="h-5 w-5" />
                  <input
                    ref={fileRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handlePicChange}
                  />
                </label>
              )}
            </div>

            {!editing ? (
              <div className="flex flex-col gap-2">
                <button
                  type="button"
                  onClick={startEdit}
                  className="mt-1 flex items-center gap-1 text-sm text-gray-400 transition hover:text-sky-500"
                >
                  <PencilIcon className="h-4 w-4" /> Edit Profile
                </button>
                <button
                  type="button"
                  onClick={() => setShowPasswordModal(true)}
                  className="flex items-center gap-1 text-sm text-gray-400 transition hover:text-blue-500"
                >
                  <KeyIcon className="h-4 w-4" /> Change Password
                </button>
              </div>
            ) : (
              <button
                type="button"
                onClick={cancelEdit}
                className="mt-1 flex items-center gap-1 text-sm text-red-500 transition hover:text-red-600"
              >
                <XMarkIcon className="h-4 w-4" /> Cancel
              </button>
            )}
          </div>

          {/* Form ---------------------------------------------------- */}
          <form
            onSubmit={e => {
              e.preventDefault();
              if (editing) saveChanges();
            }}
            className="flex flex-1 flex-col gap-6"
          >
            <h1 className="mb-2 text-2xl font-bold text-gray-700">My Profile</h1>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <EditableInput
                label="Full Name"
                value={view.name}
                disabled={!editing}
                onChange={val => setDraftField('name', val)}
              />
              <EditableInput
                label="Email Address"
                type="email"
                value={view.email}
                disabled={!editing}
                onChange={val => setDraftField('email', val)}
              />

              <EditableInput
                label="Phone"
                value={view.phone}
                disabled={!editing}
                onChange={val => setDraftField('phone', val)}
              />

              {/* Location dropdown when editing -------------------- */}
              <div>
                <label className="mb-1 block text-xs font-semibold text-gray-500">
                  Location
                </label>
                {!editing ? (
                  <input
                    value={view.location}
                    disabled
                    className="w-full rounded border border-gray-200 bg-gray-100 p-2 text-gray-700"
                  />
                ) : (
                  <select
                    value={draft?.locationId ?? ''}
                    onChange={e => {
                      const id = e.target.value;
                      const found = districts.find(d => d._id === id);
                      setDraftField('locationId', id);
                      setDraftField('location', found?.name ?? '');
                    }}
                    className="w-full rounded border border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="" disabled>
                      Select district
                    </option>
                    {districts.map(d => (
                      <option key={d._id} value={d._id}>
                        {d.name}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              {/* Department dropdown when editing ------------------ */}
              <div>
                <label className="mb-1 block text-xs font-semibold text-gray-500">
                  Department
                </label>
                {!editing ? (
                  <input
                    value={view.department}
                    disabled
                    className="w-full rounded border border-gray-200 bg-gray-100 p-2 text-gray-700"
                  />
                ) : (
                  <select
                    value={draft?.departmentId ?? ''}
                    onChange={e => {
                      const id = e.target.value;
                      const found = departments.find(d => d._id === id);
                      setDraftField('departmentId', id);
                      setDraftField('department', found?.name ?? '');
                    }}
                    className="w-full rounded border border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="" disabled>
                      Select department
                    </option>
                    {departments.map(dep => (
                      <option key={dep._id} value={dep._id}>
                        {dep.name}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              <EditableTextarea
                label="Bio"
                value={view.bio}
                disabled={!editing}
                onChange={val => setDraftField('bio', val)}
                className="md:col-span-2"
              />

              <EditableInput
                label="Experience (years)"
                type="number"
                value={String(view.experience)}
                disabled={!editing}
                onChange={val => setDraftField('experience', Number(val))}
              />

              {/* Skills ------------------------------------------ */}
              <div className="md:col-span-2">
                <label className="mb-1 block text-xs font-semibold text-gray-500">
                  Skills
                </label>
                {!editing ? (
                  <div className="flex flex-wrap gap-2">
                    {view.skills.length
                      ? view.skills.map(s => (
                          <span
                            key={s}
                            className="flex items-center rounded-full bg-sky-100 px-3 py-1 text-sky-700"
                          >
                            {s}
                          </span>
                        ))
                      : '—'}
                  </div>
                ) : (
                  <input
                    value={draft?.skills.join(', ') ?? ''}
                    onChange={e =>
                      setDraftField(
                        'skills',
                        e.target.value
                          .split(',')
                          .map(x => x.trim())
                          .filter(Boolean),
                      )
                    }
                    className="w-full rounded border border-gray-300 p-2"
                  />
                )}
              </div>
            </div>

            {editing && (
              <div className="mt-4 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={cancelEdit}
                  className="rounded bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
                >
                  Save
                </button>
              </div>
            )}
          </form>
        </div>
      </div>

      {/* Password Change Modal */}
      <PasswordChangeModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
      />
    </div>
  );
}

/* ------------------------------------------------------------------ */
/* Tiny reusable controls                                             */
/* ------------------------------------------------------------------ */
interface EditableProps {
  label: string;
  value: string;
  disabled: boolean;
  onChange: (val: string) => void;
  type?: string;
  className?: string;
}
function EditableInput({
  label,
  value,
  disabled,
  onChange,
  type = 'text',
  className,
}: EditableProps) {
  return (
    <div className={className}>
      <label className="mb-1 block text-xs font-semibold text-gray-500">
        {label}
      </label>
      <input
        type={type}
        value={value}
        disabled={disabled}
        onChange={e => onChange(e.target.value)}
        className={`w-full rounded border p-2 ${
          disabled
            ? 'border-gray-200 bg-gray-100 text-gray-700'
            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
        }`}
      />
    </div>
  );
}

function EditableTextarea({
  label,
  value,
  disabled,
  onChange,
  className,
}: EditableProps) {
  return (
    <div className={className}>
      <label className="mb-1 block text-xs font-semibold text-gray-500">
        {label}
      </label>
      <textarea
        value={value}
        disabled={disabled}
        rows={2}
        onChange={e => onChange(e.target.value)}
        className={`w-full rounded border p-2 ${
          disabled
            ? 'border-gray-200 bg-gray-100 text-gray-700'
            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
        }`}
      />
    </div>
  );
}
