import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  // Protect dashboard routes: if not logged in, redirect to login
  const token = request.cookies.get('token')?.value;
  const isDashboard = request.nextUrl.pathname.match(/^\/(admin|manager|accountant|field|senior-manager)(\/|$)/);

  if (isDashboard && !token) {
    const loginUrl = new URL('/login', request.url);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!_next|static|favicon.ico|robots.txt).*)'],
}; 