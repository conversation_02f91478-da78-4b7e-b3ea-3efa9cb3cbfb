"use client";

import React, { useState } from "react";
import { api } from "@/services/api";

interface Activity {
  title: string;
  description: string;
}

interface Deliverable {
  title: string;
  description: string;
  deadline: string;
}

interface Milestone {
  title: string;
  description: string;
  deadline: string;
}

interface Resource {
  type: "human" | "material" | "financial" | "equipment";
  name: string;
  quantity: number;
  cost: number;
}

interface Risk {
  title: string;
  description: string;
  severity: "low" | "medium" | "high";
  mitigation: string;
}

interface Assumption {
  title: string;
  description: string;
}

interface ProjectPlanFormData {
  activities: Activity[];
  deliverables: Deliverable[];
  milestones: Milestone[];
  resources: Resource[];
  risks: Risk[];
  assumptions: Assumption[];
}

interface ProjectPlanningFormProps {
  projectId: string;
  projectTitle: string;
  onPlanCreated: () => void;
}

type PlanningStep = 1 | 2 | 3;

export default function ProjectPlanningForm({
  projectId,
  projectTitle,
  onPlanCreated
}: ProjectPlanningFormProps) {
  const [currentStep, setCurrentStep] = useState<PlanningStep>(1);
  const [formData, setFormData] = useState<ProjectPlanFormData>({
    activities: [{ title: "", description: "" }],
    deliverables: [{ title: "", description: "", deadline: "" }],
    milestones: [{ title: "", description: "", deadline: "" }],
    resources: [{ type: "human", name: "", quantity: 1, cost: 0 }],
    risks: [{ title: "", description: "", severity: "low", mitigation: "" }],
    assumptions: [{ title: "", description: "" }],
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<any>({});

  const validateStep = (step: PlanningStep): boolean => {
    const newErrors: any = {};

    switch (step) {
      case 1:
        if (formData.activities.filter(activity => activity.title.trim()).length === 0) {
          newErrors.activities = "At least one activity is required";
        }
        break;
      case 2:
        if (formData.deliverables.filter(deliverable => deliverable.title.trim()).length === 0) {
          newErrors.deliverables = "At least one deliverable is required";
        }
        if (formData.milestones.filter(milestone => milestone.title.trim()).length === 0) {
          newErrors.milestones = "At least one milestone is required";
        }
        break;
      case 3:
        if (formData.resources.filter(resource => resource.name.trim()).length === 0) {
          newErrors.resources = "At least one resource is required";
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, 3) as PlanningStep);
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1) as PlanningStep);
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return "Project Activities";
      case 2: return "Deliverables & Milestones";
      case 3: return "Resources, Risks & Assumptions";
      default: return "Project Planning";
    }
  };

  // Activity handlers
  const handleActivityChange = (index: number, field: keyof Activity, value: string) => {
    const updated = [...formData.activities];
    updated[index][field] = value;
    setFormData({ ...formData, activities: updated });
  };

  const addActivity = () => {
    setFormData({
      ...formData,
      activities: [...formData.activities, { title: "", description: "" }]
    });
  };

  const removeActivity = (index: number) => {
    const updated = formData.activities.filter((_, i) => i !== index);
    setFormData({ ...formData, activities: updated });
  };

  // Deliverable handlers
  const handleDeliverableChange = (index: number, field: keyof Deliverable, value: string) => {
    const updated = [...formData.deliverables];
    updated[index][field] = value;
    setFormData({ ...formData, deliverables: updated });
  };

  const addDeliverable = () => {
    setFormData({
      ...formData,
      deliverables: [...formData.deliverables, { title: "", description: "", deadline: "" }]
    });
  };

  const removeDeliverable = (index: number) => {
    const updated = formData.deliverables.filter((_, i) => i !== index);
    setFormData({ ...formData, deliverables: updated });
  };

  // Milestone handlers
  const handleMilestoneChange = (index: number, field: keyof Milestone, value: string) => {
    const updated = [...formData.milestones];
    updated[index][field] = value as any;
    setFormData({ ...formData, milestones: updated });
  };

  const addMilestone = () => {
    setFormData({
      ...formData,
      milestones: [...formData.milestones, { title: "", description: "", deadline: "" }]
    });
  };

  const removeMilestone = (index: number) => {
    const updated = formData.milestones.filter((_, i) => i !== index);
    setFormData({ ...formData, milestones: updated });
  };

  // Resource handlers
  const handleResourceChange = (index: number, field: keyof Resource, value: string | number) => {
    const updated = [...formData.resources];
    if (field === 'quantity') {
      updated[index][field] = value === '' ? 0 : parseInt(value as string) || 0;
    } else if (field === 'cost') {
      updated[index][field] = value === '' ? 0 : parseFloat(value as string) || 0;
    } else {
      updated[index][field] = value as any;
    }
    setFormData({ ...formData, resources: updated });
  };

  const addResource = () => {
    setFormData({
      ...formData,
      resources: [...formData.resources, { type: "human", name: "", quantity: 1, cost: 0 }]
    });
  };

  const removeResource = (index: number) => {
    const updated = formData.resources.filter((_, i) => i !== index);
    setFormData({ ...formData, resources: updated });
  };

  // Risk handlers
  const handleRiskChange = (index: number, field: keyof Risk, value: string) => {
    const updated = [...formData.risks];
    updated[index][field] = value as any;
    setFormData({ ...formData, risks: updated });
  };

  const addRisk = () => {
    setFormData({
      ...formData,
      risks: [...formData.risks, { title: "", description: "", severity: "low", mitigation: "" }]
    });
  };

  const removeRisk = (index: number) => {
    const updated = formData.risks.filter((_, i) => i !== index);
    setFormData({ ...formData, risks: updated });
  };

  // Assumption handlers
  const handleAssumptionChange = (index: number, field: keyof Assumption, value: string) => {
    const updated = [...formData.assumptions];
    updated[index][field] = value;
    setFormData({ ...formData, assumptions: updated });
  };

  const addAssumption = () => {
    setFormData({
      ...formData,
      assumptions: [...formData.assumptions, { title: "", description: "" }]
    });
  };

  const removeAssumption = (index: number) => {
    const updated = formData.assumptions.filter((_, i) => i !== index);
    setFormData({ ...formData, assumptions: updated });
  };

  const validateForm = () => {
    const newErrors: any = {};
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Activities validation
    if (!formData.activities || formData.activities.length === 0) {
      newErrors.activities = "At least one activity is required";
    } else {
      formData.activities.forEach((activity, index) => {
        if (!activity.title || !activity.title.trim()) {
          newErrors[`activity_${index}_title`] = "Activity title is required";
        }
        if (!activity.description || !activity.description.trim()) {
          newErrors[`activity_${index}_description`] = "Activity description is required";
        }
      });
    }

    // Deliverables validation
    if (!formData.deliverables || formData.deliverables.length === 0) {
      newErrors.deliverables = "At least one deliverable is required";
    } else {
      formData.deliverables.forEach((deliverable, index) => {
        if (!deliverable.title || !deliverable.title.trim()) {
          newErrors[`deliverable_${index}_title`] = "Deliverable title is required";
        }
        if (!deliverable.description || !deliverable.description.trim()) {
          newErrors[`deliverable_${index}_description`] = "Deliverable description is required";
        }
        if (!deliverable.deadline) {
          newErrors[`deliverable_${index}_deadline`] = "Deliverable deadline is required";
        } else {
          const deadline = new Date(deliverable.deadline);
          if (deadline < today) {
            newErrors[`deliverable_${index}_deadline`] = "Deliverable deadline cannot be in the past";
          }
        }
      });
    }

    // Milestones validation
    if (!formData.milestones || formData.milestones.length === 0) {
      newErrors.milestones = "At least one milestone is required";
    } else {
      formData.milestones.forEach((milestone, index) => {
        if (!milestone.title || !milestone.title.trim()) {
          newErrors[`milestone_${index}_title`] = "Milestone title is required";
        }
        if (!milestone.description || !milestone.description.trim()) {
          newErrors[`milestone_${index}_description`] = "Milestone description is required";
        }
        if (!milestone.deadline) {
          newErrors[`milestone_${index}_deadline`] = "Milestone deadline is required";
        } else {
          const deadline = new Date(milestone.deadline);
          if (deadline < today) {
            newErrors[`milestone_${index}_deadline`] = "Milestone deadline cannot be in the past";
          }
        }
      });
    }

    // Resources validation
    if (!formData.resources || formData.resources.length === 0) {
      newErrors.resources = "At least one resource is required";
    } else {
      formData.resources.forEach((resource, index) => {
        if (!resource.name || !resource.name.trim()) {
          newErrors[`resource_${index}_name`] = "Resource name is required";
        }
        if (!resource.quantity || resource.quantity <= 0) {
          newErrors[`resource_${index}_quantity`] = "Resource quantity must be greater than 0";
        }
        if (!resource.cost || resource.cost < 0) {
          newErrors[`resource_${index}_cost`] = "Resource cost cannot be negative";
        }
      });
    }

    // Risks validation
    if (!formData.risks || formData.risks.length === 0) {
      newErrors.risks = "At least one risk assessment is required";
    } else {
      formData.risks.forEach((risk, index) => {
        if (!risk.title || !risk.title.trim()) {
          newErrors[`risk_${index}_title`] = "Risk title is required";
        }
        if (!risk.description || !risk.description.trim()) {
          newErrors[`risk_${index}_description`] = "Risk description is required";
        }
        if (!risk.mitigation || !risk.mitigation.trim()) {
          newErrors[`risk_${index}_mitigation`] = "Risk mitigation strategy is required";
        }
      });
    }

    // Assumptions validation
    if (!formData.assumptions || formData.assumptions.length === 0) {
      newErrors.assumptions = "At least one assumption is required";
    } else {
      formData.assumptions.forEach((assumption, index) => {
        if (!assumption.title || !assumption.title.trim()) {
          newErrors[`assumption_${index}_title`] = "Assumption title is required";
        }
        if (!assumption.description || !assumption.description.trim()) {
          newErrors[`assumption_${index}_description`] = "Assumption description is required";
        }
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      alert("Please fix all validation errors before submitting");
      return;
    }

    setIsSubmitting(true);

    try {
      // Transform the data to match the backend createProjectPlan function structure
      const payload = {
        projectId: projectId, // Backend expects 'projectId'
        activities: formData.activities,
        deliverables: formData.deliverables,
        milestones: formData.milestones,
        resources: formData.resources,
        risks: formData.risks.map(risk => ({
          ...risk,
          probability: risk.severity, // Map severity to probability for backend
          impact: risk.severity       // Map severity to impact for backend
        })),
        assumptions: formData.assumptions
      };

      console.log("📝 Sending project plan payload:", payload);
      console.log("📊 Payload details:", {
        project: projectId,
        activitiesCount: formData.activities.length,
        deliverablesCount: formData.deliverables.length,
        milestonesCount: formData.milestones.length,
        resourcesCount: formData.resources.length,
        risksCount: formData.risks.length,
        assumptionsCount: formData.assumptions.length
      });

      await api.createProjectPlan(payload);
      alert("Project plan created successfully! You can now assign the project to a project manager.");
      onPlanCreated();
    } catch (error: any) {
      console.error("Failed to create project plan:", error);

      // Handle validation errors from backend
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        const backendErrors = error.response.data.errors.join('\n');
        alert(`Validation failed:\n${backendErrors}`);
      } else if (error.response?.data?.message) {
        alert(`Error: ${error.response.data.message}`);
      } else {
        alert("An error occurred while creating the project plan. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Project Activities</h3>
              <div className="space-y-6">
                {formData.activities.map((activity, index) => (
                  <div key={index} className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Activity Title <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Enter activity title"
                          value={activity.title}
                          onChange={(e) => handleActivityChange(index, "title", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Activity Description <span className="text-red-500">*</span>
                        </label>
                        <textarea
                          placeholder="Describe the activity in detail"
                          value={activity.description}
                          onChange={(e) => handleActivityChange(index, "description", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 h-24 resize-none"
                        />
                      </div>
                    </div>
                    {formData.activities.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeActivity(index)}
                        className="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove Activity
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addActivity}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Activity
                </button>
                {errors.activities && <p className="text-red-500 text-sm mt-2">{errors.activities}</p>}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            {/* Deliverables Section */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Project Deliverables</h3>
              <div className="space-y-6">
                {formData.deliverables.map((deliverable, index) => (
                  <div key={index} className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Deliverable Title <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Enter deliverable title"
                          value={deliverable.title}
                          onChange={(e) => handleDeliverableChange(index, "title", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Deadline
                        </label>
                        <input
                          type="date"
                          value={deliverable.deadline}
                          onChange={(e) => handleDeliverableChange(index, "deadline", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                    </div>
                    <div className="mt-4">
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Description <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        placeholder="Describe the deliverable"
                        value={deliverable.description}
                        onChange={(e) => handleDeliverableChange(index, "description", e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 h-24 resize-none"
                      />
                    </div>
                    {formData.deliverables.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeDeliverable(index)}
                        className="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove Deliverable
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addDeliverable}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Deliverable
                </button>
                {errors.deliverables && <p className="text-red-500 text-sm mt-2">{errors.deliverables}</p>}
              </div>
            </div>

            {/* Milestones Section */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Project Milestones</h3>
              <div className="space-y-6">
                {formData.milestones.map((milestone, index) => (
                  <div key={index} className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Milestone Title <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Enter milestone title"
                          value={milestone.title}
                          onChange={(e) => handleMilestoneChange(index, "title", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Target Date
                        </label>
                        <input
                          type="date"
                          value={milestone.deadline}
                          onChange={(e) => handleMilestoneChange(index, "deadline", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                    </div>
                    <div className="mt-4">
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Description <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        placeholder="Describe the milestone"
                        value={milestone.description}
                        onChange={(e) => handleMilestoneChange(index, "description", e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 h-24 resize-none"
                      />
                    </div>
                    {formData.milestones.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeMilestone(index)}
                        className="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove Milestone
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addMilestone}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Milestone
                </button>
                {errors.milestones && <p className="text-red-500 text-sm mt-2">{errors.milestones}</p>}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            {/* Resources Section */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Project Resources</h3>
              <div className="space-y-6">
                {formData.resources.map((resource, index) => (
                  <div key={index} className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Resource Type <span className="text-red-500">*</span>
                        </label>
                        <select
                          value={resource.type}
                          onChange={(e) => handleResourceChange(index, "type", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        >
                          <option value="human">Human</option>
                          <option value="material">Material</option>
                          <option value="financial">Financial</option>
                          <option value="equipment">Equipment</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Resource Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Enter resource name"
                          value={resource.name}
                          onChange={(e) => handleResourceChange(index, "name", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Quantity <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          placeholder="Enter quantity"
                          value={resource.quantity || ''}
                          onChange={(e) => handleResourceChange(index, "quantity", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          min="1"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Cost (MWK) <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          placeholder="Enter cost"
                          value={resource.cost || ''}
                          onChange={(e) => handleResourceChange(index, "cost", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>
                    {formData.resources.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeResource(index)}
                        className="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove Resource
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addResource}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Resource
                </button>
                {errors.resources && <p className="text-red-500 text-sm mt-2">{errors.resources}</p>}
              </div>
            </div>

            {/* Risks Section */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Project Risks</h3>
              <div className="space-y-6">
                {formData.risks.map((risk, index) => (
                  <div key={index} className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Risk Title <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Enter risk title"
                          value={risk.title}
                          onChange={(e) => handleRiskChange(index, "title", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Severity <span className="text-red-500">*</span>
                        </label>
                        <select
                          value={risk.severity}
                          onChange={(e) => handleRiskChange(index, "severity", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>
                    </div>
                    <div className="mt-4">
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Risk Description <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        placeholder="Describe the risk"
                        value={risk.description}
                        onChange={(e) => handleRiskChange(index, "description", e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 h-24 resize-none"
                      />
                    </div>
                    <div className="mt-4">
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Mitigation Strategy
                      </label>
                      <textarea
                        placeholder="How will this risk be mitigated?"
                        value={risk.mitigation}
                        onChange={(e) => handleRiskChange(index, "mitigation", e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 h-24 resize-none"
                      />
                    </div>
                    {formData.risks.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeRisk(index)}
                        className="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove Risk
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addRisk}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Risk
                </button>
              </div>
            </div>

            {/* Assumptions Section */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Project Assumptions</h3>
              <div className="space-y-6">
                {formData.assumptions.map((assumption, index) => (
                  <div key={index} className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Assumption Title <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Enter assumption title"
                          value={assumption.title}
                          onChange={(e) => handleAssumptionChange(index, "title", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Description <span className="text-red-500">*</span>
                        </label>
                        <textarea
                          placeholder="Describe the assumption"
                          value={assumption.description}
                          onChange={(e) => handleAssumptionChange(index, "description", e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 h-24 resize-none"
                        />
                      </div>
                    </div>
                    {formData.assumptions.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeAssumption(index)}
                        className="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove Assumption
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addAssumption}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Assumption
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-8 bg-white shadow-lg rounded-xl border border-gray-100">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Project Planning</h2>
        <p className="text-gray-600">Create a comprehensive plan for: <span className="font-semibold text-blue-600">{projectTitle}</span></p>
        <p className="text-sm text-gray-500 mt-2">Step {currentStep} of 3: {getStepTitle()}</p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          {[1, 2, 3].map((step) => (
            <div
              key={step}
              className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                step <= currentStep
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {step}
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / 3) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Step Content */}
        {renderStepContent()}

        {/* Navigation Buttons */}
        <div className="pt-6 border-t border-gray-200">
          <div className="flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Previous
            </button>

            {currentStep < 3 ? (
              <button
                type="button"
                onClick={nextStep}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                Next
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            ) : (
              <button
                type="submit"
                disabled={isSubmitting}
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating Plan...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Plan
                  </>
                )}
              </button>
            )}
          </div>
          </div>
      </form>
    </div>
  );
}
