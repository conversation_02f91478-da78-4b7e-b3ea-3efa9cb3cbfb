"use client";
import axios from "axios";
import { useState } from "react";

interface Goal {
  _id?: any;
  title: string;
  target: string;
}

interface Objective {
  _id?: any;
  title: string;
  goals: Goal[];
}

interface InputItem {
  name: string;
  quantity: number;
}

interface OutputItem {
  name: string;
  value: number;
}

interface ProjectPlanFormProps {
  projectId: string;
  onSubmit: (plan: any) => void;
}

export default function ProjectPlanForm({ projectId, onSubmit }: ProjectPlanFormProps) {
  const [objectives, setObjectives] = useState<Objective[]>([]);
  const [expectedOutcome, setExpectedOutcome] = useState("");
  const [inputs, setInputs] = useState<InputItem[]>([]);
  const [outputs, setOutputs] = useState<OutputItem[]>([]);

  const addObjective = () => {
    setObjectives([
      ...objectives,
      { title: "", goals: [] }
    ]);
  };

  const addGoal = (objIndex: number) => {
    const updated = [...objectives];
    updated[objIndex].goals.push({ title: "", target: "" });
    setObjectives(updated);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
  
    if (!projectId || !expectedOutcome) {
      alert("Project ID and Expected Outcome are required.");
      return;
    }
  
    try {
      const createdGoalIds: string[] = [];
      const createdObjectiveIds: string[] = [];
  
      // STEP 1: Create goals and store their IDs
      for (const obj of objectives) {
        for (const goal of obj.goals) {
          const res = await axios.post("http://localhost:7000/api/v1/project-plan/goal", {
            projectId,
            description: goal.title,
          });
  
          const goalId = res.data?.data?._id;
          if (goalId) createdGoalIds.push(goalId);
        }
      }
  
      // STEP 2: Create objectives linked to goals (use first goal per objective)
      let goalIndex = 0;
      for (const obj of objectives) {
        const goalId = createdGoalIds[goalIndex];
        const res = await axios.post("http://localhost:7000/api/v1/project-plan/objective", {
          goalId,
          description: obj.title,
          isMandatory: false, // adjust as needed
          targetDate: new Date(), // placeholder, adjust as needed
        });
  
        const objectiveId = res.data?.data?._id;
        if (objectiveId) createdObjectiveIds.push(objectiveId);
  
        goalIndex += obj.goals.length; // move index forward for multiple goals per objective
      }
  
      // STEP 3: Submit the final project plan
      const payload = {
        projectId: projectId,
        activities: [], // Add empty activities array if needed
        deliverables: [],
        milestones: [],
        resources: [],
        risks: [],
        assumptions: []
      };
  
      console.log("📦 Final plan payload:", payload);
  
      const planRes = await axios.post("http://localhost:7000/api/v1/project/plan", payload);
      alert("✅ Project plan added successfully!");
      onSubmit(planRes.data);
    } catch (err: any) {
      console.error("❌ Error submitting project plan:", err?.response?.data || err.message);
      alert("Error submitting project plan. Check console for details.");
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h2 className="text-xl font-semibold mb-4">Project Planning</h2>

      {/* OBJECTIVES */}
      <div>
        <h3 className="font-semibold">Objectives & Goals</h3>
        <button type="button" onClick={addObjective} className="text-sm text-blue-600 mt-2">
          + Add Objective
        </button>
        {objectives.map((obj, objIndex) => (
          <div key={objIndex} className="mt-4 border p-4 rounded-md bg-gray-50">
            <input
              type="text"
              placeholder="Objective title"
              value={obj.title}
              onChange={(e) => {
                const updated = [...objectives];
                updated[objIndex].title = e.target.value;
                setObjectives(updated);
              }}
              className="w-full p-2 border rounded mb-2"
            />
            <button
              type="button"
              onClick={() => addGoal(objIndex)}
              className="text-sm text-green-600"
            >
              + Add Goal
            </button>
            {obj.goals.map((goal, goalIndex) => (
              <div key={goalIndex} className="mt-2 grid grid-cols-2 gap-2">
                <input
                  type="text"
                  placeholder="Goal title"
                  value={goal.title}
                  onChange={(e) => {
                    const updated = [...objectives];
                    updated[objIndex].goals[goalIndex].title = e.target.value;
                    setObjectives(updated);
                  }}
                  className="p-2 border rounded"
                />
                <input
                  type="text"
                  placeholder="Target"
                  value={goal.target}
                  onChange={(e) => {
                    const updated = [...objectives];
                    updated[objIndex].goals[goalIndex].target = e.target.value;
                    setObjectives(updated);
                  }}
                  className="p-2 border rounded"
                />
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* EXPECTED OUTCOME */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Expected Outcome</label>
        <textarea
          rows={3}
          value={expectedOutcome}
          onChange={(e) => setExpectedOutcome(e.target.value)}
          className="mt-1 block w-full rounded border p-2"
        />
      </div>

      {/* INPUTS */}
      <div>
        <label className="block font-semibold">Inputs</label>
        {inputs.map((input, index) => (
          <div key={index} className="grid grid-cols-2 gap-2 mt-2">
            <input
              type="text"
              placeholder="Input name"
              value={input.name}
              onChange={(e) => {
                const updated = [...inputs];
                updated[index].name = e.target.value;
                setInputs(updated);
              }}
              className="p-2 border rounded"
            />
            <input
              type="number"
              placeholder="Quantity"
              value={input.quantity}
              onChange={(e) => {
                const updated = [...inputs];
                updated[index].quantity = +e.target.value;
                setInputs(updated);
              }}
              className="p-2 border rounded"
            />
          </div>
        ))}
        <button
          type="button"
          onClick={() => setInputs([...inputs, { name: "", quantity: 0 }])}
          className="text-sm text-blue-600 mt-2"
        >
          + Add Input
        </button>
      </div>

      {/* OUTPUTS */}
      <div>
        <label className="block font-semibold">Outputs</label>
        {outputs.map((output, index) => (
          <div key={index} className="grid grid-cols-2 gap-2 mt-2">
            <input
              type="text"
              placeholder="Output name"
              value={output.name}
              onChange={(e) => {
                const updated = [...outputs];
                updated[index].name = e.target.value;
                setOutputs(updated);
              }}
              className="p-2 border rounded"
            />
            <input
              type="number"
              placeholder="Value"
              value={output.value}
              onChange={(e) => {
                const updated = [...outputs];
                updated[index].value = +e.target.value;
                setOutputs(updated);
              }}
              className="p-2 border rounded"
            />
          </div>
        ))}
        <button
          type="button"
          onClick={() => setOutputs([...outputs, { name: "", value: 0 }])}
          className="text-sm text-blue-600 mt-2"
        >
          + Add Output
        </button>
      </div>

      {/* SUBMIT */}
      <div className="flex justify-end">
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Add Project Plan
        </button>
      </div>
    </form>
  );
}
