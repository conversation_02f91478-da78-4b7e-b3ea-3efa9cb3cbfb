"use client";

import React, { useState, useEffect } from "react";
import { api } from "@/services/api";

interface Activity {
  _id: string;
  title: string;
  description: string;
  status: "pending" | "inprogress" | "completed";
  priority: "high" | "medium" | "low";
  project?: {
    _id: string;
    title: string;
  };
  assignedTo?: {
    _id: string;
    fullName: string;
    email: string;
  };
  assignedBy?: {
    _id: string;
    fullName: string;
    email: string;
  };
  startDate?: string;
  endDate?: string;
  budget: number;
  location?: string;
  targetOutcome?: string;
  assignmentDate?: string;
  createdAt: string;
}

interface Project {
  id: string;
  title: string;
  status: string;
}

interface ProjectActivitiesData {
  project: Project;
  activities: Activity[];
}

export default function ActivitiesManagementPage() {
  const [projectsData, setProjectsData] = useState<ProjectActivitiesData[]>([]);
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [fieldOfficers, setFieldOfficers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showActivityModal, setShowActivityModal] = useState(false);
  const [selectedActivityForView, setSelectedActivityForView] = useState<Activity | null>(null);

  const [assignmentForm, setAssignmentForm] = useState({
    assignedTo: "",
    startDate: "",
    endDate: "",
    budget: 0,
    location: "",
    targetOutcome: "",
    kpis: [{ name: "", target: "", description: "" }]
  });

  useEffect(() => {
    fetchProjectsAndActivities();
  }, []);

  const fetchProjectsAndActivities = async () => {
    try {
      setLoading(true);

      // Get all projects assigned to this project manager
      const projectsRes = await api.get("/api/v1/project");

      // Handle different response structures
      const projectsArray = projectsRes.data?.projects || projectsRes.data || [];

      if (!Array.isArray(projectsArray)) {
        setProjectsData([]);
        return;
      }

      const assignedProjects = projectsArray.filter((p: any) => {
        return p.assignedTo && p.assignedTo._id === getCurrentUserId();
      });

      // Fetch activities for each project
      const projectsWithActivities = await Promise.all(
        assignedProjects.map(async (project: any) => {
          try {
            const activitiesRes = await api.get(`/api/v1/project/${project._id}/activities`);

            // Handle different response structures
            const activitiesData = activitiesRes.data?.data || activitiesRes.data;

            return {
              project: {
                id: project._id,
                title: project.title,
                status: project.status
              },
              activities: activitiesData?.activities || []
            };
          } catch (error: any) {
            return {
              project: {
                id: project._id,
                title: project.title,
                status: project.status
              },
              activities: []
            };
          }
        })
      );

      setProjectsData(projectsWithActivities);

      if (projectsWithActivities.length > 0) {
        setSelectedProject(projectsWithActivities[0].project.id);
      }
    } catch (error: any) {
      setProjectsData([]);
    } finally {
      setLoading(false);
    }
  };

  // Debug function to check teams for a project
  const debugTeamsForProject = async (projectId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:7000/api/v1/team/manager/project/${projectId}/teams-debug`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
      } else {
        const errorData = await response.text();
      }
    } catch (error) {
      // Silent error handling
    }
  };

  // Fetch field officers from team for a specific project
  const fetchFieldOfficersForProject = async (projectId: string) => {
    try {
      console.log('🔍 Fetching field officers for project:', projectId);
      const response = await api.getTeamFieldOfficersForProject(projectId);
      console.log('📋 API response:', response);

      // Handle different response structures
      const officers = response.data?.data?.fieldOfficers || response.data?.fieldOfficers || [];
      console.log('👥 Field officers found:', officers);

      if (Array.isArray(officers)) {
        setFieldOfficers(officers);
      } else {
        setFieldOfficers([]);
      }
    } catch (error: any) {
      console.error('❌ Error fetching field officers:', error);
      console.error('❌ Error response:', error?.response?.data);

      // If no team exists for the project, show a helpful message
      if (error?.response?.status === 404) {
        setFieldOfficers([]);
        return;
      }

      setFieldOfficers([]);
    }
  };

  const getCurrentUserId = () => {
    // This should come from your auth context/state
    const userId = localStorage.getItem('userId') || localStorage.getItem('user_id') || '';
    return userId;
  };

  const handleAssignActivity = async (activity: Activity, projectId?: string) => {
    setSelectedActivity(activity);
    setAssignmentForm({
      assignedTo: activity.assignedTo?._id || "",
      startDate: activity.startDate ? activity.startDate.split('T')[0] : "",
      endDate: activity.endDate ? activity.endDate.split('T')[0] : "",
      budget: activity.budget || 0,
      location: activity.location || "",
      targetOutcome: activity.targetOutcome || "",
      kpis: (activity as any).kpis && (activity as any).kpis.length > 0
        ? (activity as any).kpis
        : [{ name: "", target: "", description: "" }]
    });

    // Fetch field officers from the team for this project
    const activityProjectId = projectId || activity.project?._id;
    if (activityProjectId) {
      await fetchFieldOfficersForProject(activityProjectId);
    } else {
      setFieldOfficers([]);
    }

    setShowAssignModal(true);
  };

  const handleViewActivity = (activity: Activity) => {
    setSelectedActivityForView(activity);
    setShowActivityModal(true);
  };

  // KPI management functions
  const addKPI = () => {
    setAssignmentForm({
      ...assignmentForm,
      kpis: [...assignmentForm.kpis, { name: "", target: "", description: "" }]
    });
  };

  const removeKPI = (index: number) => {
    const newKpis = assignmentForm.kpis.filter((_, i) => i !== index);
    setAssignmentForm({
      ...assignmentForm,
      kpis: newKpis.length > 0 ? newKpis : [{ name: "", target: "", description: "" }]
    });
  };

  const updateKPI = (index: number, field: string, value: string) => {
    const newKpis = [...assignmentForm.kpis];
    newKpis[index] = { ...newKpis[index], [field]: value };
    setAssignmentForm({
      ...assignmentForm,
      kpis: newKpis
    });
  };

  const submitAssignment = async () => {
    if (!selectedActivity) {
      alert('No activity selected');
      return;
    }

    try {
      // Filter out empty KPIs before submission
      const filteredKpis = assignmentForm.kpis.filter(kpi => kpi.name.trim() && kpi.target.trim());

      // Validate KPIs if any are provided
      for (const kpi of filteredKpis) {
        if (kpi.name.length > 200) {
          alert('KPI name cannot exceed 200 characters');
          return;
        }
        if (kpi.target.length > 500) {
          alert('KPI target cannot exceed 500 characters');
          return;
        }
        if (kpi.description && kpi.description.length > 1000) {
          alert('KPI description cannot exceed 1000 characters');
          return;
        }
      }

      const submissionData = {
        ...assignmentForm,
        kpis: filteredKpis
      };

      // Validate required fields before sending
      if (!submissionData.assignedTo) {
        alert('Please select a field officer');
        return;
      }

      if (!submissionData.startDate || !submissionData.endDate) {
        alert('Please select start and end dates');
        return;
      }

      // Validate dates
      const startDate = new Date(submissionData.startDate);
      const endDate = new Date(submissionData.endDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (startDate < today) {
        alert('Start date cannot be in the past');
        return;
      }

      if (endDate <= startDate) {
        alert('End date must be after start date');
        return;
      }

      const response = await api.put(`/api/v1/project/activity/${selectedActivity._id}/assign`, submissionData);

      // Refresh activities
      await fetchProjectsAndActivities();

      setShowAssignModal(false);
      setSelectedActivity(null);
      alert("Activity assigned successfully!");
    } catch (error: any) {

      // Enhanced error message extraction
      let errorMessage = "Failed to assign activity";
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.error) {
        errorMessage = typeof error.response.data.error === 'string'
          ? error.response.data.error
          : JSON.stringify(error.response.data.error);
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show detailed error information
      const status = error?.response?.status || 'Unknown';
      const fullErrorMessage = `${errorMessage}\n\nStatus: ${status}\nPlease check the console for more details.`;
      alert(fullErrorMessage);
    }
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'inprogress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const selectedProjectData = projectsData.find(p => p.project.id === selectedProject);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading activities...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Activity Management</h1>
          <p className="text-gray-600">Assign and manage project activities for field officers</p>
        </div>

        {/* No Projects State */}
        {!loading && projectsData.length === 0 && (
          <div className="bg-white rounded-lg shadow-md p-12 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Assigned</h3>
            <p className="text-gray-600 mb-4">You don't have any projects assigned to you yet.</p>
            <p className="text-sm text-gray-500">Contact your senior manager to get projects assigned.</p>
          </div>
        )}

        {/* Project Selection - Only show if there are projects */}
        {!loading && projectsData.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Select Project</h2>
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a project...</option>
              {projectsData.map((projectData) => (
                <option key={projectData.project.id} value={projectData.project.id}>
                  {projectData.project.title} ({projectData.activities.length} activities)
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Activities Table */}
        {!loading && projectsData.length > 0 && selectedProjectData && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">
                  Activities for {selectedProjectData.project.title}
                </h2>
                <span className="text-sm text-gray-500">
                  {selectedProjectData.activities.length} activities
                </span>
              </div>
            </div>

            {selectedProjectData.activities.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Activities Found</h3>
                <p className="text-gray-600">This project doesn't have any activities yet.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Activity Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assigned To
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Budget
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {selectedProjectData.activities.map((activity) => (
                      <tr 
                        key={activity._id} 
                        className="hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleViewActivity(activity)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{activity.title}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-500 max-w-xs">{activity.description}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(activity.status)}`}>
                            {activity.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {activity.assignedTo ? (
                            <div>
                              <div className="font-medium">{activity.assignedTo.fullName}</div>
                              <div className="text-gray-500">{activity.assignedTo.email}</div>
                            </div>
                          ) : (
                            <span className="text-gray-400 italic">Unassigned</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {activity.budget ? `MWK ${activity.budget.toLocaleString()}` : 'Not set'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAssignActivity(activity, selectedProjectData.project.id);
                            }}
                            className="text-green-600 hover:text-green-900 mr-4"
                          >
                            {activity.assignedTo ? 'Update' : 'Assign'}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Activity Details Modal */}
        {showActivityModal && selectedActivityForView && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto mx-4">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900">
                  {selectedActivityForView.title}
                </h3>
                <button
                  onClick={() => setShowActivityModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Description</h4>
                    <p className="mt-1 text-gray-900">{selectedActivityForView.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Status</h4>
                      <span className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedActivityForView.status)}`}>
                        {selectedActivityForView.status}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Priority</h4>
                      <span className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(selectedActivityForView.priority)}`}>
                        {selectedActivityForView.priority}
                      </span>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Budget</h4>
                    <p className="mt-1 text-lg font-semibold text-gray-900">
                      {selectedActivityForView.budget ? `MWK ${selectedActivityForView.budget.toLocaleString()}` : 'Not allocated'}
                    </p>
                  </div>

                  {selectedActivityForView.location && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Location</h4>
                      <p className="mt-1 text-gray-900">{selectedActivityForView.location}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {selectedActivityForView.assignedTo && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Assigned To</h4>
                      <div className="mt-1">
                        <p className="font-medium text-gray-900">{selectedActivityForView.assignedTo.fullName}</p>
                        <p className="text-gray-600">{selectedActivityForView.assignedTo.email}</p>
                      </div>
                    </div>
                  )}

                  {selectedActivityForView.assignedBy && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Assigned By</h4>
                      <div className="mt-1">
                        <p className="font-medium text-gray-900">{selectedActivityForView.assignedBy.fullName}</p>
                        <p className="text-gray-600">{selectedActivityForView.assignedBy.email}</p>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 gap-4">
                    {selectedActivityForView.startDate && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Start Date</h4>
                        <p className="mt-1 text-gray-900">{new Date(selectedActivityForView.startDate).toLocaleDateString()}</p>
                      </div>
                    )}
                    {selectedActivityForView.endDate && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">End Date</h4>
                        <p className="mt-1 text-gray-900">{new Date(selectedActivityForView.endDate).toLocaleDateString()}</p>
                      </div>
                    )}
                  </div>

                  {selectedActivityForView.targetOutcome && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Target Outcome</h4>
                      <p className="mt-1 text-gray-900">{selectedActivityForView.targetOutcome}</p>
                    </div>
                  )}

                  <div>
                    <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Created</h4>
                    <p className="mt-1 text-gray-900">{new Date(selectedActivityForView.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4 mt-8 pt-6 border-t">
                <button
                  onClick={() => setShowActivityModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setShowActivityModal(false);
                    handleAssignActivity(selectedActivityForView, selectedProjectData?.project.id);
                  }}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700"
                >
                  {selectedActivityForView.assignedTo ? 'Update Assignment' : 'Assign Activity'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Assignment Modal */}
        {showAssignModal && selectedActivity && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">
                  {selectedActivity.assignedTo ? 'Update' : 'Assign'} Activity: {selectedActivity.title}
                </h3>
                <button
                  onClick={() => setShowAssignModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Assign to Field Officer <span className="text-red-500">*</span>
                  </label>
                  {fieldOfficers.length === 0 ? (
                    <div className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50">
                      <div className="text-center text-gray-500">
                        <p className="text-sm">No team found for this project.</p>
                        <p className="text-xs mt-1 mb-3">
                          Please create a team for this project first in the Teams section.
                        </p>
                        <button
                          onClick={() => window.open('/manager/teams', '_blank')}
                          className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
                        >
                          Go to Teams
                        </button>
                      </div>
                    </div>
                  ) : (
                    <select
                      value={assignmentForm.assignedTo}
                      onChange={(e) => setAssignmentForm({...assignmentForm, assignedTo: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    >
                      <option value="">Select field officer from team...</option>
                      {fieldOfficers.map((officer) => (
                        <option key={officer._id} value={officer._id}>
                          {officer.fullName} ({officer.email})
                        </option>
                      ))}
                    </select>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Start Date <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={assignmentForm.startDate}
                      onChange={(e) => setAssignmentForm({...assignmentForm, startDate: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      End Date <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={assignmentForm.endDate}
                      onChange={(e) => setAssignmentForm({...assignmentForm, endDate: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Budget (MK)
                    </label>
                    <input
                      type="number"
                      value={assignmentForm.budget}
                      onChange={(e) => setAssignmentForm({...assignmentForm, budget: parseFloat(e.target.value) || 0})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="0"
                      step="0.01"
                    />
                  </div>


                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    value={assignmentForm.location}
                    onChange={(e) => setAssignmentForm({...assignmentForm, location: e.target.value})}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter activity location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Target Outcome
                  </label>
                  <textarea
                    value={assignmentForm.targetOutcome}
                    onChange={(e) => setAssignmentForm({...assignmentForm, targetOutcome: e.target.value})}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24 resize-none"
                    placeholder="Describe the expected outcome of this activity"
                  />
                </div>
              </div>

              {/* KPIs Section */}
              <div className="mt-6">
                <div className="flex items-center justify-between mb-4">
                  <label className="block text-sm font-semibold text-gray-700">
                    Key Performance Indicators (KPIs)
                  </label>
                  <button
                    type="button"
                    onClick={addKPI}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    + Add KPI
                  </button>
                </div>

                <div className="space-y-4">
                  {assignmentForm.kpis.map((kpi, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-sm font-medium text-gray-700">KPI {index + 1}</h4>
                        {assignmentForm.kpis.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeKPI(index)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            KPI Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={kpi.name}
                            onChange={(e) => updateKPI(index, 'name', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                            placeholder="e.g., Number of beneficiaries reached"
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Target <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={kpi.target}
                            onChange={(e) => updateKPI(index, 'target', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                            placeholder="e.g., 500 people"
                          />
                        </div>
                      </div>

                      <div className="mt-3">
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Description (Optional)
                        </label>
                        <textarea
                          value={kpi.description}
                          onChange={(e) => updateKPI(index, 'description', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm h-16 resize-none"
                          placeholder="Additional details about this KPI..."
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-4 mt-6">
                <button
                  onClick={() => setShowAssignModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={submitAssignment}
                  disabled={fieldOfficers.length === 0 || !assignmentForm.assignedTo}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {selectedActivity?.assignedTo ? 'Update Assignment' : 'Assign Activity'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

