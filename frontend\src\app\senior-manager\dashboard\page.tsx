"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  HomeIcon,
  ClipboardIcon,
  UserCircleIcon,
  CogIcon,
  Bars3Icon,
  CheckIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import SummaryCards from "../components/SummaryCards";
import VisualizationSection from "../components/VisualizationSection";
import api from "@/utils/api";
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';

ChartJS.register(
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

export default function SeniorManagerDashboard() {
  const router = useRouter();

  // State for real data
  const [stats, setStats] = useState({
    totalProjects: 0,
    completedProjects: 0,
    ongoingProjects: 0,
  });
  const [loading, setLoading] = useState(true);
  const [recentProjects, setRecentProjects] = useState<any[]>([]);
  const [allProjects, setAllProjects] = useState<any[]>([]);
  const [projectProgressData, setProjectProgressData] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Debug: Check if user is logged in and has token
        const token = localStorage.getItem('token');
        const userRole = localStorage.getItem('userRole');
        console.log('🔐 Debug - Token exists:', !!token);
        console.log('🔐 Debug - User role:', userRole);

        if (!token) {
          console.error('❌ No token found, redirecting to login');
          router.push('/login');
          return;
        }

        // Test backend connection first
        console.log('🔗 Testing backend connection...');
        try {
          const healthRes = await api.get('/health');
          console.log('✅ Backend is reachable:', healthRes.data);
        } catch (healthError) {
          console.error('❌ Backend health check failed:', healthError);
          setConnectionError("Backend server is not reachable. Please ensure the server is running at localhost:7000");
          return;
        }

        // Test auth endpoint
        console.log('🔐 Testing auth endpoint...');
        try {
          const authTestRes = await api.get('/api/v1/auth/test');
          console.log('✅ Auth test successful:', authTestRes.data);
        } catch (authError) {
          console.error('❌ Auth test failed:', authError);
          if (authError.response?.status === 401) {
            console.error('🔐 Token is invalid or expired');
            setConnectionError("Authentication failed. Please log in again.");
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('userId');
            localStorage.removeItem('userRole');
            setTimeout(() => router.push('/login'), 2000);
            return;
          }
        }

        // Fetch dashboard stats
        console.log('📊 Fetching senior manager dashboard stats...');
        const statsRes = await api.get(
          "/api/v1/senior-manager/dashboard/stats"
        );
        // Handle different response formats
        let statsData: any;
        if (statsRes.data && statsRes.data.status === 'success') {
          statsData = statsRes.data.data;
        } else if (statsRes.data) {
          // If response has data but no status, use it directly
          statsData = statsRes.data;
        } else {
          statsData = {
            totalProjects: 0,
            completedProjects: 0,
            ongoingProjects: 0,
            teamMembers: 0,
          };
        }

        // Ensure we have the right data structure
        const finalStats = {
          totalProjects: statsData.totalProjects || 0,
          completedProjects: statsData.completedProjects || 0,
          ongoingProjects: statsData.ongoingProjects || 0,
        };

        setStats(finalStats);

        // Handle other data - updated for new response structure
        if (statsRes.data && typeof statsRes.data === 'object' && !Array.isArray(statsRes.data)) {
          // Check if data is nested in statsRes.data.data
          const responseData = statsRes.data.data || statsRes.data;

          console.log('📊 Dashboard data received:', {
            recentProjects: responseData.recentProjects?.length || 0,
            projectProgress: responseData.projectProgress?.length || 0,
            recentActivities: responseData.recentActivities?.length || 0
          });

          console.log('📊 Project progress data:', responseData.projectProgress);

          setRecentProjects(responseData.recentProjects || []);
          setProjectProgressData(responseData.projectProgress || []);
          setRecentActivities(responseData.recentActivities || []);
        }

        // Fetch all projects separately for the modal functionality
        try {
          console.log('📊 Fetching all projects for modals...');
          const allProjectsRes = await api.get('/api/v1/senior-manager/projects');
          const allProjectsData = allProjectsRes.data?.data || allProjectsRes.data || [];
          console.log('📊 All projects fetched:', allProjectsData.length);
          setAllProjects(allProjectsData);
        } catch (projectsError) {
          console.warn('⚠️ Could not fetch all projects for modals:', projectsError);
          // Try alternative endpoint
          try {
            const altProjectsRes = await api.get('/api/v1/projects');
            const altProjectsData = altProjectsRes.data?.data || altProjectsRes.data || [];
            console.log('📊 All projects fetched from alternative endpoint:', altProjectsData.length);
            setAllProjects(altProjectsData);
          } catch (altError) {
            console.warn('⚠️ Alternative projects endpoint also failed:', altError);
            setAllProjects([]);
          }
        }


      } catch (error: any) {
        console.error("❌ Failed to fetch dashboard data:", error);
        console.error("❌ Error details:", {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          code: error.code,
          message: error.message
        });

        // Check if it's a 401 authentication error
        if (error.response?.status === 401) {
          console.error('🔐 Authentication failed - token may be invalid or expired');
          setConnectionError("Authentication failed. Please log in again.");
          // Clear stored auth data
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('userId');
          localStorage.removeItem('userRole');
          // Redirect to login
          setTimeout(() => router.push('/login'), 2000);
        }
        // Check if it's a connection error
        else if (error.code === 'ERR_NETWORK' || error.code === 'ERR_CONNECTION_REFUSED' || error.message?.includes('Network Error')) {
          setConnectionError("Backend server is not running. Please start the server at localhost:7000");
        } else {
          setConnectionError("Failed to load dashboard data. Please check your connection.");
        }

        // Set empty state instead of fallback data
        setStats({
          totalProjects: 0,
          completedProjects: 0,
          ongoingProjects: 0,
        });
        setRecentProjects([]);
        setAllProjects([]);
        setProjectProgressData([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);


  

 



  return (
    <div className="min-h-screen bg-[#f4f6fa] p-2 max-w-6xl mx-auto">
      {/* Connection Error Banner */}
      {connectionError && (
        <div className="mb-2 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Connection Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{connectionError}</p>
                <p className="mt-1">
                  <strong>To fix this:</strong>
                  <br />
                  1. Open terminal and navigate to backend-main directory
                  <br />
                  2. Run: <code className="bg-red-100 px-1 rounded">npm start</code>
                  <br />
                  3. Refresh this page once the server is running
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="mb-1">
        <SummaryCards
          totalProjects={stats.totalProjects || 0}
          completedProjects={stats.completedProjects || 0}
          ongoingProjects={stats.ongoingProjects || 0}
          allProjects={allProjects}
        />
      </div>
      {/* Main Grid */}
      <div className="grid grid-cols-1 gap-1">
        {/* Full Width: Project Progress + Recent Projects */}
        <div className="flex flex-col gap-2">
          {/* Project Progress Chart */}
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-gray-900">Project Progress</h3>
              <span className="text-sm text-gray-500">Activity Completion</span>
            </div>
            <div className="h-64">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : projectProgressData.length > 0 ? (
                <Bar
                  data={{
                    labels: projectProgressData.map(proj =>
                      (proj.title || proj.name || 'Unnamed Project').length > 15
                        ? (proj.title || proj.name || 'Unnamed Project').substring(0, 15) + '...'
                        : (proj.title || proj.name || 'Unnamed Project')
                    ),
                    datasets: [
                      {
                        label: 'Progress (%)',
                        data: projectProgressData.map(proj => proj.progress || 0),
                        backgroundColor: projectProgressData.map(proj => {
                          const progress = proj.progress || 0;
                          // Use progress-based colors for in-progress projects
                          return progress >= 80 ? '#3B82F6' :  // Blue for high progress
                                 progress >= 50 ? '#F59E0B' :  // Orange for medium progress
                                 '#EF4444';                    // Red for low progress
                        }),
                        borderColor: projectProgressData.map(proj => {
                          const progress = proj.progress || 0;
                          // Use progress-based border colors for in-progress projects
                          return progress >= 80 ? '#1D4ED8' :  // Dark blue for high progress
                                 progress >= 50 ? '#D97706' :  // Dark orange for medium progress
                                 '#DC2626';                    // Dark red for low progress
                        }),
                        borderWidth: 1,
                        borderRadius: 4,
                      }
                    ]
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        callbacks: {
                          title: (context) => {
                            const index = context[0].dataIndex;
                            return projectProgressData[index]?.title || projectProgressData[index]?.name || 'Project';
                          },
                          label: (context) => {
                            return `Progress: ${context.parsed.y}%`;
                          }
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                          callback: function(value) {
                            return value + '%';
                          }
                        },
                        grid: {
                          color: 'rgba(0, 0, 0, 0.1)',
                        }
                      },
                      x: {
                        grid: {
                          display: false,
                        }
                      }
                    },
                    interaction: {
                      intersect: false,
                      mode: 'index',
                    },
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p>No project progress data available</p>
                  </div>
                </div>
              )}
            </div>
          </div>



          {/* Recent Projects Table */}
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <div className="font-semibold text-gray-900">Recent Projects</div>
              <button
                className="flex items-center gap-1 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg text-sm font-semibold shadow transition"
                onClick={() => router.push("/senior-manager/projects")}
              >
                + Add Project
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="text-gray-500 text-xs uppercase">
                    <th className="px-4 py-2 text-left">Project Name</th>
                    <th className="px-4 py-2 text-left">Assigned To</th>
                    <th className="px-4 py-2 text-left">Progress</th>
                    <th className="px-4 py-2 text-left">Status</th>
                    <th className="px-4 py-2 text-left">Timeline</th>
                  </tr>
                </thead>
                <tbody>
  {loading ? (
    <tr>
      <td colSpan={5} className="text-center py-4 text-gray-400">
        Loading...
      </td>
    </tr>
  ) : recentProjects && recentProjects.length > 0 ? (
    recentProjects.map((project) => (
      <tr key={project._id || project.id}>
        <td className="px-4 py-2 font-medium text-gray-900">{project.title || project.name}</td>
        <td className="px-4 py-2">
          {project.assignedTo?.fullName || project.assignedTo?.name || 'Unassigned'}
        </td>
        <td className="px-4 py-2">
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  (project.progress || 0) >= 100
                    ? 'bg-green-500'
                    : (project.progress || 0) >= 75
                    ? 'bg-blue-500'
                    : (project.progress || 0) >= 50
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`}
                style={{ width: `${Math.min(project.progress || 0, 100)}%` }}
              ></div>
            </div>
            <span className="text-xs font-medium text-gray-600 min-w-[35px]">
              {project.progress != null ? `${project.progress}%` : '0%'}
            </span>
          </div>
        </td>
        <td className="px-4 py-2 capitalize">
          <span
            className={`px-2 py-1 text-xs rounded-full font-semibold ${
              project.status === 'inprogress'
                ? 'bg-blue-100 text-blue-700'
                : project.status === 'completed'
                ? 'bg-blue-100 text-blue-700'
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {project.status === 'inprogress' ? 'In Progress' :
             project.status === 'completed' ? 'Completed' :
             project.status || 'Unknown'}
          </span>
        </td>
        <td className="px-4 py-2">
          {(() => {
            if (!project.endDate) return 'N/A';

            const endDate = new Date(project.endDate);
            const today = new Date();
            const isCompleted = project.status === 'completed' || (project.progress || 0) >= 100;

            if (isCompleted) {
              // Show completion date for completed projects
              return (
                <div className="text-green-600 font-medium">
                  <div className="text-xs text-gray-500">Completed</div>
                  <div>{endDate.toLocaleDateString()}</div>
                </div>
              );
            } else {
              // Show remaining days for ongoing projects
              const diffTime = endDate.getTime() - today.getTime();
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              if (diffDays < 0) {
                return (
                  <div className="text-red-600 font-medium">
                    <div className="text-xs">Overdue</div>
                    <div>{Math.abs(diffDays)} days late</div>
                  </div>
                );
              } else if (diffDays === 0) {
                return (
                  <div className="text-orange-600 font-medium">
                    <div className="text-xs">Due</div>
                    <div>Today</div>
                  </div>
                );
              } else {
                return (
                  <div className="text-blue-600 font-medium">
                    <div className="text-xs">Remaining</div>
                    <div>{diffDays} days</div>
                  </div>
                );
              }
            }
          })()}
        </td>
      </tr>
    ))
  ) : (
    <tr>
      <td colSpan={5} className="text-center py-8 text-gray-400">
        No projects to display.
      </td>
    </tr>
  )}
</tbody>

              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}