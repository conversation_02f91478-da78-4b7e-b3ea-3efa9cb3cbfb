"use client";

import React, { useState, useEffect } from "react";
import { api } from "@/services/api";
import { toast } from "react-hot-toast";
import {
  UserGroupIcon,
  PlusIcon,
  MapPinIcon,
  UserCircleIcon,
  XMarkIcon,
  CheckIcon,
  CalendarDaysIcon,
  BuildingOfficeIcon,
} from "@heroicons/react/24/outline";

interface Project {
  _id: string;
  title: string;
  description: string;
  location: {
    _id: string;
    name: string;
  };
  startDate: string;
  endDate: string;
}

interface FieldOfficer {
  _id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  district: {
    _id: string;
    name: string;
  };
}

interface Team {
  _id: string;
  name: string;
  description: string;
  project: {
    _id: string;
    title: string;
    location: {
      name: string;
    };
  } | null;
  fieldOfficers: FieldOfficer[];
  createdAt: string;
}

export default function TeamsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [availableOfficers, setAvailableOfficers] = useState<FieldOfficer[]>([]);
  const [selectedOfficers, setSelectedOfficers] = useState<string[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(false);
  const [teamForm, setTeamForm] = useState({
    name: "",
    description: "",
  });

  useEffect(() => {
    checkUserAuth();
    fetchProjects();
    fetchTeams();
  }, []);

  const checkUserAuth = async () => {
    try {
      const token = localStorage.getItem('token');
      const userRole = localStorage.getItem('userRole');
      const userId = localStorage.getItem('userId');

      // Also check with backend
      if (token) {
        const response = await fetch('http://localhost:7000/api/v1/auth/me', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
        }
      }

      // Check what projects exist in the database
      try {
        const allProjectsResponse = await api.getProjects();

        // Check which projects are assigned to current user
        const assignedProjects = allProjectsResponse.data?.filter((p: any) =>
          p.assignedTo === userId || p.assignedTo?._id === userId
        );
      } catch (error) {
        // Error fetching projects
      }
    } catch (error) {
      // Error checking user auth
    }
  };

  const debugFieldOfficers = async (projectId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:7000/api/v1/team/manager/project/${projectId}/debug`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
      } else {
        const errorData = await response.text();
      }
    } catch (error) {
      // Debug field officers fetch error
    }
  };

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await api.getProjectsForTeamCreation();
      console.log("🔍 Full API response:", response);
      console.log("🔍 Projects for team creation:", response.data);
      console.log("🔍 Projects data type:", typeof response.data);
      console.log("🔍 Is array:", Array.isArray(response.data));

      // Handle the API response structure correctly
      // The API returns {status: 'success', data: Array}
      // So we need to access response.data.data for the actual projects array
      let projectsData: any;
      if (response.data && typeof response.data === 'object' && 'data' in response.data) {
        // API returned {status: 'success', data: Array}
        projectsData = response.data.data;
        console.log("🔍 Extracted projects from response.data.data:", projectsData);
      } else if (Array.isArray(response.data)) {
        // API returned Array directly
        projectsData = response.data;
        console.log("🔍 Using response.data directly:", projectsData);
      } else {
        console.warn("⚠️ Unexpected response structure:", response.data);
        projectsData = [];
      }

      if (Array.isArray(projectsData)) {
        console.log("✅ Setting projects array with", projectsData.length, "projects");
        setProjects(projectsData);
      } else {
        console.warn("⚠️ Projects data is not an array:", projectsData);
        setProjects([]);
      }
    } catch (error) {
      console.error("❌ Error fetching projects:", error);
      console.error("❌ Error details:", error.response?.data);
      setProjects([]); // Ensure projects is always an array
      toast.error("Failed to fetch projects");
    } finally {
      setLoading(false);
    }
  };

  const fetchTeams = async () => {
    try {
      const response = await api.getProjectManagerTeams();
      console.log("Project manager teams:", response.data);

      // Handle the API response structure correctly
      let teamsData: any;
      if (response.data && typeof response.data === 'object' && 'data' in response.data) {
        // API returned {status: 'success', data: Array}
        teamsData = response.data.data;
        console.log("🔍 Extracted teams from response.data.data:", teamsData);
      } else if (Array.isArray(response.data)) {
        // API returned Array directly
        teamsData = response.data;
        console.log("🔍 Using response.data directly:", teamsData);
      } else {
        console.warn("⚠️ Unexpected teams response structure:", response.data);
        teamsData = [];
      }

      if (Array.isArray(teamsData)) {
        console.log("✅ Setting teams array with", teamsData.length, "teams");
        setTeams(teamsData);
      } else {
        console.warn("Teams data is not an array:", teamsData);
        setTeams([]);
      }
    } catch (error) {
      console.error("Error fetching teams:", error);
      setTeams([]); // Ensure teams is always an array
      toast.error("Failed to fetch teams");
    }
  };

  const handleProjectSelect = async (project: Project) => {
    try {
      setSelectedProject(project);
      setLoading(true);

      const response = await api.getFieldOfficersByProjectLocation(project._id);
      console.log("🔍 Field officers API response:", response);
      console.log("🔍 Field officers response.data:", response.data);

      // Handle the API response structure correctly
      let fieldOfficersData: any;
      if (response.data && typeof response.data === 'object') {
        if ('data' in response.data && response.data.data?.fieldOfficers) {
          // API returned {status: 'success', data: {project: {...}, fieldOfficers: [...]}}
          fieldOfficersData = response.data.data.fieldOfficers;
          console.log("🔍 Extracted field officers from response.data.data.fieldOfficers:", fieldOfficersData);
        } else if (response.data.fieldOfficers) {
          // API returned {fieldOfficers: [...]}
          fieldOfficersData = response.data.fieldOfficers;
          console.log("🔍 Using response.data.fieldOfficers:", fieldOfficersData);
        } else {
          console.warn("⚠️ Unexpected field officers response structure:", response.data);
          fieldOfficersData = [];
        }
      } else {
        console.warn("⚠️ Unexpected response structure:", response.data);
        fieldOfficersData = [];
      }

      if (Array.isArray(fieldOfficersData)) {
        console.log("✅ Setting field officers array with", fieldOfficersData.length, "officers");
        setAvailableOfficers(fieldOfficersData);
      } else {
        console.warn("Field officers data is not an array:", fieldOfficersData);
        setAvailableOfficers([]);
      }

      setSelectedOfficers([]);
      setTeamForm({
        name: `${project.title} Team`,
        description: `Team for ${project.title} project in ${project.location.name}`,
      });
    } catch (error: any) {
      console.error("❌ Error fetching field officers:", error);
      console.error("❌ Error response:", error.response?.data);
      setAvailableOfficers([]); // Ensure field officers is always an array

      // Show more specific error message
      const errorMessage = error.response?.data?.message || "Failed to fetch field officers for this project";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const toggleOfficerSelection = (officerId: string) => {
    setSelectedOfficers(prev => 
      prev.includes(officerId) 
        ? prev.filter(id => id !== officerId)
        : [...prev, officerId]
    );
  };

  const handleCreateTeam = async () => {
    if (!selectedProject || !teamForm.name.trim() || selectedOfficers.length === 0) {
      toast.error("Please fill in all required fields and select at least one field officer");
      return;
    }

    try {
      setLoading(true);
      
      const teamData = {
        name: teamForm.name.trim(),
        description: teamForm.description.trim(),
        projectId: selectedProject._id,
        fieldOfficers: selectedOfficers,
      };

      await api.createProjectTeam(teamData);
      toast.success("Team created successfully!");
      
      // Reset form and close modal
      setShowCreateModal(false);
      setSelectedProject(null);
      setSelectedOfficers([]);
      setTeamForm({ name: "", description: "" });
      
      // Refresh teams list
      await fetchTeams();
      
    } catch (error: any) {
      console.error("Error creating team:", error);
      const errorMessage = error.response?.data?.message || "Failed to create team";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const closeModal = () => {
    setShowCreateModal(false);
    setSelectedProject(null);
    setSelectedOfficers([]);
    setTeamForm({ name: "", description: "" });
  };

  const handleTeamClick = (team: Team) => {
    setSelectedTeam(team);
    setShowDetailsModal(true);
  };

  const closeDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedTeam(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20 pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Team Management</h1>
              <p className="mt-2 text-gray-600">
                Create and manage teams for your assigned projects
              </p>
            </div>
          </div>
        </div>

        {/* Create Team Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Create Team</h2>
            <button
              onClick={() => setShowCreateModal(true)}
              disabled={projects.length === 0}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              Create New Team
            </button>
          </div>

          {loading && projects.length === 0 ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">Loading projects...</p>
            </div>
          ) : projects.length === 0 ? (
            <div className="text-center py-8">
              <BuildingOfficeIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Available</h3>
              <p className="text-gray-500 mb-4">
                You dont have any active projects assigned to you yet.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left max-w-md mx-auto">
                <h4 className="font-medium text-blue-900 mb-2">To create teams, you need:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Projects assigned to you by a Senior Manager</li>
                  <li>• Projects with status In Progress</li>
                  <li>• Field officers available in the project location</li>
                </ul>
                <p className="text-sm text-blue-700 mt-3">
                  Contact your Senior Manager to assign projects to you.
                </p>
              </div>
              <div className="mt-4 space-x-2">
                <button
                  onClick={checkUserAuth}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                >
                  Check Debug Info (Console)
                </button>
                {projects.length > 0 && (
                  <button
                    onClick={() => debugFieldOfficers(projects[0]._id)}
                    className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm"
                  >
                    Debug Field Officers
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">
                You have {projects.length} project{projects.length !== 1 ? 's' : ''} available for team creation.
              </p>
              <p className="text-sm text-gray-500">
                Click Create New Team to select a project and create a team.
              </p>
            </div>
          )}
        </div>

        {/* Existing Teams Section */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Your Teams ({teams.length})
          </h2>
          
          {teams.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <UserGroupIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Teams Created</h3>
              <p className="text-gray-500">
                Create your first team by selecting a project above.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {teams.map((team) => (
                <div
                  key={team._id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow h-fit max-h-96 overflow-hidden flex flex-col cursor-pointer"
                  onClick={() => handleTeamClick(team)}
                >
                  {/* Header Section */}
                  <div className="flex items-start justify-between mb-4 flex-shrink-0">
                    <div className="flex items-center flex-1 min-w-0">
                      <UserGroupIcon className="w-8 h-8 text-blue-600 mr-3 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <h3 className="font-semibold text-lg text-gray-900 truncate" title={team.name}>
                          {team.name}
                        </h3>
                        <p className={`text-sm truncate ${team.project ? 'text-gray-600' : 'text-orange-600 italic'}`} title={team.project?.title || 'No project assigned'}>
                          {team.project?.title || 'No project assigned'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 flex-shrink-0 ml-2">
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        {team.fieldOfficers.length} members
                      </span>
                      {!team.project && (
                        <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
                          No Project
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="space-y-2 mb-4 flex-shrink-0">
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPinIcon className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="truncate">{team.project?.location?.name || 'No location specified'}</span>
                    </div>
                    {team.description && (
                      <p className="text-sm text-gray-600 line-clamp-2" title={team.description}>
                        {team.description}
                      </p>
                    )}
                  </div>

                  {/* Team Members Section */}
                  <div className="border-t border-gray-100 pt-3 flex-1 min-h-0">
                    <p className="text-xs text-gray-500 mb-2 flex-shrink-0">Team Members:</p>
                    <div className="space-y-1 overflow-y-auto max-h-24">
                      {team.fieldOfficers.slice(0, 3).map((officer) => (
                        <div key={officer._id} className="flex items-center text-sm min-w-0">
                          <UserCircleIcon className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
                          <span className="text-gray-700 truncate" title={officer.fullName}>
                            {officer.fullName}
                          </span>
                        </div>
                      ))}
                      {team.fieldOfficers.length > 3 && (
                        <p className="text-xs text-gray-500 ml-6 flex-shrink-0">
                          +{team.fieldOfficers.length - 3} more members
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create Team Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Create New Team
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Select a project and assign field officers to create a team
                </p>
              </div>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6 space-y-6">
              {/* Project Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Select Project <span className="text-red-500">*</span>
                </label>
                <select
                  value={selectedProject?._id || ''}
                  onChange={(e) => {
                    const project = projects.find(p => p._id === e.target.value);
                    if (project) {
                      handleProjectSelect(project);
                    }
                  }}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Choose a project...</option>
                  {projects.filter(project => !teams.some(team => team.project?._id === project._id)).map((project) => (
                    <option key={project._id} value={project._id}>
                      {project.title} - {project.location?.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Team Details */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Team Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={teamForm.name}
                    onChange={(e) => setTeamForm({ ...teamForm, name: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter team name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={teamForm.description}
                    onChange={(e) => setTeamForm({ ...teamForm, description: e.target.value })}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter team description"
                  />
                </div>
              </div>

              {/* Field Officers Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Select Field Officers <span className="text-red-500">*</span>
                </label>
                {selectedProject ? (
                  <p className="text-sm text-gray-600 mb-3">
                    Available field officers in {selectedProject.location.name}:
                  </p>
                ) : (
                  <p className="text-sm text-gray-600 mb-3">
                    Please select a project first to see available field officers.
                  </p>
                )}

                {!selectedProject ? (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <UserCircleIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">
                      Select a project to view available field officers
                    </p>
                  </div>
                ) : availableOfficers.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <UserCircleIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">
                      No field officers available in {selectedProject.location.name}
                    </p>
                  </div>
                ) : (
                  <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-lg">
                    {availableOfficers.map((officer) => (
                      <div
                        key={officer._id}
                        className={`p-3 border-b border-gray-100 last:border-b-0 cursor-pointer hover:bg-gray-50 transition-colors ${
                          selectedOfficers.includes(officer._id) ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                        onClick={() => toggleOfficerSelection(officer._id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <UserCircleIcon className="w-8 h-8 text-gray-400 mr-3" />
                            <div>
                              <p className="font-medium text-gray-900">{officer.fullName}</p>
                              <p className="text-sm text-gray-600">{officer.email}</p>
                              <p className="text-sm text-gray-500">{officer.phoneNumber}</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {selectedOfficers.includes(officer._id) && (
                              <CheckIcon className="w-5 h-5 text-blue-600" />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {selectedOfficers.length > 0 && (
                  <p className="text-sm text-blue-600 mt-2">
                    {selectedOfficers.length} field officer(s) selected
                  </p>
                )}
              </div>
            </div>

            {/* Modal Footer */}
            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={closeModal}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateTeam}
                disabled={loading || !teamForm.name.trim() || selectedOfficers.length === 0}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                Create Team
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Team Details Modal */}
      {showDetailsModal && selectedTeam && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  {selectedTeam.name}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Team Details
                </p>
              </div>
              <button
                onClick={closeDetailsModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Team Information */}
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Team Information</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Team Name</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedTeam.name}</p>
                      </div>

                      {selectedTeam.description && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Description</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedTeam.description}</p>
                        </div>
                      )}

                      <div>
                        <label className="block text-sm font-medium text-gray-700">Created</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {new Date(selectedTeam.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">Total Members</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedTeam.fieldOfficers.length} field officers</p>
                      </div>
                    </div>
                  </div>

                  {/* Project Information */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Project Information</h4>
                    {selectedTeam.project ? (
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Project Title</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedTeam.project.title}</p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">Location</label>
                          <div className="mt-1 flex items-center text-sm text-gray-900">
                            <MapPinIcon className="w-4 h-4 mr-2 text-gray-500" />
                            {selectedTeam.project.location?.name || 'Location not specified'}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="text-gray-400 mb-2">
                          <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <p className="text-gray-500 text-sm">No project assigned to this team</p>
                        <p className="text-gray-400 text-xs mt-1">Assign a project to see project details</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Team Members */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Team Members</h4>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {selectedTeam.fieldOfficers.map((officer, index) => (
                      <div
                        key={officer._id}
                        className="flex items-center p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full mr-3">
                          <span className="text-sm font-medium text-blue-600">
                            {index + 1}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {officer.fullName}
                          </p>
                          <p className="text-sm text-gray-500 truncate">
                            {officer.email}
                          </p>
                          {officer.phoneNumber && (
                            <p className="text-sm text-gray-500">
                              {officer.phoneNumber}
                            </p>
                          )}
                        </div>
                        <UserCircleIcon className="w-8 h-8 text-gray-400" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end p-6 border-t border-gray-200">
              <button
                onClick={closeDetailsModal}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
