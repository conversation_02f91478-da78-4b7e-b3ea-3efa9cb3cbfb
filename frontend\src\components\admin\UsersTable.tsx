import { User } from "@/types/types";
import React, { useState, useEffect } from "react";
import {
  UserCircleIcon,
  UserGroupIcon,
  PhoneIcon,
  MapPinIcon,
  PencilIcon
} from "@heroicons/react/24/outline";
import SimpleEditUserModal from "./SimpleEditUserModal";

const UsersTable = ({
  users = [],
  search = "",
  onUserUpdated,
}: {
  users: User[];
  search: string;
  onUserUpdated?: () => void;
}) => {
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const filteredUsers = users.filter(
    (user) =>
      (user.name &&
        user.name.toLowerCase().includes(search.toLowerCase())) ||
      (user.fullName &&
        user.fullName.toLowerCase().includes(search.toLowerCase())) ||
      (user.email &&
        user.email.toLowerCase().includes(search.toLowerCase()))
  );

  // User data processing
  useEffect(() => {
    // Process user data for display
  }, [users]);

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditingUser(null);
  };

  const handleUserUpdated = () => {
    if (onUserUpdated) {
      onUserUpdated();
    }
    handleCloseEditModal();
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div className="p-3 bg-blue-100 rounded-xl mr-4">
            <UserGroupIcon className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">
              User Management ({filteredUsers.length} users)
            </h3>
            <p className="text-gray-600 text-sm">
              Manage user accounts, roles, and permissions
            </p>
          </div>
        </div>
      </div>

      {/* Table Format */}
      <div className="overflow-x-auto shadow-lg rounded-xl border border-gray-200" style={{scrollbarWidth: 'thin', scrollbarColor: '#10b981 #f3f4f6'}}>
        <table className="min-w-full bg-white">
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
            <tr>
              <th className="px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                User
              </th>
              <th className="px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Role
              </th>
              <th className="px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Contact
              </th>
              <th className="px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Branch
              </th>
              <th className="px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Status
              </th>
              <th className="px-8 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-100">
            {filteredUsers.map((user, index) => {
              // Generate initials for avatar fallback
              const displayName = user.fullName || user.name || user.email;
              const initials = displayName
                ? displayName
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .slice(0, 2)
                    .toUpperCase()
                : "U";

              const userId = user._id || user.id;

              return (
                <tr key={userId || user.email || index} className="hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-50 transition-all duration-200">
                  {/* User Info */}
                  <td className="px-8 py-6 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-semibold text-gray-900">
                          {displayName}
                        </div>
                        <div className="text-sm text-gray-600">{user.email}</div>
                      </div>
                    </div>
                  </td>

                  {/* Role */}
                  <td className="px-8 py-6 whitespace-nowrap">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-green-100 to-green-200 text-green-800 shadow-sm">
                      {typeof user.role === 'object' ? user.role?.name : user.role || user.roleName || 'No Role'}
                    </span>
                  </td>

                  {/* Contact */}
                  <td className="px-8 py-6">
                    <div className="space-y-1">
                      {(user.phoneNumber || user.phone_number) && (
                        <div className="flex items-center text-sm text-gray-700">
                          <PhoneIcon className="w-4 h-4 mr-2 text-green-500" />
                          <span className="font-medium">{user.phoneNumber || user.phone_number}</span>
                        </div>
                      )}
                      {!(user.phoneNumber || user.phone_number) && (
                        <span className="text-sm text-gray-400 italic">No phone</span>
                      )}
                    </div>
                  </td>

                  {/* Branch */}
                  <td className="px-8 py-6">
                    <div className="flex items-center text-sm text-gray-700">
                      {(typeof user.district === 'object' ? user.district?.name : user.district) ? (
                        <>
                          <MapPinIcon className="w-4 h-4 mr-2 text-green-500" />
                          <span className="font-medium">{typeof user.district === 'object' ? user.district?.name : user.district}</span>
                        </>
                      ) : (
                        <span className="text-sm text-gray-400 italic">No branch</span>
                      )}
                    </div>
                  </td>

                  {/* Status */}
                  <td className="px-8 py-6 whitespace-nowrap">
                    <div className="flex flex-col space-y-1">
                      {/* Active Status */}
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm ${
                        (user as any).isOnline
                          ? 'bg-gradient-to-r from-green-100 to-green-200 text-green-800'
                          : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-600'
                      }`}>
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          (user as any).isOnline ? 'bg-green-500' : 'bg-gray-400'
                        }`}></div>
                        {(user as any).isOnline ? 'Active' : 'Inactive'}
                      </span>

                      {/* Last Seen */}
                      {(user as any).lastSeen && !(user as any).isOnline && (
                        <span className="text-xs text-gray-500">
                          Last seen: {new Date((user as any).lastSeen).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </td>

                  {/* Actions */}
                  <td className="px-8 py-6 whitespace-nowrap">
                    <button
                      onClick={() => handleEditUser(user)}
                      className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:text-blue-700 transition-colors duration-200"
                      title="Edit user"
                    >
                      <PencilIcon className="w-4 h-4 mr-1" />
                      Edit
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-16 bg-gray-50 rounded-xl">
          <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <UserCircleIcon className="w-10 h-10 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">No users found</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            {search ? 'Try adjusting your search criteria to find the users you\'re looking for.' : 'Get started by adding your first user to the system.'}
          </p>
        </div>
      )}

      {/* Edit User Modal */}
      {isEditModalOpen && editingUser && (
        <SimpleEditUserModal
          user={editingUser}
          setIsEditUserModalOpen={setIsEditModalOpen}
          callBack={handleUserUpdated}
        />
      )}
    </div>
  );
};

export default UsersTable;
