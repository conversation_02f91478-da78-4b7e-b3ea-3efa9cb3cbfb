'use client';
import { useState, useEffect } from 'react';
import { UserCircleIcon, CheckCircleIcon, XCircleIcon, ClockIcon, EyeIcon, XMarkIcon } from '@heroicons/react/24/solid';
import apiUtil from '@/utils/api';
import { api } from '@/services/api';

interface Project {
  _id: string;
  title: string;
  name: string;
}

interface Report {
  _id: string;
  activity: {
    _id: string;
    title: string;
    location?: string;
  };
  project: {
    _id: string;
    name: string;
    title?: string;
  };
  submittedBy: {
    _id: string;
    fullName: string;
    email?: string;
  };
  reportDate: string;
  location: string;
  content: Array<{
    fieldName: string;
    entry: string;
    required: boolean;
  }>;
  amountSpent: number;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  rejectionReason?: string;
  approvedBy?: {
    _id: string;
    fullName: string;
  };
  approvedAt?: string;
  rejectedBy?: {
    _id: string;
    fullName: string;
  };
  rejectedAt?: string;
}

export default function ReportsPage() {
  const [filter, setFilter] = useState('all');
  const [reports, setReports] = useState<Report[]>([]);
  const [allReports, setAllReports] = useState<Report[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  // Fetch projects and reports on component mount
  useEffect(() => {
    fetchProjects();
    fetchReports();
  }, []);

  const fetchProjects = async () => {
    try {
      const response = await api.getProjects();
      console.log('Projects response:', response);

      if (response.data && Array.isArray(response.data)) {
        setProjects(response.data);
      } else {
        console.error('Invalid projects response format:', response);
        setProjects([]);
      }
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      setProjects([]);
    }
  };

  const fetchReports = async () => {
    setLoading(true);
    try {
      const response = await apiUtil.get('/api/v1/manager/reports');

      if (response.data && response.data.status === 'success') {
        const reportsData = response.data.data || [];
        console.log('📊 Reports data received:', reportsData);
        console.log('📊 Sample report:', reportsData[0]);
        setAllReports(reportsData);
        setReports(reportsData);
      } else {
        console.error('Invalid response format:', response.data);
        setAllReports([]);
        setReports([]);
      }
    } catch (error) {
      console.error('Failed to fetch reports:', error);
      setAllReports([]);
      setReports([]);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (reportId: string) => {
    try {
      const response = await apiUtil.patch(`/api/v1/manager/reports/${reportId}/approve`);

      if (response.data && response.data.status === 'success') {
        // Update the report status in the local state
        setReports(reports.map(report =>
          report._id === reportId ? { ...report, status: 'approved' } : report
        ));
        alert('Report approved successfully!');
      }
    } catch (error) {
      console.error('Failed to approve report:', error);
      alert('Failed to approve report. Please try again.');
    }
  };

  const handleReject = async () => {
    if (!selectedReport || !rejectionReason.trim()) {
      alert('Please provide a reason for rejection.');
      return;
    }

    try {
      const response = await apiUtil.patch(`/api/v1/manager/reports/${selectedReport._id}/reject`, {
        reason: rejectionReason
      });

      if (response.data && response.data.status === 'success') {
        // Update the report status in the local state
        setReports(reports.map(report =>
          report._id === selectedReport._id
            ? { ...report, status: 'rejected', rejectionReason }
            : report
        ));
        alert('Report rejected successfully!');
        setShowRejectModal(false);
        setSelectedReport(null);
        setRejectionReason('');
      }
    } catch (error) {
      console.error('Failed to reject report:', error);
      alert('Failed to reject report. Please try again.');
    }
  };

  const openRejectModal = (report: Report) => {
    setSelectedReport(report);
    setShowRejectModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600';
      case 'rejected':
        return 'text-red-600';
      default:
        return 'text-yellow-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
      case 'rejected':
        return <XCircleIcon className="w-5 h-5 text-red-600" />;
      default:
        return <ClockIcon className="w-5 h-5 text-yellow-600" />;
    }
  };

  // Filter reports by project and status
  useEffect(() => {
    let filtered = allReports;

    // Filter by project
    if (selectedProject !== 'all') {
      filtered = filtered.filter(report =>
        report.project && report.project._id === selectedProject
      );
    }

    // Filter by status
    if (filter !== 'all') {
      filtered = filtered.filter(report => report.status === filter);
    }

    setReports(filtered);
  }, [allReports, selectedProject, filter]);

  const openDetailModal = (report: Report) => {
    console.log('📋 Opening modal for report:', report);
    console.log('📋 Report content:', report.content);
    console.log('📋 Report location:', report.location);
    console.log('📋 Activity location:', report.activity?.location);
    setSelectedReport(report);
    setShowDetailModal(true);
  };

  const closeDetailModal = () => {
    setSelectedReport(null);
    setShowDetailModal(false);
  };

  const filteredReports = reports;

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Reports Management</h1>
        <div className="flex gap-4">
          <select
            value={selectedProject}
            onChange={(e) => setSelectedProject(e.target.value)}
            className="p-2 border rounded-md focus:ring-2 focus:ring-sky-500"
          >
            <option value="all">All Projects</option>
            {projects.map((project) => (
              <option key={project._id} value={project._id}>
                {project.title || project.name}
              </option>
            ))}
          </select>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="p-2 border rounded-md focus:ring-2 focus:ring-sky-500"
          >
            <option value="all">All Reports</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Officer</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                  Loading reports...
                </td>
              </tr>
            ) : filteredReports.length > 0 ? (
              filteredReports.map((report) => (
                <tr key={report._id} className="hover:bg-gray-50 cursor-pointer" onClick={() => openDetailModal(report)}>
                  <td className="px-6 py-4">
                    <div className="font-medium text-gray-900">{report.activity?.title || 'N/A'}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{report.project?.name || report.project?.title || 'N/A'}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <UserCircleIcon className="w-8 h-8 text-gray-400 mr-2" />
                      <span>{report.submittedBy?.fullName || 'N/A'}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">{report.location || report.activity?.location || 'N/A'}</td>
                  <td className="px-6 py-4">
                    {report.reportDate ? new Date(report.reportDate).toLocaleDateString() :
                     report.createdAt ? new Date(report.createdAt).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      {getStatusIcon(report.status)}
                      <span className={`ml-2 ${getStatusColor(report.status)}`}>
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4" onClick={(e) => e.stopPropagation()}>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => openDetailModal(report)}
                        className="text-blue-600 hover:text-blue-800 font-medium flex items-center"
                      >
                        <EyeIcon className="w-4 h-4 mr-1" />
                        View
                      </button>
                      {report.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApprove(report._id)}
                            className="text-green-600 hover:text-green-800 font-medium"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => openRejectModal(report)}
                            className="text-red-600 hover:text-red-800 font-medium"
                          >
                            Reject
                          </button>
                        </>
                      )}
                    </div>
                    {report.status === 'rejected' && report.rejectionReason && (
                      <div className="text-sm text-gray-500 mt-1">
                        Reason: {report.rejectionReason}
                      </div>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                  No reports found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Rejection Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Reject Report
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Please provide a reason for rejecting this report:
              </p>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                rows={4}
                placeholder="Enter rejection reason..."
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setSelectedReport(null);
                    setRejectionReason('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReject}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  Reject Report
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Report Detail Modal */}
      {showDetailModal && selectedReport && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                Activity Report Details
              </h3>
              <button
                onClick={closeDetailModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {/* Report Header */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-3">Report Information</h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-600">Activity:</span>
                      <p className="text-sm text-gray-900">{selectedReport.activity?.title || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Project:</span>
                      <p className="text-sm text-gray-900">{selectedReport.project?.name || selectedReport.project?.title || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Location:</span>
                      <p className="text-sm text-gray-900">{selectedReport.location || selectedReport.activity?.location || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Amount Spent:</span>
                      <p className="text-sm text-gray-900">MWK {selectedReport.amountSpent?.toLocaleString() || '0'}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-3">Submission Details</h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-600">Submitted By:</span>
                      <p className="text-sm text-gray-900">{selectedReport.submittedBy?.fullName || 'N/A'}</p>
                      {selectedReport.submittedBy?.email && (
                        <p className="text-xs text-gray-500">{selectedReport.submittedBy.email}</p>
                      )}
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Submitted On:</span>
                      <p className="text-sm text-gray-900">
                        {selectedReport.createdAt ? new Date(selectedReport.createdAt).toLocaleString() : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Status:</span>
                      <div className="flex items-center mt-1">
                        {getStatusIcon(selectedReport.status)}
                        <span className={`ml-2 text-sm font-medium ${getStatusColor(selectedReport.status)}`}>
                          {selectedReport.status.charAt(0).toUpperCase() + selectedReport.status.slice(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Report Content */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">Report Content</h4>
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  {selectedReport.content && selectedReport.content.length > 0 ? (
                    selectedReport.content.map((item, index) => (
                      <div key={index} className="mb-4 last:mb-0">
                        <div className="flex items-center mb-2">
                          <span className="text-sm font-medium text-gray-700">{item.fieldName || `Field ${index + 1}`}:</span>
                          {item.required && (
                            <span className="ml-2 text-xs text-red-500">*Required</span>
                          )}
                        </div>
                        <div className="bg-gray-50 p-3 rounded border">
                          <p className="text-sm text-gray-900 whitespace-pre-wrap">{item.entry || 'No content provided'}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-gray-500">No report content available</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Approval/Rejection Details */}
              {selectedReport.status === 'approved' && selectedReport.approvedBy && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Approval Details</h4>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <CheckCircleIcon className="w-5 h-5 text-green-600 mr-2" />
                      <div>
                        <p className="text-sm text-green-800">
                          Approved by {selectedReport.approvedBy.fullName}
                        </p>
                        {selectedReport.approvedAt && (
                          <p className="text-xs text-green-600">
                            on {new Date(selectedReport.approvedAt).toLocaleString()}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {selectedReport.status === 'rejected' && selectedReport.rejectionReason && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Rejection Details</h4>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <XCircleIcon className="w-5 h-5 text-red-600 mr-2 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm text-red-800 mb-2">
                          {selectedReport.rejectedBy?.fullName ?
                            `Rejected by ${selectedReport.rejectedBy.fullName}` :
                            'Rejected'
                          }
                        </p>
                        {selectedReport.rejectedAt && (
                          <p className="text-xs text-red-600 mb-2">
                            on {new Date(selectedReport.rejectedAt).toLocaleString()}
                          </p>
                        )}
                        <div className="bg-white p-3 rounded border">
                          <p className="text-sm text-gray-900">{selectedReport.rejectionReason}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Modal Actions */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
              {selectedReport.status === 'pending' && (
                <>
                  <button
                    onClick={() => {
                      closeDetailModal();
                      handleApprove(selectedReport._id);
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Approve Report
                  </button>
                  <button
                    onClick={() => {
                      closeDetailModal();
                      openRejectModal(selectedReport);
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    Reject Report
                  </button>
                </>
              )}
              <button
                onClick={closeDetailModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}