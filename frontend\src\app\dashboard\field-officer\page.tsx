'use client';
import { useState } from 'react';
import {
  HomeIcon,
  CogIcon,
  UserCircleIcon,
  DocumentTextIcon,
  ChartBarIcon,
  BellIcon,
  ClockIcon,
  CalendarIcon,
  ClipboardDocumentListIcon,
  CheckIcon,
  UserGroupIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import dynamic from 'next/dynamic';

// Dynamically import Chart.js components to avoid SSR issues
const Chart = dynamic(() => import('react-chartjs-2').then(mod => mod.Line), { ssr: false });
const DoughnutChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Doughnut), { ssr: false });

export default function FieldOfficerDashboard() {
  const [theme, setTheme] = useState('light');
  const [activeTab, setActiveTab] = useState('home');
  
  return (
    <div className={`min-h-screen flex ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50'}`}>
      {/* Sidebar */}
      <div className="w-64 bg-sky-700 text-white p-4">
        <div className="mb-8">
          <h2 className="text-xl font-bold">Field Officer Portal</h2>
        </div>
        
        <nav className="space-y-2">
          <button 
            onClick={() => setActiveTab('home')}
            className={`flex items-center space-x-2 w-full p-2 rounded ${activeTab === 'home' ? 'bg-sky-800' : 'hover:bg-sky-600'}`}
          >
            <HomeIcon className="w-5 h-5" />
            <span>Home</span>
          </button>
          
          <button 
            onClick={() => setActiveTab('reports')}
            className={`flex items-center space-x-2 w-full p-2 rounded ${activeTab === 'reports' ? 'bg-sky-800' : 'hover:bg-sky-600'}`}
          >
            <DocumentTextIcon className="w-5 h-5" />
            <span>Reports</span>
          </button>
          
          <button 
            onClick={() => setActiveTab('analytics')}
            className={`flex items-center space-x-2 w-full p-2 rounded ${activeTab === 'analytics' ? 'bg-sky-800' : 'hover:bg-sky-600'}`}
          >
            <ChartBarIcon className="w-5 h-5" />
            <span>Analytics</span>
          </button>
          
          <button 
            onClick={() => setActiveTab('settings')}
            className={`flex items-center space-x-2 w-full p-2 rounded ${activeTab === 'settings' ? 'bg-sky-800' : 'hover:bg-sky-600'}`}
          >
            <CogIcon className="w-5 h-5" />
            <span>Settings</span>
          </button>
          
          <button 
            onClick={() => setActiveTab('profile')}
            className={`flex items-center space-x-2 w-full p-2 rounded ${activeTab === 'profile' ? 'bg-sky-800' : 'hover:bg-sky-600'}`}
          >
            <UserCircleIcon className="w-5 h-5" />
            <span>Profile</span>
          </button>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <div className="sticky top-0 z-10 bg-white flex justify-between items-center p-8 pb-6 border-b border-gray-100">
          <h1 className="text-2xl font-bold text-gray-900">Field Officer Dashboard</h1>
          <div className="flex items-center space-x-4">
            <button className="p-2 rounded-full hover:bg-gray-200">
              <BellIcon className="w-6 h-6" />
            </button>
            <div className="relative">
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                2
              </span>
              <button className="p-2 rounded-full hover:bg-gray-200">
                <ClockIcon className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Activity Overview Cards */}
        <div className="px-8 pb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Active Projects</h3>
                <div className="p-2 bg-sky-100 rounded-lg">
                  <ClipboardDocumentListIcon className="w-6 h-6 text-sky-600" />
                </div>
              </div>
              <div className="text-3xl font-bold text-sky-600">8</div>
              <p className="text-gray-600 text-sm mt-2">Currently assigned projects</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Pending Reports</h3>
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <DocumentTextIcon className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
              <div className="text-3xl font-bold text-yellow-600">3</div>
              <p className="text-gray-600 text-sm mt-2">Reports awaiting submission</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Completed Activities</h3>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <CheckIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="text-3xl font-bold text-blue-600">12</div>
              <p className="text-gray-600 text-sm mt-2">Successfully completed tasks</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Team Members</h3>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <UserGroupIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="text-3xl font-bold text-purple-600">5</div>
              <p className="text-gray-600 text-sm mt-2">Active team members</p>
            </div>
          </div>

          {/* Report Form */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 mb-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Submit Activity Report</h2>
              <button className="px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 transition-colors flex items-center gap-2">
                <PlusIcon className="w-5 h-5" />
                New Report
              </button>
            </div>
            <form className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Activity Title</label>
                  <input 
                    type="text" 
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                    placeholder="Enter activity title"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500">
                    <option>Select project</option>
                    <option>Project A</option>
                    <option>Project B</option>
                    <option>Project C</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea 
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  rows={4}
                  placeholder="Enter activity description"
                ></textarea>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Attachments</label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-sky-400 transition-colors">
                  <input type="file" multiple className="hidden" id="file-upload" />
                  <label 
                    htmlFor="file-upload"
                    className="cursor-pointer text-sky-600 hover:text-sky-800 font-medium"
                  >
                    Click to upload or drag and drop files here
                  </label>
                  <p className="text-sm text-gray-500 mt-2">PDF, images, or documents up to 10MB</p>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button 
                  type="button"
                  className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Save Draft
                </button>
                <button 
                  type="submit"
                  className="px-6 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-colors"
                >
                  Submit Report
                </button>
              </div>
            </form>
          </div>

          {/* Activity Timeline */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 mb-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Activity Timeline</h2>
              <div className="flex space-x-2">
                <button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  Sort by Date
                </button>
                <button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  Sort by Project
                </button>
              </div>
            </div>
            
            <div className="space-y-4">
              {/* Timeline Item */}
              <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  <div className="w-4 h-4 rounded-full bg-sky-600 mt-2"></div>
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <h4 className="font-semibold text-gray-900">Field Visit - Project A</h4>
                    <span className="text-sm text-gray-500">2 hours ago</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Completed site inspection and collected data from local stakeholders.
                  </p>
                  <div className="mt-3">
                    <div className="flex justify-between text-sm text-gray-500 mb-1">
                      <span>Progress</span>
                      <span>75%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-sky-600 h-2 rounded-full transition-all duration-300" style={{ width: '75%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Second Timeline Item */}
              <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  <div className="w-4 h-4 rounded-full bg-blue-600 mt-2"></div>
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <h4 className="font-semibold text-gray-900">Data Collection - Project B</h4>
                    <span className="text-sm text-gray-500">1 day ago</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Successfully completed data collection phase for environmental assessment.
                  </p>
                  <div className="mt-3">
                    <div className="flex justify-between text-sm text-gray-500 mb-1">
                      <span>Progress</span>
                      <span>100%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full transition-all duration-300" style={{ width: '100%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Analytics Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Progress</h3>
              <div className="h-64 flex items-center justify-center text-gray-400">
                Chart component will be implemented here
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget Utilization</h3>
              <div className="h-64 flex items-center justify-center text-gray-400">
                Chart component will be implemented here
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 