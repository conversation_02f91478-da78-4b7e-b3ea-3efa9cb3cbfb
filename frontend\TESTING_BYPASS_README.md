# Temporary Authentication Bypass for Testing

## ⚠️ IMPORTANT: This is a temporary testing feature and should be removed before production deployment!

## Overview

This authentication bypass allows frontend testing without requiring backend authentication. It uses predefined test user credentials and simulates the login process entirely on the frontend.

## How to Enable/Disable

### Method 1: Environment Variable (Recommended)
1. Create or edit `.env.local` file in the project root
2. Add: `NEXT_PUBLIC_BYPASS_AUTH=true` to enable bypass
3. Set to `false` or remove the variable to disable bypass

### Method 2: Code Toggle
1. Open `finalyear/src/app/(auth)/login/page.tsx`
2. Find line: `const BYPASS_MODE = process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true' || true;`
3. Change `|| true` to `|| false` to disable bypass

## Test Credentials

When bypass mode is active, you can use any of these email addresses with any password:

- **Admin**: `<EMAIL>`
- **Project Manager**: `<EMAIL>`
- **Field Officer**: `<EMAIL>`
- **Accountant**: `<EMAIL>`
- **Senior Manager**: `<EMAIL>`

## Features

- ✅ Visual indicator when bypass mode is active
- ✅ Clickable test credentials in the UI
- ✅ Simulated API delay for realistic testing
- ✅ Proper localStorage storage of user data
- ✅ Correct role-based dashboard redirection
- ✅ Easy toggle on/off mechanism

## What's Bypassed

- Backend API call to `/api/v1/auth/login`
- Password validation
- Token generation from backend
- User data retrieval from database

## What's NOT Affected

- All backend authentication and authorization systems remain unchanged
- API interceptors still work normally
- Token validation on protected routes (uses test tokens)
- All other API endpoints function normally

## Dashboard Redirections

- Admin → `/admin/dashboard`
- Project Manager → `/manager/dashboard`
- Field Officer → `/field/dashboard`
- Accountant → `/accountant/dashboard`
- Senior Manager → `/senior-manager/dashboard`

## How to Revert

### Complete Removal
1. Open `finalyear/src/app/(auth)/login/page.tsx`
2. Remove lines 8-48 (bypass constants and test users)
3. Replace the `handleSubmit` function with the original backend-only version
4. Remove the bypass mode indicator UI (lines with `BYPASS_MODE` conditions)
5. Remove the test credentials section

### Quick Disable
1. Set `BYPASS_MODE = false` in the code, or
2. Set `NEXT_PUBLIC_BYPASS_AUTH=false` in environment variables

## Security Notes

- This bypass only works on the frontend
- Backend security remains fully intact
- Test tokens are not valid for actual backend operations
- Always remove before production deployment

## Testing Workflow

1. Enable bypass mode
2. Navigate to login page
3. Click on any test credential email or manually enter
4. Enter any password
5. Click "Sign in"
6. Verify redirection to correct dashboard
7. Test frontend functionality without backend dependencies

## Troubleshooting

- If bypass doesn't work, check browser console for errors
- Ensure `BYPASS_MODE` is `true` in the code
- Clear localStorage if experiencing issues
- Check that test user emails match exactly

---

**Remember to remove this bypass before production deployment!**
