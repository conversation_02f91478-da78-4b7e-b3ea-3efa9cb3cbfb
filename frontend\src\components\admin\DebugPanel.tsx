'use client';

import React, { useState, useEffect } from 'react';
import { 
  ExclamationTriangleIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ArrowPathIcon 
} from '@heroicons/react/24/outline';
import { checkBackendHealth, checkApiEndpoints, HealthCheckResult } from '@/utils/healthCheck';

interface DebugPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

const DebugPanel: React.FC<DebugPanelProps> = ({ isVisible, onClose }) => {
  const [backendHealth, setBackendHealth] = useState<HealthCheckResult | null>(null);
  const [endpointResults, setEndpointResults] = useState<{[key: string]: HealthCheckResult}>({});
  const [isChecking, setIsChecking] = useState(false);

  const runHealthCheck = async () => {
    setIsChecking(true);
    try {
      const health = await checkBackendHealth();
      setBackendHealth(health);

      if (health.status === 'success') {
        const endpoints = await checkApiEndpoints();
        setEndpointResults(endpoints);
      } else {
        setEndpointResults({});
      }
    } catch (error) {
      // Health check failed
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    if (isVisible) {
      runHealthCheck();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">System Debug Panel</h2>
          <div className="flex gap-2">
            <button
              onClick={runHealthCheck}
              disabled={isChecking}
              className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              <ArrowPathIcon className={`w-4 h-4 ${isChecking ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={onClose}
              className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>

        {isChecking && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Checking system health...</p>
          </div>
        )}

        {!isChecking && (
          <div className="space-y-6">
            {/* Backend Health */}
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">Backend Server Status</h3>
              {backendHealth ? (
                <div className="flex items-start gap-3">
                  {getStatusIcon(backendHealth.status)}
                  <div className="flex-1">
                    <p className="font-medium">{backendHealth.message}</p>
                    {backendHealth.details && (
                      <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                        {JSON.stringify(backendHealth.details, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No health check data available</p>
              )}
            </div>

            {/* API Endpoints */}
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">API Endpoints Status</h3>
              {Object.keys(endpointResults).length > 0 ? (
                <div className="space-y-3">
                  {Object.entries(endpointResults).map(([name, result]) => (
                    <div key={name} className="flex items-start gap-3">
                      {getStatusIcon(result.status)}
                      <div className="flex-1">
                        <p className="font-medium capitalize">{name}</p>
                        <p className="text-sm text-gray-600">{result.message}</p>
                        {result.details && (
                          <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">
                  {backendHealth?.status === 'error' 
                    ? 'Cannot check endpoints - backend server is not responding'
                    : 'No endpoint data available'
                  }
                </p>
              )}
            </div>

            {/* Troubleshooting Tips */}
            <div className="border rounded-lg p-4 bg-yellow-50">
              <h3 className="text-lg font-semibold mb-3 text-yellow-800">Troubleshooting Tips</h3>
              <div className="text-sm text-yellow-700 space-y-2">
                <p><strong>If backend server is not running:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Navigate to the backend-main directory</li>
                  <li>Run: <code className="bg-yellow-100 px-1 rounded">npm install</code></li>
                  <li>Run: <code className="bg-yellow-100 px-1 rounded">npm start</code> or <code className="bg-yellow-100 px-1 rounded">node server.js</code></li>
                  <li>Make sure MongoDB is running</li>
                  <li>Check the .env file configuration</li>
                </ul>
                
                <p className="mt-3"><strong>If endpoints are failing:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Check if you're logged in as an admin</li>
                  <li>Verify API routes are correctly configured</li>
                  <li>Check browser console for detailed error messages</li>
                  <li>Ensure CORS is properly configured</li>
                </ul>
              </div>
            </div>

            {/* Configuration Info */}
            <div className="border rounded-lg p-4 bg-blue-50">
              <h3 className="text-lg font-semibold mb-3 text-blue-800">Configuration</h3>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>Backend URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000'}</p>
                <p><strong>Environment:</strong> {process.env.NODE_ENV || 'development'}</p>
                <p><strong>Frontend URL:</strong> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DebugPanel;
