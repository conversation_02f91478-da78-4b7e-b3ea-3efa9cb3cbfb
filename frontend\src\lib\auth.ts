import { sign, verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || 'b103e8909f948fcf073c9478323d0d91d8b8f38d9bde9d1d832ec6a2cbe0eb7a97b1ad87c0b4c5c24d48907f799ddc8bc55d49de6633d00b8db30d3af5c9dcda';

export interface UserToken {
  id: number;
  email: string;
  role: string;
}

export const verifyToken = (token: string): UserToken => {
  try {
    return verify(token, JWT_SECRET) as UserToken;
  } catch (error) {
    throw new Error('Invalid token');
  }
};

export const getTokenFromCookies = async () => {
  const cookieStore = await cookies();
  return cookieStore.get('auth-token')?.value;
};

// For app directory usage
export const getUserFromToken = async (): Promise<UserToken | null> => {
  const token = await getTokenFromCookies();
  if (!token) return null;
  try {
    return verifyToken(token);
  } catch (error) {
    return null;
  }
};

// For API route usage
export const getUserFromTokenApi = async (request: Request): Promise<UserToken | null> => {
  const cookieHeader = request.headers.get('cookie');
  if (!cookieHeader) return null;
  const token = cookieHeader
    .split(';')
    .map(c => c.trim())
    .find(c => c.startsWith('auth-token='))
    ?.split('=')[1];
  if (!token) return null;
  try {
    return verifyToken(token);
  } catch (error) {
    return null;
  }
};

export const isAuthorized = async (allowedRoles: string[]): Promise<boolean> => {
  const user = await getUserFromToken();
  if (!user) return false;
  return allowedRoles.includes(user.role);
};

export const ROLES = {
  ADMIN: 'admin',
  PROJECT_MANAGER: 'projectManager',
  FIELD_OFFICER: 'fieldOfficer',
  ACCOUNTANT: 'accountant',
  SENIOR_MANAGER: 'seniorManager',
} as const;

export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
}

export const generateToken = (payload: TokenPayload): string => {
  return sign(payload, JWT_SECRET, { expiresIn: '1d' });
};

export const isAuthorizedUserRole = (userRole: string, allowedRoles: string[]): boolean => {
  return allowedRoles.includes(userRole);
}; 