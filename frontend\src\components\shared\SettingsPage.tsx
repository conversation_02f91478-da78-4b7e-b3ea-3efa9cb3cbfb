"use client";

import { useState, useEffect } from "react";
import {
  CheckI<PERSON>,
  SunIcon,
  MoonIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useTheme } from "@/app/theme-context";
import { api } from "@/services/api";

export default function SettingsPage() {
  const { theme, setTheme } = useTheme();
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");

  // Password change state
  const [passwords, setPasswords] = useState({
    current: "",
    newPass: "",
    confirm: "",
  });

  // Notification settings
  const [notifications, setNotifications] = useState({
    emailNotifications: false,
    projectUpdates: true,
    activityReminders: true,
    reportNotifications: true,
    systemAlerts: true,
  });

  // Fetch current settings on mount
  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch settings
        const settingsRes = await api.getUserSettings();
        if (settingsRes.data) {
          setNotifications({
            emailNotifications: settingsRes.data.emailNotifications ?? false,
            projectUpdates: settingsRes.data.projectUpdates ?? true,
            activityReminders: settingsRes.data.activityReminders ?? true,
            reportNotifications: settingsRes.data.reportNotifications ?? true,
            systemAlerts: settingsRes.data.systemAlerts ?? true,
          });
          if (settingsRes.data.theme) setTheme(settingsRes.data.theme);
        }
      } catch (error) {
        console.error("Failed to load settings:", error);
      }
    }
    fetchData();
  }, [setTheme]);

  // Handle input changes for passwords
  const onPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPasswords((p) => ({ ...p, [e.target.name]: e.target.value }));
  };

  // Validate password form
  const validatePasswords = () => {
    if (
      passwords.current === "" &&
      passwords.newPass === "" &&
      passwords.confirm === ""
    ) {
      // No password change requested, so valid
      return "";
    }
    if (!passwords.current) return "Current password is required.";
    if (passwords.newPass.length < 8)
      return "New password must be at least 8 characters.";
    if (passwords.newPass !== passwords.confirm) return "Passwords do not match.";
    return "";
  };

  // Save settings handler
  const handleSave = async () => {
    setErrorMsg("");
    const validationError = validatePasswords();
    if (validationError) {
      setErrorMsg(validationError);
      return;
    }
    setLoading(true);

    try {
      // 1. Update notifications & theme preference
      await api.updateUserSettings({
        ...notifications,
        theme,
      });

      // 2. Change password only if fields are filled
      if (
        passwords.current !== "" &&
        passwords.newPass !== "" &&
        passwords.confirm !== ""
      ) {
        await api.changePassword({
          currentPassword: passwords.current,
          newPassword: passwords.newPass,
        });
      }

      setSaveSuccess(true);
      setPasswords({ current: "", newPass: "", confirm: "" });
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error: any) {
      setErrorMsg(
        error?.response?.data?.message || "Failed to save settings. Try again."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8 p-6 max-w-3xl mx-auto">
      {/* Header */}
      <header>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage your account preferences and security settings
        </p>
      </header>

      {/* Success Message */}
      {saveSuccess && (
        <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-700/40 dark:bg-green-800/20">
          <CheckIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
          <span className="font-medium text-green-800 dark:text-green-200">
            Settings saved successfully!
          </span>
        </div>
      )}

      {/* Error Message */}
      {errorMsg && (
        <div className="flex items-center gap-2 rounded-lg border border-red-300 bg-red-50 p-4 dark:border-red-700/40 dark:bg-red-900/20">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
          <span className="font-medium text-red-800 dark:text-red-200">{errorMsg}</span>
        </div>
      )}

      {/* Appearance Section */}
      <section className="rounded-lg border border-gray-100 bg-white p-6 shadow-sm dark:border-gray-700/40 dark:bg-gray-800">
        <h2 className="mb-6 text-lg font-semibold text-gray-900 dark:text-gray-100">
          Appearance
        </h2>

        <div className="flex space-x-4">
          <button
            onClick={() => setTheme("light")}
            className={`flex items-center rounded-md px-4 py-2 transition ${
              theme === "light"
                ? "bg-sky-600 text-white"
                : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
            }`}
          >
            <SunIcon className="mr-2 h-5 w-5" />
            Light Mode
          </button>

          <button
            onClick={() => setTheme("dark")}
            className={`flex items-center rounded-md px-4 py-2 transition ${
              theme === "dark"
                ? "bg-sky-600 text-white"
                : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
            }`}
          >
            <MoonIcon className="mr-2 h-5 w-5" />
            Dark Mode
          </button>
        </div>
      </section>

      {/* Notifications Section */}
      <section className="rounded-lg border border-gray-100 bg-white p-6 shadow-sm dark:border-gray-700/40 dark:bg-gray-800">
        <h2 className="mb-6 text-lg font-semibold text-gray-900 dark:text-gray-100">
          Notification Preferences
        </h2>
        <div className="space-y-4">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={notifications.emailNotifications}
              onChange={(e) => setNotifications(prev => ({ ...prev, emailNotifications: e.target.checked }))}
              className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
            />
            <div>
              <span className="text-gray-900 dark:text-gray-100 font-medium">Email Notifications</span>
              <p className="text-sm text-gray-500 dark:text-gray-400">Receive notifications via email</p>
            </div>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={notifications.projectUpdates}
              onChange={(e) => setNotifications(prev => ({ ...prev, projectUpdates: e.target.checked }))}
              className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
            />
            <div>
              <span className="text-gray-900 dark:text-gray-100 font-medium">Project Updates</span>
              <p className="text-sm text-gray-500 dark:text-gray-400">Get notified about project status changes</p>
            </div>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={notifications.activityReminders}
              onChange={(e) => setNotifications(prev => ({ ...prev, activityReminders: e.target.checked }))}
              className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
            />
            <div>
              <span className="text-gray-900 dark:text-gray-100 font-medium">Activity Reminders</span>
              <p className="text-sm text-gray-500 dark:text-gray-400">Receive reminders for upcoming activities</p>
            </div>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={notifications.reportNotifications}
              onChange={(e) => setNotifications(prev => ({ ...prev, reportNotifications: e.target.checked }))}
              className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
            />
            <div>
              <span className="text-gray-900 dark:text-gray-100 font-medium">Report Notifications</span>
              <p className="text-sm text-gray-500 dark:text-gray-400">Get notified about report submissions and approvals</p>
            </div>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={notifications.systemAlerts}
              onChange={(e) => setNotifications(prev => ({ ...prev, systemAlerts: e.target.checked }))}
              className="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500"
            />
            <div>
              <span className="text-gray-900 dark:text-gray-100 font-medium">System Alerts</span>
              <p className="text-sm text-gray-500 dark:text-gray-400">Receive important system notifications</p>
            </div>
          </label>
        </div>
      </section>

      {/* Password Change Section */}
      <section className="rounded-lg border border-gray-100 bg-white p-6 shadow-sm dark:border-gray-700/40 dark:bg-gray-800">
        <h2 className="mb-6 text-lg font-semibold text-gray-900 dark:text-gray-100">
          Change Password
        </h2>
        <div className="space-y-4 max-w-md">
          <input
            type="password"
            name="current"
            placeholder="Current Password"
            value={passwords.current}
            onChange={onPasswordChange}
            className="w-full rounded border border-gray-300 px-3 py-2 text-gray-700 placeholder-gray-400 focus:border-sky-500 focus:outline-none focus:ring-1 focus:ring-sky-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 dark:placeholder-gray-500"
          />
          <input
            type="password"
            name="newPass"
            placeholder="New Password"
            value={passwords.newPass}
            onChange={onPasswordChange}
            className="w-full rounded border border-gray-300 px-3 py-2 text-gray-700 placeholder-gray-400 focus:border-sky-500 focus:outline-none focus:ring-1 focus:ring-sky-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 dark:placeholder-gray-500"
          />
          <input
            type="password"
            name="confirm"
            placeholder="Confirm New Password"
            value={passwords.confirm}
            onChange={onPasswordChange}
            className="w-full rounded border border-gray-300 px-3 py-2 text-gray-700 placeholder-gray-400 focus:border-sky-500 focus:outline-none focus:ring-1 focus:ring-sky-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 dark:placeholder-gray-500"
          />
        </div>
      </section>

      {/* Save Button */}
      <div className="flex justify-end space-x-3">
        <button
          onClick={handleSave}
          disabled={loading}
          className="rounded-md bg-sky-600 px-6 py-2 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-sky-700 transition"
        >
          {loading ? "Saving..." : "Save Changes"}
        </button>
      </div>
    </div>
  );
}
