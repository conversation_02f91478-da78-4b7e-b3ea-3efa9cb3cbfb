@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@layer base {
  body {
    @apply text-gray-900 bg-white font-medium text-sm;
  }

  /* Set medium font weight and smaller font sizes as default for common elements */
  h1 {
    @apply font-medium text-xl;
  }

  h2 {
    @apply font-medium text-lg;
  }

  h3 {
    @apply font-medium text-base;
  }

  h4, h5, h6 {
    @apply font-medium text-sm;
  }

  p, span, div, label, input, button {
    @apply font-medium text-sm;
  }

  /* Reduce table text sizes */
  table {
    @apply text-xs;
  }

  th {
    @apply text-xs;
  }

  td {
    @apply text-xs;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200 font-medium shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-all duration-200 font-medium shadow-sm hover:shadow-md;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300;
  }

  .card-modern {
    @apply bg-white rounded-xl shadow-sm border border-blue-200 hover:shadow-md transition-all duration-300 hover:scale-105;
  }

  .input-modern {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-blue-50 via-white to-indigo-50;
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}