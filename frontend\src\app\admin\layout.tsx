'use client';

import React, { useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  HomeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  ShieldCheckIcon,
  ChartBarIcon,
} from "@heroicons/react/24/solid";
import CollapsibleSidebar from "@/components/CollapsibleSidebar";
import NotificationBell from "@/components/NotificationBell";

interface AdminLayoutProps {
  children: React.ReactNode;
}

const navLinks = [
  { name: "Dashboard", href: "/admin/dashboard", icon: HomeIcon },
  { name: "Users", href: "/admin/users", icon: UserGroupIcon },
  { name: "Activity Logs", href: "/admin/logs", icon: DocumentTextIcon },
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [userName, setUserName] = useState('Admin');
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is authorized to access admin routes
    const checkAuthorization = () => {
      const userRole = localStorage.getItem('userRole');
      const token = localStorage.getItem('token');

      console.log('🔍 Admin layout authorization check:', {
        userRole,
        hasToken: !!token,
        pathname
      });

      if (!token) {
        console.log('❌ No token found, redirecting to login');
        router.push('/login');
        return;
      }

      if (userRole !== 'admin') {
        console.log('❌ User is not admin, redirecting to appropriate dashboard');
        // Redirect to appropriate dashboard based on role
        switch (userRole) {
          case 'projectManager':
            router.push('/manager/dashboard');
            break;
          case 'fieldOfficer':
            router.push('/field/dashboard');
            break;
          case 'accountant':
            router.push('/accountant/dashboard');
            break;
          case 'seniorManager':
            router.push('/senior-manager/dashboard');
            break;
          default:
            router.push('/login');
        }
        return;
      }

      console.log('✅ User is authorized as admin');
      setIsAuthorized(true);
    };

    checkAuthorization();

    // Fetch user info for greeting and avatar
    const token = localStorage.getItem('token');

    if (token) {
      fetch('http://localhost:7000/api/v1/user/me', {
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
        .then(res => res.json())
        .then(data => {
          console.log('👤 Admin user data:', data);
          const userData = data?.data || data;
          setUserName(userData?.fullName || userData?.name || 'Admin');
        })
        .catch((error) => {
          console.error('Failed to fetch user data:', error);
          // Try localStorage as fallback
          const storedUser = localStorage.getItem('user');
          if (storedUser) {
            try {
              const user = JSON.parse(storedUser);
              setUserName(user.fullName || user.name || 'Admin');
            } catch (error) {
              setUserName('Admin');
            }
          }
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      // Try localStorage as fallback
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          setUserName(user.fullName || user.name || 'Admin');
        } catch (error) {
          setUserName('Admin');
        }
      }
      setIsLoading(false);
    }
  }, [router, pathname]);

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return { text: 'Good morning', emoji: '🌅' };
    if (hour < 17) return { text: 'Good afternoon', emoji: '☀️' };
    return { text: 'Good evening', emoji: '🌙' };
  };

  const { text: greeting, emoji: greetingEmoji } = getGreeting();

  const getPageTitle = () => {
    if (pathname === '/admin/dashboard') return 'Dashboard';
    if (pathname === '/admin/users') return 'User Management';
    if (pathname === '/admin/logs') return 'Activity Logs';
    if (pathname === '/admin/profile') return 'Profile';
    if (pathname === '/admin/settings') return 'Settings';
    return 'Admin Panel';
  };

  // Show loading spinner while checking authorization
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#f4f6fa] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Don't render anything if not authorized (redirect will happen)
  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[#f4f6fa]">
      <CollapsibleSidebar
        navLinks={navLinks}
        userName={userName}
        userRole="Administrator"
      >
        {/* Header */}
        <header className="sticky top-0 z-10 flex items-center justify-between bg-white border-b border-gray-100 px-6 py-3">
          <div className="text-lg font-bold text-blue-700 tracking-tight">
            {getPageTitle()}
          </div>
          <div className="flex-1 flex justify-center">
            <div className="text-base font-medium text-gray-700">
              {greeting}, {userName}! {greetingEmoji}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <NotificationBell userRole="admin" />
            <button
              onClick={() => router.push('/admin/profile')}
              className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden hover:bg-gray-300 transition-colors"
            >
              <UserCircleIcon className="w-6 h-6 text-blue-400" />
            </button>
          </div>
        </header>
        <main className="p-6">{children}</main>
      </CollapsibleSidebar>
    </div>
  );
}