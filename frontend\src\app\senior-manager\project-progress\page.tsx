"use client";

import React, { useState, useEffect } from "react";
import { Tab } from "@headlessui/react";
import { api } from "@/services/api";

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

interface Milestone {
  title: string;
  description?: string;
  deadline: string;
  targetDate?: string;
  completedDate?: string;
  completed: boolean;
  progress: number;
  status?: string;
  isOverdue?: boolean;
  dependencies?: string[];
  deliverables?: string[];
}

interface Activity {
  title: string;
  description: string;
  progress: number;
  status: string;
  startDate?: string;
  endDate?: string;
  assignedTo?: string;
  priority?: string;
  budget?: number;
  isCritical?: boolean;
  isOverdue?: boolean;
  startOffset?: string;
  duration?: string;
}

interface Deliverable {
  title: string;
  description?: string;
  deadline: string;
  completed: boolean;
  progress: number;
  status?: string;
  type?: string;
  assignedTo?: string;
  isOverdue?: boolean;
  requirements?: string[];
  submittedFiles?: Array<{
    name: string;
    submittedDate?: string;
  }>;
}

interface ProjectProgress {
  projectId: string;
  title: string;
  progress: number;
  status: string;
  startDate: string;
  endDate: string;
  location: string;
  milestones: Milestone[];
  activities: Activity[];
  deliverables: Deliverable[];
  totalActivities: number;
  completedActivities: number;
  inProgressActivities: number;
  pendingActivities: number;
  // Milestone statistics
  totalMilestones: number;
  completedMilestones: number;
  upcomingMilestones: number;
  overdueMilestones: number;
  // Deliverable statistics
  totalDeliverables: number;
  completedDeliverables: number;
  inProgressDeliverables: number;
  overdueDeliverables: number;
  // Timeline properties
  bufferDays?: number;
}

export default function ProjectProgressPage() {
  const [projects, setProjects] = useState<ProjectProgress[]>([]);
  const [selectedProject, setSelectedProject] = useState<ProjectProgress | null>(null);
  const [loading, setLoading] = useState(true);


  useEffect(() => {
    fetchProjectProgress();
  }, []);

  const fetchProjectProgress = async () => {
    try {
      setLoading(true);
      const response = await api.getProjectProgress();
      setProjects(response.data || []);
      if (response.data && response.data.length > 0) {
        setSelectedProject(response.data[0]);
      }
    } catch (error) {
      console.error("Failed to fetch project progress:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Generate timeline months based on project start and end dates
  const generateTimelineMonths = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Fallback to current year if dates are invalid
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      const currentYear = new Date().getFullYear();
      const fallbackStart = new Date(currentYear, 0, 1); // January 1st
      const fallbackEnd = new Date(currentYear, 11, 31); // December 31st
      return generateTimelineMonths(fallbackStart.toISOString(), fallbackEnd.toISOString());
    }

    const months = [];

    // Start from the beginning of the start month
    const current = new Date(start.getFullYear(), start.getMonth(), 1);

    // Generate months until we reach or pass the end date
    while (current <= end) {
      months.push({
        label: current.toLocaleDateString('en', { month: 'short', year: '2-digit' }),
        fullDate: new Date(current)
      });
      current.setMonth(current.getMonth() + 1);
    }

    // Ensure we have at least 6 months for better visualization
    while (months.length < 6) {
      months.push({
        label: current.toLocaleDateString('en', { month: 'short', year: '2-digit' }),
        fullDate: new Date(current)
      });
      current.setMonth(current.getMonth() + 1);
    }

    return months;
  };

  // Calculate position percentage for dates within the timeline
  const calculateTimelinePosition = (date: string, timelineMonths: any[]) => {
    if (!date || timelineMonths.length === 0) return '0%';

    const targetDate = new Date(date);
    // Check if date is valid
    if (isNaN(targetDate.getTime())) return '0%';

    const startDate = timelineMonths[0].fullDate;
    const endDate = new Date(timelineMonths[timelineMonths.length - 1].fullDate);
    endDate.setMonth(endDate.getMonth() + 1); // End of last month

    const totalDuration = endDate.getTime() - startDate.getTime();
    const elapsed = targetDate.getTime() - startDate.getTime();

    const percentage = Math.max(0, Math.min(100, (elapsed / totalDuration) * 100));
    return `${percentage}%`;
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned':
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'planning':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const calculateDaysRemaining = (endDate: string) => {
    const today = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading project progress...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Project Progress Dashboard</h1>
          <p className="text-gray-600">Monitor project milestones, activities, and deliverables</p>
        </div>

        {/* Project Selection Dropdown */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Select Project</h2>
            {selectedProject && (
              <div className="flex items-center space-x-4">
                <span className={`px-3 py-1 text-sm rounded-full font-medium ${getStatusColor(selectedProject.status)}`}>
                  {selectedProject.status}
                </span>
                <span className="text-sm text-gray-600">
                  {selectedProject.progress}% Complete
                </span>
              </div>
            )}
          </div>

          <div className="max-w-md">
            <select
              value={selectedProject?.projectId || ""}
              onChange={(e) => {
                const project = projects.find(p => p.projectId === e.target.value);
                setSelectedProject(project || null);
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
            >
              <option value="">Select a project...</option>
              {projects.map((project) => (
                <option key={project.projectId} value={project.projectId}>
                  {project.title} - {project.progress}% Complete
                </option>
              ))}
            </select>
          </div>

          {selectedProject && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Location:</span>
                  <span className="ml-2 font-medium">{selectedProject.location}</span>
                </div>
                <div>
                  <span className="text-gray-600">Start Date:</span>
                  <span className="ml-2 font-medium">{formatDate(selectedProject.startDate)}</span>
                </div>
                <div>
                  <span className="text-gray-600">End Date:</span>
                  <span className="ml-2 font-medium">{formatDate(selectedProject.endDate)}</span>
                </div>
                {selectedProject.status === 'completed' ? (
                  <div>
                    <span className="text-gray-600">Completed:</span>
                    <span className="ml-2 font-medium text-green-600">Project Completed</span>
                  </div>
                ) : (
                  <div>
                    <span className="text-gray-600">Days Remaining:</span>
                    <span className={`ml-2 font-medium ${calculateDaysRemaining(selectedProject.endDate) < 0 ? 'text-red-600' : 'text-gray-900'}`}>
                      {calculateDaysRemaining(selectedProject.endDate) < 0
                        ? `${Math.abs(calculateDaysRemaining(selectedProject.endDate))} days overdue`
                        : `${calculateDaysRemaining(selectedProject.endDate)} days`
                      }
                    </span>
                  </div>
                )}
              </div>

              <div className="mt-3">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Overall Progress</span>
                  <span>{selectedProject.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      selectedProject.progress >= 80 ? 'bg-green-500' :
                      selectedProject.progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${selectedProject.progress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Project Details Tabs */}
        {selectedProject && (
          <div className="bg-white rounded-lg shadow-md">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-2xl font-semibold text-gray-900">{selectedProject.title}</h2>
            </div>

            <Tab.Group>
              <Tab.List className="flex space-x-1 bg-gray-50 p-1 m-6 rounded-xl">
                <Tab
                  className={({ selected }) =>
                    classNames(
                      "w-full rounded-lg py-2.5 text-sm font-medium leading-5",
                      "ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2",
                      selected
                        ? "bg-white text-blue-700 shadow"
                        : "text-gray-900 hover:bg-white/[0.12] hover:text-blue-600"
                    )
                  }
                >
                  Project Activities
                </Tab>
                <Tab
                  className={({ selected }) =>
                    classNames(
                      "w-full rounded-lg py-2.5 text-sm font-medium leading-5",
                      "ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2",
                      selected
                        ? "bg-white text-blue-700 shadow"
                        : "text-gray-900 hover:bg-white/[0.12] hover:text-blue-600"
                    )
                  }
                >
                  Project Milestones
                </Tab>
                <Tab
                  className={({ selected }) =>
                    classNames(
                      "w-full rounded-lg py-2.5 text-sm font-medium leading-5",
                      "ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2",
                      selected
                        ? "bg-white text-blue-700 shadow"
                        : "text-gray-900 hover:bg-white/[0.12] hover:text-blue-600"
                    )
                  }
                >
                  Project Deliverables
                </Tab>
                <Tab
                  className={({ selected }) =>
                    classNames(
                      "w-full rounded-lg py-2.5 text-sm font-medium leading-5",
                      "ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2",
                      selected
                        ? "bg-white text-blue-700 shadow"
                        : "text-gray-900 hover:bg-white/[0.12] hover:text-blue-600"
                    )
                  }
                >
                  Project Timeline (Gantt Chart)
                </Tab>
              </Tab.List>

              <Tab.Panels className="p-6">
                {/* Project Activities Tab */}
                <Tab.Panel>
                  <div className="space-y-6">
                    {/* Activities List */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Activity Details</h3>
                      {selectedProject.activities.length > 0 ? (
                        <div className="space-y-4">
                          {selectedProject.activities.map((activity, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                              <div className="flex justify-between items-start mb-3">
                                <div className="flex-1">
                                  <h4 className="font-medium text-gray-900 mb-1">{activity.title}</h4>
                                  <p className="text-sm text-gray-600 mb-2">{activity.description}</p>

                                  <div className="flex flex-wrap gap-4 text-xs text-gray-500">
                                    {activity.assignedTo && (
                                      <span>👤 Assigned to: {activity.assignedTo}</span>
                                    )}

                                    {activity.budget && activity.budget > 0 && (
                                      <span>💰 Budget: MWK {activity.budget.toLocaleString()}</span>
                                    )}
                                  </div>

                                  {(activity.startDate && activity.endDate) && (
                                    <div className="text-xs text-gray-500 mt-1">
                                      📅 {formatDate(activity.startDate)} - {formatDate(activity.endDate)}
                                    </div>
                                  )}
                                </div>

                                <div className="ml-4 text-right">
                                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                                    activity.status === 'completed' ? 'bg-green-100 text-green-800' :
                                    activity.status === 'inprogress' ? 'bg-blue-100 text-blue-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                                  </span>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Progress</span>
                                  <span className="font-medium">{activity.progress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full transition-all duration-300 ${
                                      activity.status === 'completed' ? 'bg-green-500' :
                                      activity.status === 'inprogress' ? 'bg-blue-500' :
                                      'bg-gray-400'
                                    }`}
                                    style={{ width: `${activity.progress}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          <p>No activities defined for this project.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </Tab.Panel>

                {/* Project Milestones Tab */}
                <Tab.Panel>
                  <div className="space-y-6">
                    {/* Milestones List */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Milestone Details</h3>
                      {selectedProject.milestones.length > 0 ? (
                        <div className="space-y-4">
                          {selectedProject.milestones.map((milestone, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                              <div className="flex justify-between items-start mb-3">
                                <div className="flex-1">
                                  <div className="flex items-center mb-2">
                                    <div className={`w-4 h-4 rounded-full mr-3 ${
                                      milestone.status === 'completed' ? 'bg-green-500' :
                                      milestone.status === 'inprogress' ? 'bg-blue-500' :
                                      milestone.isOverdue ? 'bg-red-500' : 'bg-gray-400'
                                    }`}></div>
                                    <h4 className="font-medium text-gray-900">{milestone.title}</h4>
                                  </div>
                                  <p className="text-sm text-gray-600 mb-2 ml-7">{milestone.description}</p>

                                  <div className="flex flex-wrap gap-4 text-xs text-gray-500 ml-7">
                                    {milestone.targetDate && (
                                      <span className={`px-2 py-1 rounded-full ${
                                        milestone.isOverdue ? 'bg-red-100 text-red-800' :
                                        milestone.status === 'completed' ? 'bg-green-100 text-green-800' :
                                        'bg-blue-100 text-blue-800'
                                      }`}>
                                        📅 Target: {formatDate(milestone.targetDate)}
                                      </span>
                                    )}
                                    {milestone.completedDate && (
                                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                        ✅ Completed: {formatDate(milestone.completedDate)}
                                      </span>
                                    )}
                                    {milestone.dependencies && milestone.dependencies.length > 0 && (
                                      <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full">
                                        🔗 Dependencies: {milestone.dependencies.length}
                                      </span>
                                    )}
                                  </div>
                                </div>

                                <div className="ml-4 text-right">
                                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                                    milestone.status === 'completed' ? 'bg-green-100 text-green-800' :
                                    milestone.status === 'inprogress' ? 'bg-blue-100 text-blue-800' :
                                    milestone.isOverdue ? 'bg-red-100 text-red-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {milestone.status === 'completed' ? 'Completed' :
                                     milestone.status === 'inprogress' ? 'In Progress' :
                                     milestone.isOverdue ? 'Overdue' : 'Pending'}
                                  </span>
                                </div>
                              </div>

                              {milestone.deliverables && milestone.deliverables.length > 0 && (
                                <div className="ml-7 mt-3 p-3 bg-gray-50 rounded-lg">
                                  <h5 className="text-sm font-medium text-gray-700 mb-2">Associated Deliverables:</h5>
                                  <ul className="text-sm text-gray-600 space-y-1">
                                    {milestone.deliverables.map((deliverable, idx) => (
                                      <li key={idx} className="flex items-center">
                                        <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                                        {deliverable}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                          </svg>
                          <p>No milestones defined for this project.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </Tab.Panel>

                {/* Project Deliverables Tab */}
                <Tab.Panel>
                  <div className="space-y-6">
                    {/* Deliverables List */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Deliverable Details</h3>
                      {selectedProject.deliverables.length > 0 ? (
                        <div className="space-y-4">
                          {selectedProject.deliverables.map((deliverable, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                              <div className="flex justify-between items-start mb-3">
                                <div className="flex-1">
                                  <div className="flex items-center justify-between mb-2">
                                    <h4 className="font-medium text-gray-900">{deliverable.title}</h4>
                                    <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                                      deliverable.status === 'completed' ? 'bg-green-100 text-green-800' :
                                      deliverable.status === 'inprogress' ? 'bg-blue-100 text-blue-800' :
                                      deliverable.isOverdue ? 'bg-red-100 text-red-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                      {deliverable.status === 'completed' ? 'Completed' :
                                       deliverable.status === 'inprogress' ? 'In Progress' :
                                       deliverable.isOverdue ? 'Overdue' : 'Pending'}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-600 mb-3">{deliverable.description}</p>

                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                      <span className="text-gray-500">Type:</span>
                                      <span className="ml-2 font-medium">{deliverable.type || 'Document'}</span>
                                    </div>
                                    {deliverable.deadline && (
                                      <div>
                                        <span className="text-gray-500">Deadline:</span>
                                        <span className={`ml-2 font-medium ${
                                          deliverable.isOverdue ? 'text-red-600' : 'text-gray-900'
                                        }`}>
                                          {formatDate(deliverable.deadline)}
                                        </span>
                                      </div>
                                    )}
                                    {deliverable.assignedTo && (
                                      <div>
                                        <span className="text-gray-500">Assigned to:</span>
                                        <span className="ml-2 font-medium">{deliverable.assignedTo}</span>
                                      </div>
                                    )}
                                  </div>

                                  {deliverable.requirements && deliverable.requirements.length > 0 && (
                                    <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                                      <h5 className="text-sm font-medium text-blue-900 mb-2">Requirements:</h5>
                                      <ul className="text-sm text-blue-800 space-y-1">
                                        {deliverable.requirements.map((req, idx) => (
                                          <li key={idx} className="flex items-start">
                                            <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2 mt-2 flex-shrink-0"></span>
                                            {req}
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}

                                  {deliverable.submittedFiles && deliverable.submittedFiles.length > 0 && (
                                    <div className="mt-3 p-3 bg-green-50 rounded-lg">
                                      <h5 className="text-sm font-medium text-green-900 mb-2">Submitted Files:</h5>
                                      <div className="space-y-1">
                                        {deliverable.submittedFiles.map((file, idx) => (
                                          <div key={idx} className="flex items-center text-sm text-green-800">
                                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            {file.name}
                                            {file.submittedDate && (
                                              <span className="ml-2 text-xs text-gray-500">
                                                ({formatDate(file.submittedDate)})
                                              </span>
                                            )}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="space-y-3">
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Progress</span>
                                  <span className="font-medium">{deliverable.progress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full transition-all duration-300 ${
                                      deliverable.status === 'completed' ? 'bg-green-500' :
                                      deliverable.status === 'inprogress' ? 'bg-indigo-500' :
                                      'bg-gray-400'
                                    }`}
                                    style={{ width: `${deliverable.progress}%` }}
                                  ></div>
                                </div>

                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <p>No deliverables defined for this project.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </Tab.Panel>

                {/* Project Timeline (Gantt Chart) Tab */}
                <Tab.Panel>
                  <div className="space-y-6">
                    {/* Gantt Chart */}
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Project Timeline</h3>
                        <p className="text-sm text-gray-600 mt-1">Visual representation of project activities, milestones, and deliverables</p>
                      </div>

                      <div className="p-6">
                        {/* Timeline Header */}
                        <div className="mb-6">
                          {(() => {
                            const timelineMonths = generateTimelineMonths(selectedProject.startDate, selectedProject.endDate);
                            return (
                              <div className="grid grid-cols-12 gap-2 text-xs text-gray-500 mb-2">
                                <div className="col-span-4">Task</div>
                                <div className={`col-span-8 grid grid-cols-${Math.min(12, timelineMonths.length)} gap-1`}>
                                  {timelineMonths.map((month, i) => (
                                    <div key={i} className="text-center">
                                      {month.label}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            );
                          })()}
                        </div>

                        {/* Activities Timeline */}
                        <div className="space-y-3">
                          <div className="text-sm font-medium text-gray-700 mb-2">Activities</div>
                          {selectedProject.activities.length > 0 ? (
                            (() => {
                              const timelineMonths = generateTimelineMonths(selectedProject.startDate, selectedProject.endDate);
                              return selectedProject.activities.map((activity, index) => {
                                const startPosition = calculateTimelinePosition(activity.startDate, timelineMonths);
                                const endPosition = calculateTimelinePosition(activity.endDate, timelineMonths);
                                const startPercent = parseFloat(startPosition.replace('%', ''));
                                const endPercent = parseFloat(endPosition.replace('%', ''));
                                const width = Math.max(2, endPercent - startPercent); // Minimum 2% width for visibility

                                return (
                                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded">
                                    <div className="col-span-4">
                                      <div className="text-sm font-medium text-gray-900 truncate">{activity.title}</div>
                                      <div className="text-xs text-gray-500">
                                        {activity.progress}% complete
                                        {activity.startDate && activity.endDate && (
                                          <div className="text-xs text-gray-400 mt-1">
                                            {formatDate(activity.startDate)} - {formatDate(activity.endDate)}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    <div className="col-span-8">
                                      <div className="relative h-6 bg-gray-50 rounded flex items-center">
                                        {/* Timeline background line */}
                                        <div className="absolute inset-0 flex items-center">
                                          <div className="w-full h-0.5 bg-gray-300"></div>
                                        </div>

                                        {/* Activity timeline with start and end circles */}
                                        <div
                                          className="absolute flex items-center h-full"
                                          style={{
                                            left: startPosition,
                                            width: `${width}%`
                                          }}
                                        >
                                          {/* Start circle */}
                                          <div className="relative">
                                            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                                          </div>

                                          {/* Progress line */}
                                          <div className="flex-1 h-1 mx-1 bg-blue-200 relative">
                                            <div
                                              className={`h-full ${
                                                activity.status === 'completed' ? 'bg-green-500' :
                                                activity.status === 'inprogress' ? 'bg-blue-500' :
                                                'bg-gray-400'
                                              }`}
                                              style={{ width: `${activity.progress}%` }}
                                            ></div>
                                          </div>

                                          {/* End circle */}
                                          <div className="relative">
                                            <div className={`w-3 h-3 rounded-full ${
                                              activity.status === 'completed' ? 'bg-green-500' :
                                              'bg-gray-400'
                                            }`}></div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              });
                            })()
                          ) : (
                            <div className="text-sm text-gray-500 py-4">No activities to display</div>
                          )}
                        </div>

                        {/* Milestones Timeline */}
                        <div className="space-y-3 mt-8">
                          <div className="text-sm font-medium text-gray-700 mb-2">Milestones</div>
                          {selectedProject.milestones.length > 0 ? (
                            (() => {
                              const timelineMonths = generateTimelineMonths(selectedProject.startDate, selectedProject.endDate);
                              return selectedProject.milestones.map((milestone, index) => {
                                const milestonePosition = calculateTimelinePosition(milestone.targetDate, timelineMonths);

                                return (
                                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded">
                                    <div className="col-span-4">
                                      <div className="text-sm font-medium text-gray-900 truncate">{milestone.title}</div>
                                      <div className="text-xs text-gray-500">
                                        {milestone.targetDate ? formatDate(milestone.targetDate) : 'No date set'}
                                      </div>
                                    </div>
                                    <div className="col-span-8">
                                      <div className="relative h-6 bg-gray-50 rounded flex items-center">
                                        {/* Timeline background line */}
                                        <div className="absolute inset-0 flex items-center">
                                          <div className="w-full h-0.5 bg-gray-300"></div>
                                        </div>

                                        {/* Milestone marker */}
                                        <div
                                          className="absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2"
                                          style={{ left: milestonePosition }}
                                        >
                                          <div className={`w-4 h-4 rounded-full border-2 border-white shadow-md ${
                                            milestone.status === 'completed' ? 'bg-green-500' :
                                            milestone.status === 'inprogress' ? 'bg-purple-500' :
                                            milestone.isOverdue ? 'bg-red-500' :
                                            'bg-orange-400'
                                          }`}></div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              });
                            })()
                          ) : (
                            <div className="text-sm text-gray-500 py-4">No milestones to display</div>
                          )}
                        </div>

                        {/* Deliverables Timeline */}
                        <div className="space-y-3 mt-8">
                          <div className="text-sm font-medium text-gray-700 mb-2">Deliverables</div>
                          {selectedProject.deliverables.length > 0 ? (
                            (() => {
                              const timelineMonths = generateTimelineMonths(selectedProject.startDate, selectedProject.endDate);
                              return selectedProject.deliverables.map((deliverable, index) => {
                                // Calculate deliverable position based on deadline
                                const deadlinePosition = calculateTimelinePosition(deliverable.deadline, timelineMonths);
                                // Assume deliverable work starts 30 days before deadline if no start date
                                const estimatedStartDate = new Date(deliverable.deadline);
                                estimatedStartDate.setDate(estimatedStartDate.getDate() - 30);
                                const startPosition = calculateTimelinePosition(estimatedStartDate.toISOString(), timelineMonths);

                                const startPercent = parseFloat(startPosition.replace('%', ''));
                                const endPercent = parseFloat(deadlinePosition.replace('%', ''));
                                const width = Math.max(2, endPercent - startPercent); // Minimum 2% width for visibility

                                return (
                                  <div key={index} className="grid grid-cols-12 gap-2 items-center py-2 hover:bg-gray-50 rounded">
                                    <div className="col-span-4">
                                      <div className="text-sm font-medium text-gray-900 truncate">{deliverable.title}</div>
                                      <div className="text-xs text-gray-500">
                                        {deliverable.deadline ? formatDate(deliverable.deadline) : 'No deadline'}
                                      </div>
                                    </div>
                                    <div className="col-span-8">
                                      <div className="relative h-6 bg-gray-50 rounded flex items-center">
                                        {/* Timeline background line */}
                                        <div className="absolute inset-0 flex items-center">
                                          <div className="w-full h-0.5 bg-gray-300"></div>
                                        </div>

                                        {/* Deliverable timeline with start and end circles */}
                                        <div
                                          className="absolute flex items-center h-full"
                                          style={{
                                            left: startPosition,
                                            width: `${width}%`
                                          }}
                                        >
                                          {/* Start circle */}
                                          <div className="relative">
                                            <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                                          </div>

                                          {/* Progress line */}
                                          <div className="flex-1 h-1 mx-1 bg-purple-200 relative">
                                            <div
                                              className={`h-full ${
                                                deliverable.status === 'completed' ? 'bg-green-500' :
                                                deliverable.status === 'inprogress' ? 'bg-purple-500' :
                                                deliverable.isOverdue ? 'bg-red-500' :
                                                'bg-gray-400'
                                              }`}
                                              style={{ width: `${deliverable.progress}%` }}
                                            ></div>
                                          </div>

                                          {/* End circle (deadline) */}
                                          <div className="relative">
                                            <div className={`w-3 h-3 rounded-full ${
                                              deliverable.status === 'completed' ? 'bg-green-500' :
                                              deliverable.isOverdue ? 'bg-red-500' :
                                              'bg-gray-400'
                                            }`}></div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              });
                            })()
                          ) : (
                            <div className="text-sm text-gray-500 py-4">No deliverables to display</div>
                          )}
                        </div>


                      </div>
                    </div>


                  </div>
                </Tab.Panel>
              </Tab.Panels>
            </Tab.Group>
          </div>
        )}



      </div>
    </div>
  );
}
