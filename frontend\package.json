{"name": "Sprodeta-M&E-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.1.1", "@types/nodemailer": "^6.4.17", "autoprefixer": "^10.4.17", "axios": "^1.9.0", "chart.js": "^4.5.0", "cors": "^2.8.5", "exceljs": "^4.4.0", "express": "^5.1.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "next": "^15.3.3", "nodemailer": "^7.0.3", "postcss": "^8.4.35", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "tailwindcss": "^3.4.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.17.57", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/socket.io": "^3.0.1", "eslint": "^9.0.0", "eslint-config-next": "^15.3.3", "typescript": "^5.3.3"}, "browserslist": ["defaults", "not IE 11"]}