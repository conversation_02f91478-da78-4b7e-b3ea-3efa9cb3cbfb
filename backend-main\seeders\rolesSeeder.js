const mongoose = require("mongoose");
const Role = require("../models/roles.model");

async function addRoles() {
  try {
    const roles = [
      { name: "admin" },
      { name: "senior<PERSON>anager" },
      { name: "projectManager" },
      { name: "fieldOfficer" },
      { name: "accountant" }
    ];

    for (const roleData of roles) {
      const existingRole = await Role.findOne({ name: roleData.name });
      if (!existingRole) {
        await Role.create(roleData);
        console.log(`✅ Created role: ${roleData.name}`);
      } else {
        console.log(`✅ Role already exists: ${roleData.name}`);
      }
    }

    console.log("✅ All roles seeded successfully!");
  } catch (error) {
    console.error("❌ Error seeding roles:", error);
    throw error;
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  mongoose
    .connect("mongodb://127.0.0.1:27017/sprodeta")
    .then(async () => {
      console.log("🌱 Starting roles seeder...");
      await addRoles();
      console.log("✅ Roles seeder completed!");
    })
    .catch((err) => {
      console.error("❌ MongoDB Connection Error:", err);
    })
    .finally(() => {
      mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
    });
}

module.exports = { addRoles };