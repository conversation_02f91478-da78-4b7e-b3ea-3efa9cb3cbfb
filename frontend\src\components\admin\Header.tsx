import { PlusIcon } from '@heroicons/react/24/solid';

const Header = ({ setIsAddUserModalOpen }: { setIsAddUserModalOpen: (isOpen: boolean) => void }) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 gap-4">
      <div>
        <h1 className="text-3xl font-bold text-blue-600 mb-1">
          User Management
        </h1>
        <p className="text-gray-500">
          Manage users and their roles in the system
        </p>
      </div>
      <button
        onClick={() => setIsAddUserModalOpen(true)}
        className="mt-4 sm:mt-0 inline-flex items-center justify-center rounded-lg bg-white border border-blue-100 px-6 py-2 text-base font-semibold text-blue-600 shadow hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:ring-offset-2 transition"
        data-add-user-btn
      >
        <PlusIcon className="w-5 h-5 mr-2 text-blue-600" />
        Add User
      </button>
    </div>
  );
};

export default Header;
