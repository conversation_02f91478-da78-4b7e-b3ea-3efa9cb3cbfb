import axios from "axios";
import React, { useState, useEffect } from "react";
import { baseUrl } from "@/constants/baseUrl";
import MultiTextInput from "../MultiTextInput";

const AddUserModal = ({
  setIsAddUserModalOpen,
  handleSkillsChange,
  callBack,
}: {
  setIsAddUserModalOpen: (isOpen: boolean) => void;
  handleSkillsChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  callBack: any;
}) => {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phoneNumber: "",
    district: "",
    department: "",
    bio: "",
    experience: 0,
    skills: [] as string[],
    role: "",
  });
  const [districts, setDistricts] = useState<any[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [isAddingUser, setIsAddingUser] = useState<boolean>(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const fetchDistricts = async () => {
    try {
      const res = await axios.get(`${baseUrl}/api/v1/district`);
      const districts = Array.isArray(res.data) ? res.data : res.data?.districts || res.data?.data || [];
      setDistricts(districts);
      console.log("Districts loaded:", districts);
    } catch (error) {
      console.error("Error fetching districts:", error);
      setError("Failed to load districts. Please check your connection.");
    }
  };

  const fetchRoles = async () => {
    try {
      const res = await axios.get(`${baseUrl}/api/v1/roles`);
      const roles = Array.isArray(res.data) ? res.data : res.data?.roles || res.data?.data || [];
      setRoles(roles);
      console.log("Roles loaded:", roles);
    } catch (error) {
      console.error("Error fetching roles:", error);
      setError("Failed to load roles. Please check your connection.");
    }
  };

  const fecthDepartments = async () => {
    try {
      const res = await axios.get(`${baseUrl}/api/v1/department`);
      setDepartments(res?.data?.departments || []);
      console.log(res?.data?.departments);
    } catch (error) {
      console.error("Error fetching departments:", error);
    }
  };

  useEffect(() => {
    fetchDistricts();
    fetchRoles();
    fecthDepartments();
  }, []);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "experience" ? Number(value) : value,
    }));
  };

  const handleAddUser = async (e: any) => {
    e.preventDefault();
    setIsAddingUser(true);
    setError("");
    setSuccess("");

    // Validate required fields
    if (!formData.fullName || !formData.email || !formData.phoneNumber || !formData.role) {
      setError("Please fill in all required fields");
      setIsAddingUser(false);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError("Please enter a valid email address");
      setIsAddingUser(false);
      return;
    }

    try {
      const response = await axios.post(`${baseUrl}/api/v1/user`, formData);

      if (response.data.status === 'success') {
        setSuccess("User created successfully! An email has been sent to the user with their login credentials.");
        await callBack();

        // Close modal after a short delay to show success message
        setTimeout(() => {
          setIsAddUserModalOpen(false);
        }, 2000);
      }
    } catch (error: any) {
      console.error("Error creating user:", error);

      // Safe error message extraction
      let errorMessage = "Failed to create user. Please try again.";

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.status === 400) {
        errorMessage = "User with this email or phone number already exists";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsAddingUser(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto">
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-sky-600 text-2xl font-bold"
          onClick={() => setIsAddUserModalOpen(false)}
        >
          &times;
        </button>
        <h2 className="text-xl font-bold text-sky-700 mb-6">Add User</h2>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
            {error}
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-lg text-sm">
            {success}
          </div>
        )}

        <form onSubmit={handleAddUser} className="space-y-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Name
            </label>
            <input
              type="text"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.fullName}
              onChange={handleInputChange}
              name="fullName"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.email}
              onChange={handleInputChange}
              name="email"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Phone Number
            </label>
            <input
              type="text"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              name="phoneNumber"
            />
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              District
            </label>
            <select
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.district}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, district: e.target.value }))
              }
              name="district"
              required
            >
              <option value="">Select a district</option>
              {districts.map((d: any) => (
                <option key={d._id} value={d._id}>
                  {d.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Department
            </label>
            <select
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.department}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, department: e.target.value }))
              }
              name="department"
              required
            >
              <option value="">Select a department</option>
              {departments.map((d: any) => (
                <option key={d?._id} value={d?._id}>
                  {d?.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Bio
            </label>
            <textarea
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.bio}
              onChange={handleInputChange}
              name="bio"
            />
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Experience
            </label>
            <input
              type="number"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.experience}
              onChange={handleInputChange}
              name="experience"
            />
          </div>
          <MultiTextInput
            values={formData.skills}
            onChange={(values: any) =>
              setFormData({ ...formData, skills: values })
            }
            label="Skills"
            placeholder="Type and press Enter..."
          />
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Role
            </label>
            <select
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.role}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, role: e.target.value }))
              }
              name="role"
              required
            >
              <option value="">Select a role</option>
              {roles.map((r: any) => (
                <option key={r?._id} value={r?._id}>
                  {r?.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              className="px-4 py-2 rounded-lg border border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100 font-semibold"
              onClick={() => setIsAddUserModalOpen(false)}
            >
              Cancel
            </button>
            <button
              disabled={isAddingUser}
              type="submit"
              className="px-6 py-2 rounded-lg bg-sky-600 text-white font-semibold hover:bg-sky-700 shadow transition"
            >
              {isAddingUser ? <p>Adding user...</p> : <p>Add User</p>}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddUserModal;
