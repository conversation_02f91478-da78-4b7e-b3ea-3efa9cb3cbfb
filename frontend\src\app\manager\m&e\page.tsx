"use client";

import { useState, useEffect } from "react";
import {
  ChartBarIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CalendarDaysIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  PencilSquareIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import dynamic from 'next/dynamic';
import apiUtil from '@/utils/api';

// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// Dynamically import Chart.js components to avoid SSR issues
const Chart = dynamic(() => import('react-chartjs-2').then(mod => mod.Line), { ssr: false });
const BarChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Bar), { ssr: false });
const DoughnutChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Doughnut), { ssr: false });

export default function MonitoringEvaluationPage() {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('performance');
  const [selectedProject, setSelectedProject] = useState('all');
  const [dateRange, setDateRange] = useState('30');

  // M&E Data State
  const [meData, setMeData] = useState({
    projects: [],
    activities: [],
    reports: [],
    kpis: [],
    activityPerformance: [],
    performance: {
      projectCompletion: 0,
      activityCompletion: 0,
      budgetUtilization: 0,
      timelineAdherence: 0,
      overallKPIPerformance: 0
    },
    trends: {
      labels: [],
      projectProgress: [],
      activityCompletion: [],
      budgetSpending: []
    }
  });

  const [evaluationMetrics, setEvaluationMetrics] = useState({
    effectiveness: 0,
    efficiency: 0,
    impact: 0,
    sustainability: 0,
    relevance: 0
  });

  const [selectedActivityForKPI, setSelectedActivityForKPI] = useState(null);
  const [evaluationType, setEvaluationType] = useState('post-project');
  const [showKPIDetails, setShowKPIDetails] = useState(false);
  const [selectedKPIForDetails, setSelectedKPIForDetails] = useState<any>(null);


  const [generatingPDF, setGeneratingPDF] = useState(false);

  // Enhanced function to extract KPI actual values from activity reports
  const extractKPIActualValue = (kpi: any, reports: any[], activity: any) => {
    if (!reports || reports.length === 0) {
      console.log(`📊 No reports found for KPI: ${kpi.name}`);
      return 0;
    }

    // Only use approved reports for KPI calculations
    const approvedReports = reports.filter(report => report.approved === true || report.status === 'approved');
    if (approvedReports.length === 0) {
      console.log(`📊 No approved reports found for KPI: ${kpi.name}`);
      return 0;
    }

    const latestReport = approvedReports[approvedReports.length - 1];
    console.log(`🔍 Debug - KPI: ${kpi.name}`);
    console.log(`🔍 Debug - Latest report content:`, latestReport.content);
    console.log(`🔍 Analyzing report for KPI: ${kpi.name}, Target: ${kpi.target}`);

    // Method 1: Exact KPI name matching
    if (latestReport.content && Array.isArray(latestReport.content)) {
      console.log(`📋 Searching for KPI "${kpi.name}" in report content:`);
      console.log(`📋 Available fields:`, latestReport.content.map(c => `"${c.fieldName}": "${c.entry}"`));

      // Try exact match first (case-insensitive, handle underscores/spaces)
      const exactMatch = latestReport.content.find((content: any) => {
        if (!content.fieldName || content.entry === undefined || content.entry === null) return false;
        const fieldName = content.fieldName.toLowerCase().trim().replace(/[_\s]+/g, ' ');
        const kpiName = kpi.name.toLowerCase().trim().replace(/[_\s]+/g, ' ');
        const isMatch = fieldName === kpiName;
        if (isMatch) {
          console.log(`🎯 Exact match found: "${content.fieldName}" matches "${kpi.name}"`);
        }
        return isMatch;
      });

      if (exactMatch && exactMatch.entry !== undefined && exactMatch.entry !== null && exactMatch.entry !== '') {
        console.log(`✅ Exact match found for ${kpi.name}: "${exactMatch.entry}"`);
        const numericValue = parseFloat(String(exactMatch.entry));
        if (!isNaN(numericValue)) {
          console.log(`✅ Returning numeric value: ${numericValue}`);
          return numericValue;
        }
      }

      // Method 2: Partial KPI name matching (improved)
      const partialMatch = latestReport.content.find((content: any) => {
        if (!content.fieldName || content.entry === undefined || content.entry === null) return false;
        const fieldName = content.fieldName.toLowerCase().trim().replace(/[_\s]+/g, ' ');
        const kpiName = kpi.name.toLowerCase().trim().replace(/[_\s]+/g, ' ');

        // Check if field name contains significant words from KPI name
        const kpiWords = kpiName.split(/\s+/).filter(word => word.length > 2);
        const fieldWords = fieldName.split(/\s+/).filter(word => word.length > 2);

        // Check for word overlap
        const hasMatch = kpiWords.some(kpiWord =>
          fieldWords.some(fieldWord =>
            fieldWord.includes(kpiWord) || kpiWord.includes(fieldWord)
          )
        );

        if (hasMatch) {
          console.log(`🎯 Partial match candidate: "${content.fieldName}" matches "${kpi.name}"`);
        }

        return hasMatch;
      });

      if (partialMatch && partialMatch.entry !== undefined && partialMatch.entry !== null && partialMatch.entry !== '') {
        console.log(`🎯 Partial match found for ${kpi.name}: ${partialMatch.fieldName} = ${partialMatch.entry}`);
        const numericValue = parseFloat(String(partialMatch.entry));
        if (!isNaN(numericValue)) {
          console.log(`✅ Returning partial match value: ${numericValue}`);
          return numericValue;
        }
      }

      // Method 3: Look for fields that contain numeric values (as fallback)
      console.log(`🔍 No exact/partial match found for ${kpi.name}, checking all numeric fields...`);

      const numericFields = latestReport.content.filter((content: any) => {
        if (!content.entry || content.entry === undefined || content.entry === null) return false;
        const numericValue = parseFloat(String(content.entry));
        return !isNaN(numericValue) && numericValue > 0;
      });

      console.log(`📊 Found ${numericFields.length} numeric fields:`,
        numericFields.map(f => `"${f.fieldName}": ${f.entry}`)
      );

      // If there's only one numeric field, use it as a fallback
      if (numericFields.length === 1) {
        const numericValue = parseFloat(String(numericFields[0].entry));
        console.log(`🎯 Using single numeric field as fallback for ${kpi.name}: ${numericValue}`);
        return numericValue;
      }

    }

    console.log(`❌ No matching field found for KPI: ${kpi.name}`);
    return 0;

    console.log(`❌ No matching data found for KPI: ${kpi.name}`);
    return 0;
  };

  // Function to get detailed KPI matching information for display
  const getKPIMatchingDetails = (kpi: any, reports: any[], activity: any) => {
    if (!reports || reports.length === 0) {
      return {
        matchType: 'No Reports',
        matchedField: null,
        matchedValue: null,
        allFields: [],
        actualValue: 0
      };
    }

    // Only use approved reports for KPI calculations
    const approvedReports = reports.filter(report => report.approved === true || report.status === 'approved');
    if (approvedReports.length === 0) {
      return {
        matchType: 'No Approved Reports',
        matchedField: null,
        matchedValue: null,
        allFields: reports.length > 0 && reports[0].content ? reports[0].content.map(c => ({ fieldName: c.fieldName, entry: c.entry })) : [],
        actualValue: 0
      };
    }

    const latestReport = approvedReports[approvedReports.length - 1];
    const allFields = latestReport.content && Array.isArray(latestReport.content)
      ? latestReport.content.map(c => ({ fieldName: c.fieldName, entry: c.entry }))
      : [];

    if (latestReport.content && Array.isArray(latestReport.content)) {
      // Try exact match first (handle underscores and spaces)
      const exactMatch = latestReport.content.find((content: any) => {
        if (!content.fieldName || content.entry === undefined || content.entry === null) return false;
        const fieldName = content.fieldName.toLowerCase().trim().replace(/[_\s]+/g, ' ');
        const kpiName = kpi.name.toLowerCase().trim().replace(/[_\s]+/g, ' ');
        return fieldName === kpiName;
      });

      if (exactMatch && exactMatch.entry !== undefined && exactMatch.entry !== null && exactMatch.entry !== '') {
        const numericValue = parseFloat(String(exactMatch.entry));
        if (!isNaN(numericValue)) {
          return {
            matchType: 'Exact Match',
            matchedField: exactMatch.fieldName,
            matchedValue: exactMatch.entry,
            allFields,
            actualValue: numericValue
          };
        }
      }

      // Try partial match
      const partialMatch = latestReport.content.find((content: any) => {
        if (!content.fieldName || !content.entry) return false;
        const fieldName = content.fieldName.toLowerCase();
        const kpiName = kpi.name.toLowerCase();
        const kpiWords = kpiName.split(/\s+/).filter(word => word.length > 2);
        return kpiWords.some(word => fieldName.includes(word));
      });

      if (partialMatch && partialMatch.entry) {
        const numericMatch = partialMatch.entry.match(/\d+(\.\d+)?/);
        if (numericMatch) {
          return {
            matchType: 'Partial Match',
            matchedField: partialMatch.fieldName,
            matchedValue: partialMatch.entry,
            allFields,
            actualValue: parseFloat(numericMatch[0])
          };
        }
      }

      // Try target-based match
      const targetValue = parseFloat(kpi.target) || 0;
      if (targetValue > 0) {
        const targetMatch = latestReport.content.find((content: any) => {
          if (!content.fieldName || !content.entry) return false;
          const fieldName = content.fieldName.toLowerCase();
          const entry = content.entry.toLowerCase();

          return (fieldName.includes('achievement') ||
                  fieldName.includes('result') ||
                  fieldName.includes('outcome') ||
                  fieldName.includes('target') ||
                  fieldName.includes('reached') ||
                  fieldName.includes('completed')) &&
                 (entry.includes(kpi.target.toLowerCase()) ||
                  entry.includes(targetValue.toString()));
        });

        if (targetMatch && targetMatch.entry) {
          const numericMatch = targetMatch.entry.match(/\d+(\.\d+)?/);
          if (numericMatch) {
            return {
              matchType: 'Target-based Match',
              matchedField: targetMatch.fieldName,
              matchedValue: targetMatch.entry,
              allFields,
              actualValue: parseFloat(numericMatch[0])
            };
          }
        }
      }

      // Try generic match
      const genericMatch = latestReport.content.find((content: any) => {
        if (!content.fieldName || !content.entry) return false;
        const fieldName = content.fieldName.toLowerCase();

        return fieldName.includes('achievement') ||
               fieldName.includes('result') ||
               fieldName.includes('outcome') ||
               fieldName.includes('beneficiaries') ||
               fieldName.includes('people') ||
               fieldName.includes('number') ||
               fieldName.includes('count') ||
               fieldName.includes('total');
      });

      if (genericMatch && genericMatch.entry) {
        const numericMatch = genericMatch.entry.match(/\d+(\.\d+)?/);
        if (numericMatch) {
          return {
            matchType: 'Generic Match',
            matchedField: genericMatch.fieldName,
            matchedValue: genericMatch.entry,
            allFields,
            actualValue: parseFloat(numericMatch[0])
          };
        }
      }
    }

    // Fallback to proxy calculations
    const actualValue = extractKPIActualValue(kpi, reports, activity);
    return {
      matchType: actualValue > 0 ? 'Calculated Proxy' : 'No Match',
      matchedField: null,
      matchedValue: null,
      allFields,
      actualValue
    };
  };

  // Fetch M&E data on mount and when filters change
  useEffect(() => {
    fetchMEData();
  }, [selectedProject, dateRange]);

  const fetchMEData = async () => {
    setLoading(true);
    try {
      console.log('🔄 Fetching M&E data...');

      // Check authentication
      const token = localStorage.getItem('token');
      console.log('🔐 Auth token exists:', !!token);

      // Fetch projects data
      const projectsRes = await apiUtil.get('/api/v1/project');
      console.log('🔍 Projects API Response:', projectsRes);
      const projects = Array.isArray(projectsRes.data) ? projectsRes.data : projectsRes.data?.data || projectsRes.data?.projects || [];
      console.log(`📋 Loaded ${projects.length} projects`);
      console.log('📋 Projects data structure:', projects);

      // Fetch activities data
      const activitiesRes = await apiUtil.get('/api/v1/manager/activities');
      console.log('🔍 Activities API Response:', activitiesRes);
      const activities = activitiesRes.data?.data || [];
      console.log(`⚡ Loaded ${activities.length} activities`);
      console.log('📋 Activities data structure:', activities);

      // Log activities with KPIs
      const activitiesWithKPIs = activities.filter((activity: any) => activity.kpis && activity.kpis.length > 0);
      console.log(`🎯 Activities with KPIs: ${activitiesWithKPIs.length}`);
      console.log(`📋 Total activities loaded: ${activities.length}`);

      // Log detailed activity information
      activities.forEach((activity: any, index: number) => {
        console.log(`📝 Activity ${index + 1}:`, {
          id: activity._id,
          title: activity.title,
          hasKPIs: !!(activity.kpis && activity.kpis.length > 0),
          kpisCount: activity.kpis ? activity.kpis.length : 0,
          kpis: activity.kpis || 'No KPIs',
          status: activity.status,
          assignedTo: activity.assignedTo?.fullName || 'Not assigned'
        });
      });

      activitiesWithKPIs.forEach((activity: any) => {
        console.log(`  - ${activity.title}: ${activity.kpis.length} KPIs`);
        activity.kpis.forEach((kpi: any, kpiIndex: number) => {
          console.log(`    KPI ${kpiIndex + 1}: ${kpi.name} (Target: ${kpi.target})`);
        });
      });

      // Fetch reports data
      const reportsRes = await apiUtil.get('/api/v1/manager/reports');
      console.log('🔍 Reports API Response:', reportsRes);
      const reports = reportsRes.data?.data || [];
      console.log(`📊 Loaded ${reports.length} reports`);

      // Debug: Check report approval status
      const approvedReports = reports.filter(r => r.approved === true || r.status === 'approved');
      const pendingReports = reports.filter(r => (r.approved === false || r.approved === undefined) && r.status !== 'approved');
      console.log(`✅ Approved reports: ${approvedReports.length}`);
      console.log(`⏳ Pending reports: ${pendingReports.length}`);

      // Debug: Show detailed report information
      console.log('📋 All reports details:');
      reports.forEach((report, index) => {
        console.log(`Report ${index + 1}:`, {
          id: report._id,
          approved: report.approved,
          activityId: report.activity?._id,
          activityTitle: report.activity?.title,
          contentFields: report.content?.map(c => `"${c.fieldName}": "${c.entry}"`) || [],
          hasContent: Array.isArray(report.content),
          contentLength: report.content?.length || 0
        });
      });

      // Debug: Show approved reports specifically
      if (approvedReports.length > 0) {
        console.log('✅ Approved reports details:');
        approvedReports.forEach((report, index) => {
          console.log(`Approved Report ${index + 1}:`, {
            id: report._id,
            activityId: report.activity?._id,
            activityTitle: report.activity?.title,
            contentFields: report.content?.map(c => `"${c.fieldName}": "${c.entry}"`) || []
          });
        });
      }
      console.log('📄 Reports data structure:', reports);

      // Log detailed report information
      reports.forEach((report: any, index: number) => {
        console.log(`📄 Report ${index + 1}:`, {
          id: report._id,
          activityId: report.activity?._id,
          activityTitle: report.activity?.title,
          submittedBy: report.submittedBy?.fullName,
          status: report.status,
          contentFields: report.content ? report.content.length : 0,
          amountSpent: report.amountSpent
        });

        if (report.content && report.content.length > 0) {
          console.log(`  📝 Content fields:`, report.content.map((c: any) => `${c.fieldName}: ${c.entry}`));
        }
      });

      // Calculate performance metrics
      const totalProjects = projects.length;
      const completedProjects = projects.filter(p => p.status === 'completed').length;
      const totalActivities = activities.length;
      const completedActivities = activities.filter(a => a.status === 'completed').length;

      // Calculate budget utilization
      const totalBudget = projects.reduce((sum, p) => sum + (p.initialBudget || 0), 0);
      const usedBudget = projects.reduce((sum, p) => sum + (p.usedBudget || 0), 0);

      // Calculate KPI Performance for each activity
      const activityPerformance = activities.map((activity: any) => {
        const activityReports = reports.filter((report: any) => report.activity?._id === activity._id);
        const approvedActivityReports = activityReports.filter(r => r.approved === true || r.status === 'approved');
        const latestReport = activityReports.length > 0 ? activityReports[activityReports.length - 1] : null;

        console.log(`🔍 Activity: ${activity.title}`);
        console.log(`📋 Total reports: ${activityReports.length}, Approved: ${approvedActivityReports.length}`);
        console.log(`📋 Has KPIs: ${activity.kpis?.length || 0}`);

        if (activity.kpis?.length > 0) {
          console.log(`🎯 KPIs for ${activity.title}:`, activity.kpis.map(k => k.name));

          // Debug KPI matching for each KPI
          activity.kpis.forEach((kpi: any) => {
            console.log(`\n🔍 Testing KPI: "${kpi.name}"`);
            console.log(`🎯 Target: ${kpi.target}`);

            if (approvedActivityReports.length > 0) {
              const latestReport = approvedActivityReports[approvedActivityReports.length - 1];
              console.log(`📋 Latest approved report content:`, latestReport.content);

              // Test exact match
              const exactMatch = latestReport.content?.find((content: any) => {
                if (!content.fieldName || content.entry === undefined || content.entry === null) return false;
                const fieldName = content.fieldName.toLowerCase().trim();
                const kpiName = kpi.name.toLowerCase().trim();
                return fieldName === kpiName;
              });

              if (exactMatch) {
                console.log(`✅ EXACT MATCH FOUND: "${exactMatch.fieldName}" = "${exactMatch.entry}"`);
                const numericValue = parseFloat(String(exactMatch.entry));
                console.log(`🔢 Numeric value: ${numericValue}, isValid: ${!isNaN(numericValue)}`);
              } else {
                console.log(`❌ No exact match found for "${kpi.name}"`);
                console.log(`📋 Available fields:`, latestReport.content?.map(c => `"${c.fieldName}"`) || []);
              }
            } else {
              console.log(`❌ No approved reports for this activity`);
            }
          });
        }

        // Extract KPIs from activity
        const kpis = activity.kpis || [];
        let kpiPerformanceData = [];
        let overallPerformance = 0;

        if (kpis.length > 0) {
          kpiPerformanceData = kpis.map((kpi: any) => {
            const targetValue = parseFloat(kpi.target) || 0;

            // Get matching details for this KPI
            const matchingDetails = getKPIMatchingDetails(kpi, activityReports, activity);
            const actualValue = matchingDetails.actualValue;

            // Calculate performance percentage
            let performanceRate = 0;
            if (targetValue > 0) {
              performanceRate = Math.min((actualValue / targetValue) * 100, 100);
            }

            return {
              name: kpi.name || kpi.indicator,
              target: targetValue,
              actual: actualValue,
              performanceRate: performanceRate,
              status: performanceRate >= 80 ? 'excellent' :
                     performanceRate >= 60 ? 'good' :
                     performanceRate >= 40 ? 'fair' : 'poor',
              description: kpi.description || '',
              matchingDetails: matchingDetails
            };
          });

          // Calculate overall performance for this activity
          overallPerformance = kpiPerformanceData.reduce((sum, kpi) => sum + kpi.performanceRate, 0) / kpiPerformanceData.length;
        }

        return {
          activityId: activity._id,
          activityTitle: activity.title,
          projectName: activity.project?.title || activity.project?.name,
          kpis: kpiPerformanceData,
          overallPerformance: overallPerformance,
          reportStatus: latestReport?.status || 'no-report',
          lastUpdated: latestReport?.createdAt || activity.createdAt
        };
      });

      // Calculate overall KPI performance across all activities
      const activitiesWithKPIPerformance = activityPerformance.filter(ap => ap.kpis.length > 0);
      const overallKPIPerformance = activitiesWithKPIPerformance.length > 0
        ? activitiesWithKPIPerformance.reduce((sum, ap) => sum + ap.overallPerformance, 0) / activitiesWithKPIPerformance.length
        : 0;

      // Collect all KPIs from activities for the KPIs array
      const allKPIs = activities.reduce((acc: any[], activity: any) => {
        if (activity.kpis && activity.kpis.length > 0) {
          console.log(`🎯 Found ${activity.kpis.length} KPIs for activity: ${activity.title}`);
          const activityKPIs = activity.kpis.map((kpi: any) => ({
            ...kpi,
            activityId: activity._id,
            activityTitle: activity.title,
            projectName: activity.project?.title || activity.project?.name
          }));
          acc.push(...activityKPIs);
        }
        return acc;
      }, []);

      console.log(`📊 Total KPIs collected: ${allKPIs.length}`);
      console.log(`📈 Activities with KPIs: ${activitiesWithKPIPerformance.length}`);
      console.log(`🎯 Overall KPI Performance: ${overallKPIPerformance.toFixed(2)}%`);

      setMeData({
        projects,
        activities,
        reports,
        kpis: allKPIs,
        activityPerformance,
        performance: {
          projectCompletion: totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0,
          activityCompletion: totalActivities > 0 ? (completedActivities / totalActivities) * 100 : 0,
          budgetUtilization: totalBudget > 0 ? (usedBudget / totalBudget) * 100 : 0,
          timelineAdherence: 75, // Placeholder - would need actual timeline data
          overallKPIPerformance
        },
        trends: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
          projectProgress: [10, 25, 40, 55, 70, 85],
          activityCompletion: [15, 30, 45, 60, 75, 90],
          budgetSpending: [5, 15, 30, 50, 70, 85]
        }
      });

      // Calculate evaluation metrics based on actual project data
      // For post-project evaluation, use completed projects instead of ongoing ones
      const completedProjectsList = projects.filter(p => p.status === 'completed');
      const completedActivitiesWithKPIs = activityPerformance.filter(ap => {
        return ap.kpis.length > 0 && meData.activities.some((a: any) =>
          a._id === ap.activityId &&
          completedProjectsList.some((p: any) => p._id === a.project?._id)
        );
      });

      // Also include ongoing projects for a more comprehensive evaluation
      const ongoingProjects = projects.filter(p => p.status === 'inprogress');
      const ongoingActivitiesWithKPIs = activityPerformance.filter(ap => {
        return ap.kpis.length > 0 && meData.activities.some((a: any) =>
          a._id === ap.activityId &&
          ongoingProjects.some((p: any) => p._id === a.project?._id)
        );
      });

      // Combine both completed and ongoing activities for comprehensive metrics
      const allActivitiesWithKPIs = [...completedActivitiesWithKPIs, ...ongoingActivitiesWithKPIs];

      if (allActivitiesWithKPIs.length > 0) {
        // Calculate metrics based on actual KPI performance
        const avgKPIPerformance = allActivitiesWithKPIs.reduce((sum, ap) => sum + ap.overallPerformance, 0) / allActivitiesWithKPIs.length;

        setEvaluationMetrics({
          effectiveness: Math.round(avgKPIPerformance * 0.95), // Slightly adjust for different aspects
          efficiency: Math.round(avgKPIPerformance * 1.05),
          impact: Math.round(avgKPIPerformance * 0.90),
          sustainability: Math.round(avgKPIPerformance * 0.85),
          relevance: Math.round(avgKPIPerformance * 1.10)
        });
      } else {
        // No projects with KPI data available
        setEvaluationMetrics({
          effectiveness: 0,
          efficiency: 0,
          impact: 0,
          sustainability: 0,
          relevance: 0
        });
      }

    } catch (error) {
      console.error('❌ Failed to fetch M&E data:', error);
      // Set empty state on error
      setMeData({
        projects: [],
        activities: [],
        reports: [],
        kpis: [],
        activityPerformance: [],
        performance: {
          projectCompletion: 0,
          activityCompletion: 0,
          budgetUtilization: 0,
          timelineAdherence: 0,
          overallKPIPerformance: 0
        },
        trends: {
          labels: [],
          projectProgress: [],
          activityCompletion: [],
          budgetSpending: []
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const getPerformanceColor = (value: number) => {
    if (value >= 80) return 'text-green-600 bg-green-100';
    if (value >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getPerformanceIcon = (value: number) => {
    if (value >= 80) return <ArrowTrendingUpIcon className="w-5 h-5" />;
    return <ArrowTrendingDownIcon className="w-5 h-5" />;
  };

  const getKPIStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'fair': return 'bg-yellow-100 text-yellow-800';
      case 'poor': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const generateKPIPerformancePDF = async (activityData?: any) => {
    setGeneratingPDF(true);
    try {
      // Import jsPDF and autoTable
      const { jsPDF } = await import('jspdf');
      const { default: autoTable } = await import('jspdf-autotable');

      const doc = new jsPDF();
    const currentDate = new Date().toLocaleDateString();

    // Header
    doc.setFontSize(20);
    doc.text('KPI Performance Report', 20, 20);
    doc.setFontSize(12);
    doc.text(`Generated on: ${currentDate}`, 20, 30);

    if (activityData) {
      // Single activity report
      doc.setFontSize(16);
      doc.text(`Activity: ${activityData.activityTitle}`, 20, 45);
      doc.text(`Project: ${activityData.projectName}`, 20, 55);
      doc.text(`Overall Performance: ${activityData.overallPerformance.toFixed(1)}%`, 20, 65);

      // KPI Table
      const kpiTableData = activityData.kpis.map((kpi: any) => [
        kpi.name,
        kpi.target.toString(),
        kpi.actual.toString(),
        `${kpi.performanceRate.toFixed(1)}%`,
        kpi.status.toUpperCase()
      ]);

      autoTable(doc, {
        head: [['KPI Name', 'Target', 'Actual', 'Performance', 'Status']],
        body: kpiTableData,
        startY: 75,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [34, 197, 94] }
      });
    } else {
      // Overall report
      doc.setFontSize(16);
      doc.text('Overall KPI Performance Summary', 20, 45);
      doc.text(`Overall KPI Performance: ${meData.performance.overallKPIPerformance.toFixed(1)}%`, 20, 55);

      // Activities performance table
      const activitiesTableData = meData.activityPerformance
        .filter((ap: any) => ap.kpis.length > 0)
        .map((ap: any) => [
          ap.activityTitle,
          ap.projectName,
          ap.kpis.length.toString(),
          `${ap.overallPerformance.toFixed(1)}%`,
          ap.reportStatus.toUpperCase()
        ]);

      autoTable(doc, {
        head: [['Activity', 'Project', 'KPIs Count', 'Performance', 'Report Status']],
        body: activitiesTableData,
        startY: 65,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [34, 197, 94] }
      });
    }

    // Save the PDF
    const fileName = activityData
      ? `KPI_Performance_${activityData.activityTitle.replace(/\s+/g, '_')}_${currentDate.replace(/\//g, '-')}.pdf`
      : `Overall_KPI_Performance_${currentDate.replace(/\//g, '-')}.pdf`;

    doc.save(fileName);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setGeneratingPDF(false);
    }
  };

  // Function to filter data based on selected project
  const getFilteredData = () => {
    if (selectedProject === 'all') {
      return {
        activities: meData.activities,
        activityPerformance: meData.activityPerformance,
        kpis: meData.kpis,
        projects: meData.projects,
        reports: meData.reports
      };
    }

    // Filter activities for selected project
    const filteredActivities = meData.activities.filter((activity: any) =>
      activity.project?._id === selectedProject
    );

    // Filter activity performance for selected project
    const filteredActivityPerformance = meData.activityPerformance.filter((ap: any) =>
      filteredActivities.some((activity: any) => activity._id === ap.activityId)
    );

    // Filter KPIs for selected project
    const filteredKPIs = meData.kpis.filter((kpi: any) =>
      filteredActivities.some((activity: any) => activity._id === kpi.activityId)
    );

    // Get selected project
    const filteredProjects = meData.projects.filter((project: any) =>
      project._id === selectedProject
    );

    // Filter reports for selected project activities
    const filteredReports = meData.reports.filter((report: any) =>
      filteredActivities.some((activity: any) => activity._id === report.activity?._id)
    );

    return {
      activities: filteredActivities,
      activityPerformance: filteredActivityPerformance,
      kpis: filteredKPIs,
      projects: filteredProjects,
      reports: filteredReports
    };
  };

  const generatePostProjectEvaluationPDF = async (project: any, finalKPIScore: number) => {
    setGeneratingPDF(true);
    try {
      // Import jsPDF and autoTable
      const { jsPDF } = await import('jspdf');
      const { default: autoTable } = await import('jspdf-autotable');

      const doc = new jsPDF();
    const currentDate = new Date().toLocaleDateString();

    // Header
    doc.setFontSize(20);
    doc.text('Post-Project Evaluation Report', 20, 20);
    doc.setFontSize(12);
    doc.text(`Generated on: ${currentDate}`, 20, 30);

    // Project Information
    doc.setFontSize(16);
    doc.text(`Project: ${project.title || project.name}`, 20, 45);
    doc.setFontSize(12);
    doc.text(`Description: ${project.description}`, 20, 55);
    doc.text(`Completion Date: ${project.endDate ? new Date(project.endDate).toLocaleDateString() : 'N/A'}`, 20, 65);
    doc.text(`Final KPI Score: ${finalKPIScore.toFixed(1)}%`, 20, 75);

    // Evaluation Criteria
    const evaluationData = [
      ['Effectiveness', `${evaluationMetrics.effectiveness}%`],
      ['Efficiency', `${evaluationMetrics.efficiency}%`],
      ['Impact', `${evaluationMetrics.impact}%`],
      ['Sustainability', `${evaluationMetrics.sustainability}%`],
      ['Relevance', `${evaluationMetrics.relevance}%`]
    ];

    autoTable(doc, {
      head: [['Evaluation Criteria', 'Score']],
      body: evaluationData,
      startY: 85,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [34, 197, 94] }
    });

    // Recommendations
    doc.setFontSize(14);
    doc.text('Recommendations:', 20, (doc as any).lastAutoTable.finalY + 20);
    doc.setFontSize(10);

    const recommendations = finalKPIScore >= 75
      ? ['Project achieved excellent results', 'Consider replicating approach in future projects', 'Document best practices for organizational learning']
      : finalKPIScore >= 50
      ? ['Project achieved moderate success', 'Identify areas for improvement', 'Implement lessons learned in future projects']
      : ['Project faced significant challenges', 'Conduct detailed analysis of failure points', 'Develop mitigation strategies for future projects'];

    recommendations.forEach((rec, index) => {
      doc.text(`• ${rec}`, 25, (doc as any).lastAutoTable.finalY + 35 + (index * 10));
    });

    // Save the PDF
    const fileName = `Post_Project_Evaluation_${project.title?.replace(/\s+/g, '_')}_${currentDate.replace(/\//g, '-')}.pdf`;
    doc.save(fileName);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setGeneratingPDF(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading M&E Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Monitoring & Evaluation</h1>
          <p className="text-gray-600 mt-1">Track project progress, evaluate performance, and monitor key indicators</p>
        </div>


      </div>

      <div className="flex justify-between items-center">
        <div></div>

        {/* Filters */}
        <div className="flex space-x-4">
          <select
            value={selectedProject}
            onChange={(e) => setSelectedProject(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
          >
            <option value="all">All Projects</option>
            {meData.projects.map((project: any) => (
              <option key={project._id} value={project._id}>
                {project.title || project.name}
              </option>
            ))}
          </select>

          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
            <option value="365">Last year</option>
          </select>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex justify-between max-w-2xl">
          {/* Performance Tab - Left */}
          <button
            onClick={() => setActiveTab('performance')}
            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'performance'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <ArrowTrendingUpIcon className="w-5 h-5 mr-2" />
            Performance
          </button>

          {/* KPI Tracking Tab - Center */}
          <button
            onClick={() => setActiveTab('kpi')}
            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'kpi'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <ChartBarIcon className="w-5 h-5 mr-2" />
            KPI Tracking
          </button>

          {/* Evaluation Tab - Right */}
          <button
            onClick={() => setActiveTab('evaluation')}
            className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'evaluation'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <DocumentTextIcon className="w-5 h-5 mr-2" />
            Evaluation
          </button>
        </nav>
      </div>



      {/* Performance Tab */}
      {activeTab === 'performance' && (
        <div className="space-y-6">
          {(() => {
            const filteredData = getFilteredData();

            return (
              <>
                {/* Show message if no activities at all */}
                {filteredData.activities.length === 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                    <ClipboardDocumentListIcon className="w-16 h-16 mx-auto mb-4 text-yellow-400" />
                    <h3 className="text-lg font-medium text-yellow-800 mb-2">
                      {selectedProject === 'all' ? 'No Activities Found' : 'No Activities Found for Selected Project'}
                    </h3>
                    <p className="text-yellow-700 mb-4">
                      {selectedProject === 'all'
                        ? "You don't have any activities assigned to your projects yet."
                        : "The selected project doesn't have any activities assigned yet."
                      }
                    </p>
                    <p className="text-sm text-yellow-600">
                      Activities are created during project planning and then assigned to field officers from your dashboard.
                    </p>
                  </div>
                )}

                {/* KPI Performance Overview */}
                {filteredData.activities.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex justify-between items-center mb-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">KPI Performance Tracking</h3>
                      <p className="text-sm text-gray-600">
                        {selectedProject === 'all'
                          ? 'Monitor activity performance through Key Performance Indicators'
                          : `Monitor KPI performance for ${filteredData.projects[0]?.title || filteredData.projects[0]?.name || 'selected project'}`
                        }
                      </p>
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={async () => await generateKPIPerformancePDF()}
                        disabled={generatingPDF}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <DocumentTextIcon className="w-4 h-4" />
                        <span>{generatingPDF ? 'Generating...' : `Download ${selectedProject === 'all' ? 'Overall' : 'Project'} Report`}</span>
                      </button>
                    </div>
                  </div>

                  {/* Overall KPI Performance Card */}
                  {(() => {
                    // Calculate filtered KPI performance
                    const activitiesWithKPIPerformance = filteredData.activityPerformance.filter(ap => ap.kpis.length > 0);
                    const filteredOverallKPIPerformance = activitiesWithKPIPerformance.length > 0
                      ? activitiesWithKPIPerformance.reduce((sum, ap) => sum + ap.overallPerformance, 0) / activitiesWithKPIPerformance.length
                      : 0;

                    return (
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900">
                              {selectedProject === 'all' ? 'Overall KPI Performance' : 'Project KPI Performance'}
                            </h4>
                            <p className="text-sm text-gray-600">
                              {selectedProject === 'all'
                                ? 'Average performance across all activities'
                                : `Average performance for ${filteredData.projects[0]?.title || filteredData.projects[0]?.name || 'this project'}`
                              }
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="text-3xl font-bold text-indigo-600">
                              {filteredOverallKPIPerformance.toFixed(1)}%
                            </div>
                            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPerformanceColor(filteredOverallKPIPerformance)}`}>
                              {getPerformanceIcon(filteredOverallKPIPerformance)}
                              <span className="ml-1">
                                {filteredOverallKPIPerformance >= 80 ? 'Excellent' :
                                 filteredOverallKPIPerformance >= 60 ? 'Good' :
                                 filteredOverallKPIPerformance >= 40 ? 'Fair' : 'Needs Improvement'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })()}

                  {/* KPI Performance Visualizations */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    {/* KPI Performance Chart */}
                    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">KPI Performance Distribution</h4>
                      <div className="h-64 flex items-center justify-center">
                        {filteredData.activityPerformance.filter((ap: any) => ap.kpis.length > 0).length > 0 ? (
                          <div className="w-full h-full">
                            <div className="space-y-3">
                              {['Excellent (≥80%)', 'Good (60-79%)', 'Fair (40-59%)', 'Poor (<40%)'].map((category, index) => {
                                const ranges = [
                                  { min: 80, max: 100, color: 'bg-green-500', count: 0 },
                                  { min: 60, max: 79, color: 'bg-blue-500', count: 0 },
                                  { min: 40, max: 59, color: 'bg-yellow-500', count: 0 },
                                  { min: 0, max: 39, color: 'bg-red-500', count: 0 }
                                ];

                                const activitiesInRange = filteredData.activityPerformance.filter((ap: any) => {
                                  const range = ranges[index];
                                  return ap.kpis.length > 0 && ap.overallPerformance >= range.min && ap.overallPerformance <= range.max;
                                }).length;

                                const totalActivities = filteredData.activityPerformance.filter((ap: any) => ap.kpis.length > 0).length;
                                const percentage = totalActivities > 0 ? (activitiesInRange / totalActivities) * 100 : 0;

                                return (
                                  <div key={category} className="flex items-center">
                                    <div className="w-32 text-sm text-gray-600">{category}</div>
                                    <div className="flex-1 mx-4">
                                      <div className="w-full bg-gray-200 rounded-full h-4">
                                        <div
                                          className={`h-4 rounded-full ${ranges[index].color} transition-all duration-300`}
                                          style={{ width: `${percentage}%` }}
                                        ></div>
                                      </div>
                                    </div>
                                    <div className="w-16 text-sm text-gray-900 text-right">
                                      {activitiesInRange} ({percentage.toFixed(1)}%)
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        ) : (
                          <div className="text-center text-gray-500">
                            <ChartBarIcon className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                            <p className="text-lg font-medium mb-2">No KPI data available</p>
                            <p className="text-sm">
                              {selectedProject === 'all'
                                ? 'KPIs are set when assigning activities to field officers.'
                                : 'This project has no activities with KPIs assigned yet.'
                              }
                            </p>
                            <p className="text-sm">Once activities are assigned with KPIs and reports are submitted, performance data will appear here.</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Performance Trends */}
                    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        {selectedProject === 'all' ? 'Performance Trends by Project' : 'Activity Performance Breakdown'}
                      </h4>
                      <div className="h-64 flex items-center justify-center">
                        {filteredData.activityPerformance.filter((ap: any) => ap.kpis.length > 0).length > 0 ? (
                          <div className="w-full h-full">
                            <div className="space-y-4">
                              {selectedProject === 'all' ? (
                                /* Average Performance by Project */
                                filteredData.projects.slice(0, 5).map((project: any) => {
                                  const projectActivities = filteredData.activityPerformance.filter((ap: any) =>
                                    filteredData.activities.some((a: any) => a._id === ap.activityId && a.project?._id === project._id)
                                  );
                                  const avgPerformance = projectActivities.length > 0
                                    ? projectActivities.reduce((sum: number, ap: any) => sum + ap.overallPerformance, 0) / projectActivities.length
                                    : 0;

                                  return (
                                    <div key={project._id} className="flex items-center">
                                      <div className="w-32 text-sm text-gray-600 truncate" title={project.title || project.name}>
                                        {(project.title || project.name).substring(0, 15)}...
                                      </div>
                                      <div className="flex-1 mx-4">
                                        <div className="w-full bg-gray-200 rounded-full h-3">
                                          <div
                                            className={`h-3 rounded-full transition-all duration-300 ${
                                              avgPerformance >= 80 ? 'bg-green-500' :
                                              avgPerformance >= 60 ? 'bg-blue-500' :
                                              avgPerformance >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                                            }`}
                                            style={{ width: `${Math.min(avgPerformance, 100)}%` }}
                                          ></div>
                                        </div>
                                      </div>
                                      <div className="w-16 text-sm text-gray-900 text-right">
                                        {avgPerformance.toFixed(1)}%
                                      </div>
                                    </div>
                                  );
                                })
                              ) : (
                                /* Individual Activity Performance for Selected Project */
                                filteredData.activityPerformance.slice(0, 5).map((activity: any) => (
                                  <div key={activity.activityId} className="flex items-center">
                                    <div className="w-32 text-sm text-gray-600 truncate" title={activity.activityTitle}>
                                      {activity.activityTitle.substring(0, 15)}...
                                    </div>
                                    <div className="flex-1 mx-4">
                                      <div className="w-full bg-gray-200 rounded-full h-3">
                                        <div
                                          className={`h-3 rounded-full transition-all duration-300 ${
                                            activity.overallPerformance >= 80 ? 'bg-green-500' :
                                            activity.overallPerformance >= 60 ? 'bg-blue-500' :
                                            activity.overallPerformance >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                                          }`}
                                          style={{ width: `${Math.min(activity.overallPerformance, 100)}%` }}
                                        ></div>
                                      </div>
                                    </div>
                                    <div className="w-16 text-sm text-gray-900 text-right">
                                      {activity.overallPerformance.toFixed(1)}%
                                    </div>
                                  </div>
                                ))
                              )}
                            </div>
                          </div>
                        ) : (
                          <div className="text-center text-gray-500">
                            <ArrowTrendingUpIcon className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                            <p>No trend data available</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Conditional Table Display */}
                  <div className="overflow-x-auto">
                    {selectedProject === 'all' ? (
                      /* Projects Overview Table */
                      <>
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activities</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total KPIs</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Performance</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {filteredData.projects.map((project: any) => {
                              // Calculate project-level metrics
                              const projectActivities = filteredData.activityPerformance.filter((ap: any) =>
                                filteredData.activities.some((a: any) => a._id === ap.activityId && a.project?._id === project._id)
                              );
                              const totalKPIs = projectActivities.reduce((sum: number, activity: any) => sum + activity.kpis.length, 0);
                              const avgPerformance = projectActivities.length > 0
                                ? projectActivities.reduce((sum: number, activity: any) => sum + activity.overallPerformance, 0) / projectActivities.length
                                : 0;

                              return (
                                <tr key={project._id} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{project.title || project.name}</div>
                                    <div className="text-sm text-gray-500">{project.location?.name || 'No location'}</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {projectActivities.length}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {totalKPIs}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                        <div
                                          className={`h-2 rounded-full ${
                                            avgPerformance >= 80 ? 'bg-green-500' :
                                            avgPerformance >= 60 ? 'bg-blue-500' :
                                            avgPerformance >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                                          }`}
                                          style={{ width: `${Math.min(avgPerformance, 100)}%` }}
                                        ></div>
                                      </div>
                                      <span className="text-sm font-medium text-gray-900">
                                        {avgPerformance.toFixed(1)}%
                                      </span>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                                      avgPerformance >= 80 ? 'bg-green-100 text-green-800' :
                                      avgPerformance >= 60 ? 'bg-blue-100 text-blue-800' :
                                      avgPerformance >= 40 ? 'bg-yellow-100 text-yellow-800' :
                                      totalKPIs === 0 ? 'bg-gray-100 text-gray-800' :
                                      'bg-red-100 text-red-800'
                                    }`}>
                                      {totalKPIs === 0 ? 'No KPIs' :
                                       avgPerformance >= 80 ? 'Excellent' :
                                       avgPerformance >= 60 ? 'Good' :
                                       avgPerformance >= 40 ? 'Fair' : 'Poor'}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button
                                      onClick={() => setSelectedProject(project._id)}
                                      className="text-blue-600 hover:text-blue-900"
                                    >
                                      View Details
                                    </button>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>

                        {filteredData.projects.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            <p>No projects found.</p>
                            <p className="text-sm mt-2">Projects will appear here once they are assigned to you.</p>
                          </div>
                        )}
                      </>
                    ) : (
                      /* Activities KPI Performance Table */
                      <>
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KPIs Count</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {filteredData.activityPerformance.filter((ap: any) => ap.kpis.length > 0).map((activity: any) => (
                              <tr key={activity.activityId} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm font-medium text-gray-900">{activity.activityTitle}</div>
                                  <div className="text-sm text-gray-500">Last updated: {new Date(activity.lastUpdated).toLocaleDateString()}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {activity.kpis.length}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="flex items-center">
                                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                      <div
                                        className={`h-2 rounded-full ${
                                          activity.overallPerformance >= 80 ? 'bg-green-500' :
                                          activity.overallPerformance >= 60 ? 'bg-blue-500' :
                                          activity.overallPerformance >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                                        }`}
                                        style={{ width: `${Math.min(activity.overallPerformance, 100)}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-sm font-medium text-gray-900">
                                      {activity.overallPerformance.toFixed(1)}%
                                    </span>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                                    activity.overallPerformance >= 80 ? 'bg-green-100 text-green-800' :
                                    activity.overallPerformance >= 60 ? 'bg-blue-100 text-blue-800' :
                                    activity.overallPerformance >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {activity.overallPerformance >= 80 ? 'Excellent' :
                                     activity.overallPerformance >= 60 ? 'Good' :
                                     activity.overallPerformance >= 40 ? 'Fair' : 'Poor'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                  <div className="flex space-x-2">
                                    <button
                                      onClick={() => setSelectedActivityForKPI(activity)}
                                      className="text-blue-600 hover:text-blue-900"
                                    >
                                      <EyeIcon className="w-4 h-4" />
                                    </button>
                                    <button
                                      onClick={async () => await generateKPIPerformancePDF(activity)}
                                      className="text-green-600 hover:text-green-900"
                                    >
                                      <DocumentTextIcon className="w-4 h-4" />
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>

                        {filteredData.activityPerformance.filter((ap: any) => ap.kpis.length > 0).length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            <p>No activities with KPIs found for this project.</p>
                            <p className="text-sm mt-2">KPIs are set during activity assignment and tracked through reports.</p>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
                )}
              </>
            );
          })()}
        </div>
      )}

      {/* KPI Tracking Tab */}
      {activeTab === 'kpi' && (
        <div className="space-y-6">
          {(() => {
            const filteredData = getFilteredData();

            // Calculate filtered metrics
            const filteredExcellentKPIs = filteredData.activityPerformance.reduce((count, activity) =>
              count + activity.kpis.filter((kpi: any) => kpi.status === 'excellent').length, 0
            );

            const filteredPoorKPIs = filteredData.activityPerformance.reduce((count, activity) =>
              count + activity.kpis.filter((kpi: any) => kpi.status === 'poor').length, 0
            );

            const activitiesWithKPIPerformance = filteredData.activityPerformance.filter(ap => ap.kpis.length > 0);
            const filteredAvgPerformance = activitiesWithKPIPerformance.length > 0
              ? activitiesWithKPIPerformance.reduce((sum, ap) => sum + ap.overallPerformance, 0) / activitiesWithKPIPerformance.length
              : 0;

            return (
              <>
                {/* KPI Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ChartBarIcon className="h-8 w-8 text-green-600" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            {selectedProject === 'all' ? 'Total KPIs' : 'Project KPIs'}
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">{filteredData.kpis.length}</dd>
                        </dl>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <CheckCircleIcon className="h-8 w-8 text-green-600" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">Excellent KPIs</dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {filteredExcellentKPIs}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">Poor KPIs</dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {filteredPoorKPIs}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ArrowTrendingUpIcon className="h-8 w-8 text-blue-600" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">Avg Performance</dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {filteredAvgPerformance.toFixed(1)}%
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>



                {/* Detailed KPI Tracking Table */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-100">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Detailed KPI Performance</h3>
                    <p className="text-sm text-gray-600">
                      {selectedProject === 'all'
                        ? 'Track individual KPI performance across all activities'
                        : `Track individual KPI performance for ${filteredData.projects[0]?.title || filteredData.projects[0]?.name || 'selected project'}`
                      }
                    </p>
                  </div>

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KPI Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                          {selectedProject === 'all' && (
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                          )}
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Match Type</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredData.activityPerformance.filter(activity => activity.kpis.length > 0).map((activity) =>
                          activity.kpis.map((kpi: any, kpiIndex: number) => (
                            <tr key={`${activity.activityId}-${kpiIndex}`} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{kpi.name}</div>
                                {kpi.description && (
                                  <div className="text-sm text-gray-500">{kpi.description}</div>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {activity.activityTitle}
                              </td>
                              {selectedProject === 'all' && (
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {activity.projectName}
                                </td>
                              )}
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {kpi.target}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {kpi.actual.toFixed(2)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                    <div
                                      className={`h-2 rounded-full ${
                                        kpi.status === 'excellent' ? 'bg-green-500' :
                                        kpi.status === 'good' ? 'bg-blue-500' :
                                        kpi.status === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
                                      }`}
                                      style={{ width: `${Math.min(kpi.performanceRate, 100)}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-sm text-gray-600">{kpi.performanceRate.toFixed(1)}%</span>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                  kpi.matchingDetails?.matchType === 'Exact Match' ? 'bg-green-100 text-green-800' :
                                  kpi.matchingDetails?.matchType === 'Partial Match' ? 'bg-blue-100 text-blue-800' :
                                  kpi.matchingDetails?.matchType === 'Target-based Match' ? 'bg-purple-100 text-purple-800' :
                                  kpi.matchingDetails?.matchType === 'Generic Match' ? 'bg-yellow-100 text-yellow-800' :
                                  kpi.matchingDetails?.matchType === 'Calculated Proxy' ? 'bg-orange-100 text-orange-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {kpi.matchingDetails?.matchType || 'Unknown'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  kpi.status === 'excellent' ? 'bg-green-100 text-green-800' :
                                  kpi.status === 'good' ? 'bg-blue-100 text-blue-800' :
                                  kpi.status === 'fair' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                                }`}>
                                  {kpi.status.toUpperCase()}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button
                                  onClick={() => {
                                    const activityReports = filteredData.reports.filter((report: any) => report.activity?._id === activity.activityId);
                                    const matchingDetails = getKPIMatchingDetails(kpi, activityReports, activity);
                                    setSelectedKPIForDetails({
                                      ...kpi,
                                      activity: activity,
                                      matchingDetails: matchingDetails
                                    });
                                    setShowKPIDetails(true);
                                  }}
                                  className="text-blue-600 hover:text-blue-900 transition-colors"
                                >
                                  View Details
                                </button>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>

                    {filteredData.activityPerformance.filter(activity => activity.kpis.length > 0).length === 0 && (
                      <div className="text-center py-12">
                        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No KPIs Found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          {selectedProject === 'all'
                            ? 'Activities need to have KPIs defined to track performance.'
                            : 'This project has no activities with KPIs defined yet.'
                          }
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </>
            );
          })()}
        </div>
      )}

      {/* Evaluation Tab */}
      {activeTab === 'evaluation' && (
        <div className="space-y-6">
          {(() => {
            const filteredData = getFilteredData();

            return (
              <>
                {/* Evaluation Type Selector */}
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {selectedProject === 'all' ? 'Post-Project Evaluation' : 'Project Evaluation'}
                    </h3>
                  </div>

                  {/* Post-Project Evaluation */}
                  {evaluationType === 'post-project' && (
                    <div className="space-y-6">
                      <div className="bg-green-50 rounded-lg p-4">
                        <h4 className="text-lg font-semibold text-green-900 mb-2">
                          {selectedProject === 'all' ? 'Post-Project Evaluation' : 'Project Evaluation'}
                        </h4>
                        {selectedProject !== 'all' && (
                          <p className="text-sm text-green-700">
                            Evaluation for: {filteredData.projects[0]?.title || filteredData.projects[0]?.name || 'Selected Project'}
                          </p>
                        )}
                      </div>

                      {/* Completed Projects Evaluation */}
                      <div className="bg-white rounded-lg border border-gray-100">
                        <div className="px-6 py-4 border-b border-gray-200">
                          <h4 className="text-lg font-semibold text-gray-900">
                            {selectedProject === 'all' ? 'Completed Projects Assessment' : 'Project Assessment'}
                          </h4>
                        </div>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Project</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Completion Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Final KPI Score</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Impact Assessment</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sustainability</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {filteredData.projects.filter((p: any) => p.status === 'completed').map((project: any) => {
                                const projectActivities = filteredData.activityPerformance.filter((ap: any) =>
                                  filteredData.activities.some((a: any) => a._id === ap.activityId && a.project?._id === project._id)
                                );
                                const finalKPIScore = projectActivities.length > 0
                                  ? projectActivities.reduce((sum: number, ap: any) => sum + ap.overallPerformance, 0) / projectActivities.length
                                  : 0;

                                return (
                                  <tr key={project._id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <div className="text-sm font-medium text-gray-900">{project.title || project.name}</div>
                                      <div className="text-sm text-gray-500">{project.description}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                      {project.endDate ? new Date(project.endDate).toLocaleDateString() : 'N/A'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                                        finalKPIScore >= 80 ? 'bg-green-100 text-green-800' :
                                        finalKPIScore >= 60 ? 'bg-blue-100 text-blue-800' :
                                        finalKPIScore >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                                      }`}>
                                        {finalKPIScore.toFixed(1)}%
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                                        finalKPIScore >= 75 ? 'bg-green-100 text-green-800' :
                                        finalKPIScore >= 50 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                                      }`}>
                                        {finalKPIScore >= 75 ? 'High Impact' : finalKPIScore >= 50 ? 'Moderate Impact' : 'Low Impact'}
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                                        finalKPIScore >= 70 ? 'bg-green-100 text-green-800' :
                                        finalKPIScore >= 50 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                                      }`}>
                                        {finalKPIScore >= 70 ? 'Sustainable' : finalKPIScore >= 50 ? 'Partially Sustainable' : 'At Risk'}
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                      <button
                                        onClick={async () => await generatePostProjectEvaluationPDF(project, finalKPIScore)}
                                        className="text-green-600 hover:text-green-900"
                                      >
                                        <DocumentTextIcon className="w-4 h-4" />
                                      </button>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>

                          {filteredData.projects.filter((p: any) => p.status === 'completed').length === 0 && (
                            <div className="text-center py-12">
                              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                              <h3 className="mt-2 text-sm font-medium text-gray-900">
                                {selectedProject === 'all' ? 'No Completed Projects' : 'Project Not Completed'}
                              </h3>
                              <p className="mt-1 text-sm text-gray-500">
                                {selectedProject === 'all'
                                  ? 'No projects have been completed yet for evaluation.'
                                  : 'This project has not been completed yet.'
                                }
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Post-Project Evaluation Summary */}
                      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">
                          {selectedProject === 'all' ? 'Post-Project Evaluation Summary' : 'Project Evaluation Summary'}
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                              {filteredData.projects.filter((p: any) => p.status === 'completed').length}
                            </div>
                            <div className="text-sm text-gray-600">
                              {selectedProject === 'all' ? 'Completed Projects' : 'Project Status'}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                              {evaluationMetrics.impact}%
                            </div>
                            <div className="text-sm text-gray-600">Average Impact Score</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">
                              {evaluationMetrics.sustainability}%
                            </div>
                            <div className="text-sm text-gray-600">Sustainability Rate</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </>
            );
          })()}
        </div>
      )}



      {/* KPI Details Modal */}
      {selectedActivityForKPI && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">KPI Performance Details</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Activity: {selectedActivityForKPI.activityTitle} | Project: {selectedActivityForKPI.projectName}
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={async () => await generateKPIPerformancePDF(selectedActivityForKPI)}
                    className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
                  >
                    Download PDF
                  </button>
                  <button
                    onClick={() => setSelectedActivityForKPI(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Overall Performance Summary */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">Overall Performance</h4>
                    <p className="text-sm text-gray-600">Average across all KPIs</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-indigo-600">
                      {selectedActivityForKPI.overallPerformance.toFixed(1)}%
                    </div>
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getKPIStatusColor(
                      selectedActivityForKPI.overallPerformance >= 80 ? 'excellent' :
                      selectedActivityForKPI.overallPerformance >= 60 ? 'good' :
                      selectedActivityForKPI.overallPerformance >= 40 ? 'fair' : 'poor'
                    )}`}>
                      {selectedActivityForKPI.overallPerformance >= 80 ? 'Excellent' :
                       selectedActivityForKPI.overallPerformance >= 60 ? 'Good' :
                       selectedActivityForKPI.overallPerformance >= 40 ? 'Fair' : 'Poor'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Individual KPI Performance */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-gray-900">Individual KPI Performance</h4>
                {selectedActivityForKPI.kpis.map((kpi: any, index: number) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900">{kpi.name}</h5>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full font-semibold ${getKPIStatusColor(kpi.status)}`}>
                        {kpi.status.toUpperCase()}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Target</p>
                        <p className="text-lg font-semibold text-blue-600">{kpi.target}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Actual</p>
                        <p className="text-lg font-semibold text-green-600">{kpi.actual}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Performance</p>
                        <p className="text-lg font-semibold text-indigo-600">{kpi.performanceRate.toFixed(1)}%</p>
                      </div>
                    </div>

                    {/* Performance Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className={`h-3 rounded-full transition-all duration-300 ${
                          kpi.performanceRate >= 80 ? 'bg-green-500' :
                          kpi.performanceRate >= 60 ? 'bg-blue-500' :
                          kpi.performanceRate >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(kpi.performanceRate, 100)}%` }}
                      ></div>
                    </div>

                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>0%</span>
                      <span>100%</span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Report Information */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-gray-900 mb-2">Report Information</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Report Status:</span>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-semibold ${
                      selectedActivityForKPI.reportStatus === 'approved' ? 'bg-green-100 text-green-800' :
                      selectedActivityForKPI.reportStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedActivityForKPI.reportStatus.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Last Updated:</span>
                    <span className="ml-2 text-gray-900">
                      {new Date(selectedActivityForKPI.lastUpdated).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* KPI Details Modal */}
      {showKPIDetails && selectedKPIForDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">KPI Matching Details</h2>
                  <p className="text-gray-600 mt-1">How the actual value was determined for this KPI</p>
                </div>
                <button
                  onClick={() => setShowKPIDetails(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              {/* KPI Information */}
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">KPI Information</h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-600">Name:</span>
                        <p className="text-gray-900">{selectedKPIForDetails.name}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-600">Target:</span>
                        <p className="text-gray-900">{selectedKPIForDetails.target}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-600">Description:</span>
                        <p className="text-gray-900">{selectedKPIForDetails.description || 'No description provided'}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Information</h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-600">Activity:</span>
                        <p className="text-gray-900">{selectedKPIForDetails.activity.activityTitle}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-600">Project:</span>
                        <p className="text-gray-900">{selectedKPIForDetails.activity.projectName}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-600">Performance:</span>
                        <div className="flex items-center mt-1">
                          <div className="w-32 bg-gray-200 rounded-full h-3 mr-3">
                            <div
                              className={`h-3 rounded-full ${
                                selectedKPIForDetails.status === 'excellent' ? 'bg-green-500' :
                                selectedKPIForDetails.status === 'good' ? 'bg-blue-500' :
                                selectedKPIForDetails.status === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.min(selectedKPIForDetails.performanceRate, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900">
                            {selectedKPIForDetails.performanceRate.toFixed(1)}% ({selectedKPIForDetails.status.toUpperCase()})
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>



              {/* Close Button */}
              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowKPIDetails(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
