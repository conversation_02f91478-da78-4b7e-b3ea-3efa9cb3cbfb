const express = require("express");
const helmet = require("helmet");
const ratelimit = require("express-rate-limit");
const cors = require("cors");
const path = require("path");
const cookieParser = require("cookie-parser");
const SchedulerService = require("./services/schedulerService");
const MonitoringService = require("./services/monitoringService");

require("dotenv").config();
require("./models/roles.model");

// Debug environment variables
console.log("🔧 Environment check:");
console.log("🔧 NODE_ENV:", process.env.NODE_ENV);
console.log("🔧 PORT:", process.env.PORT);
console.log("🔧 DATABASE_URL:", process.env.DATABASE_URL ? "Set" : "Not set");
console.log("🔧 JWT_SECRET:", process.env.JWT_SECRET ? "Set" : "Not set");
console.log("🔧 FRONTEND_URL:", process.env.FRONTEND_URL || "Using default");
console.log("🔧 JWT_SECRET length:", process.env.JWT_SECRET?.length || 0);

const userRoutes = require("./routes/user.routes");
const districtRoutes = require("./routes/district.routes");
const rolesRoutes = require("./routes/roles.routes");
const departmentRoutes = require("./routes/department.routes");
const projectRoutes = require("./routes/project.routes");

const projectReportRoutes = require("./routes/activityReport.routes");
const dashboardRoutes = require("./routes/dashboard.routes");
const fieldOfficerRoutes = require("./routes/fieldOfficer.routes");
const notificationsRoutes = require("./routes/notifications.routes");
const authRoutes = require("./routes/auth.routes");
const projectPlanRoutes = require("./routes/projectPlan.routes");
const recentActivitiesRoutes = require("./routes/recentActivities.routes");
const reportsRoutes = require("./routes/reports.routes");
const accountantRoutes = require("./routes/accountant.routes");
const managerRoutes = require("./routes/manager.routes");
const seniorManagerRoutes = require("./routes/seniorManager.routes");
const adminRoutes = require("./routes/admin.routes");
const projectTrackingRoutes = require("./routes/projectTracking.routes");
const teamRoutes = require("./routes/team.routes");



const app = express();

app.use(cors({
  origin: [
    process.env.FRONTEND_URL || "http://localhost:3000",
    "http://************:3000",
    "http://************",
    "http://*************:3000",
    "http://localhost:3001",
    "http://localhost:3002",
    "http://localhost:3003"
  ],
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());
app.use(helmet());
app.set("trust proxy", 1);
app.use("/uploads", express.static(path.join(__dirname, "uploads")));
console.log("Static uploads path:", path.join(__dirname, "uploads"));

const limiter = ratelimit({
  max: 100,
  windoMs: 60 * 60 * 1000,
  message: "Server is busy please try again later",
});

process.removeAllListeners("warning");

app.use("/api", limiter);


app.use("/api/v1/auth", authRoutes);
app.use("/api/v1/user", userRoutes);
app.use("/api/v1/district", districtRoutes);
app.use("/api/v1/roles", rolesRoutes);
app.use("/api/v1/department", departmentRoutes);
app.use("/api/v1/project", projectRoutes);

app.use("/api/v1/project-report", projectReportRoutes);
app.use("/api/v1/dashboard", dashboardRoutes);
app.use("/api/v1/field-officer", fieldOfficerRoutes);
app.use("/api/v1/notifications", notificationsRoutes);
app.use('/api/v1/project-plan', projectPlanRoutes);
app.use("/api/v1/recentActivities", recentActivitiesRoutes);
app.use("/api/v1/reports", reportsRoutes);
app.use("/api/v1/accountant", accountantRoutes);
app.use("/api/v1/manager", managerRoutes);
app.use("/api/v1/senior-manager", seniorManagerRoutes);
app.use("/api/v1/admin", adminRoutes);
app.use("/api/v1/project-tracking", projectTrackingRoutes);
app.use("/api/v1/team", teamRoutes);


// Initialize scheduler for notifications
SchedulerService.init();

// Initialize comprehensive monitoring system
MonitoringService.initializeMonitoring();

// Initialize scheduler service for cron jobs
SchedulerService.init();

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// 404 handler
app.all('*', (req, res) => {
  res.status(404).json({
    status: 'failed',
    message: `Route ${req.originalUrl} not found`
  });
});

module.exports = app;
