'use client';
import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { CurrencyDollarIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/solid';

// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { api } from '@/services/api';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// Dynamically import Chart.js components
const DoughnutChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Doughnut), { ssr: false });
const BarChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Bar), { ssr: false });

interface ProjectBudget {
  id: string;
  name: string;
  totalBudget: number;
  spent: number;
  remaining: number;
  status: 'on_track' | 'over_budget' | 'under_budget';
}

interface ActivityBudget {
  id: string;
  name: string;
  allocatedBudget: number;
  spentBudget: number;
  remainingBudget: number;
  progress: number;
}

export default function BudgetPage() {
  const [selectedProject, setSelectedProject] = useState('all');
  const [budgets, setBudgets] = useState<ProjectBudget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activityBudgets, setActivityBudgets] = useState<ActivityBudget[]>([]);
  const [loadingActivities, setLoadingActivities] = useState(false);
  const [usingFallbackData, setUsingFallbackData] = useState(false);

  useEffect(() => {
    async function fetchBudgets() {
      setLoading(true);
      setError(null);
      try {
        // Use the manager budget overview endpoint that calculates spending from approved reports
        try {
          const budgetRes = await api.get('/api/v1/manager/budget/overview');

          if (budgetRes.data && budgetRes.data.status === 'success') {
            const budgetData = budgetRes.data.data;

            // Map the budget distribution data to our ProjectBudget interface
            const mapped = budgetData.budgetDistribution.map((project: any, index: number) => {
              const totalBudget = project.budget || 0;
              const spent = project.spent || 0;
              const remaining = project.remaining || 0;

              return {
                id: project.id || project._id || `project-${index}`,
                name: project.name || `Project ${index + 1}`,
                totalBudget,
                spent,
                remaining,
                status: spent > totalBudget ? 'over_budget' :
                       spent < totalBudget * 0.5 ? 'under_budget' : 'on_track',
              };
            }) as ProjectBudget[];

            setBudgets(mapped);
            setUsingFallbackData(false);
            return; // Exit early if successful
          }
        } catch (budgetError: any) {
          console.warn('Manager budget overview endpoint failed:', budgetError);

          // If it's a 403 error, the user might not have the right permissions
          if (budgetError.response?.status === 403) {
            console.warn('403 Forbidden - User may not have project manager role or may not be assigned to any projects');
          }

          // Continue to fallback method
        }

        // Fallback method if budget overview endpoint fails
        // Fallback to projects endpoint if budget overview fails
        console.log('Using fallback method to fetch project data...');

        try {
          const res = await api.getProjects();
          console.log('Projects data received:', res.data);

          // Simple mapping without complex report calculations to avoid additional API calls that might fail
          const mapped = res.data.map((p: any, index: number) => {
            const totalBudget = p.budget?.total || p.initialBudget || 0;
            const spent = p.budget?.used || p.usedBudget || 0; // Use existing project data
            const remaining = totalBudget - spent;

            return {
              id: p._id || p.id || `fallback-project-${index}`,
              name: p.title || p.name || `Project ${index + 1}`,
              totalBudget,
              spent,
              remaining,
              status: spent > totalBudget ? 'over_budget' :
                     spent < totalBudget * 0.5 ? 'under_budget' : 'on_track',
            };
          });

          console.log('Mapped budget data:', mapped);
          setBudgets(mapped);
          setUsingFallbackData(true);

        } catch (projectError: any) {
          console.error('Failed to fetch projects:', projectError);

          // If even the basic projects endpoint fails, show empty state
          setBudgets([]);
          throw new Error(`Failed to fetch project data: ${projectError.message}`);
        }
      } catch (err: any) {
        console.error('Budget fetch error:', err);

        // Provide more specific error messages
        let errorMessage = 'Failed to fetch budget data';
        if (err.response?.status === 403) {
          errorMessage = 'Access denied. Please ensure you are logged in as a project manager.';
        } else if (err.response?.status === 401) {
          errorMessage = 'Authentication required. Please log in again.';
        } else if (err.response?.status === 500) {
          errorMessage = 'Server error. Please try again later.';
        } else if (err.message) {
          errorMessage = err.message;
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    }
    fetchBudgets();
  }, []);

  // Fetch activity budgets when a project is selected
  useEffect(() => {
    if (selectedProject && selectedProject !== 'all') {
      fetchActivityBudgets(selectedProject);
    } else {
      setActivityBudgets([]);
    }
  }, [selectedProject]);

  const fetchActivityBudgets = async (projectId: string) => {
    setLoadingActivities(true);
    try {
      // Fetch activities for the selected project
      const activitiesRes = await api.get(`/api/v1/manager/activities?projectId=${projectId}`);
      let activities = [];

      if (activitiesRes.data && activitiesRes.data.status === 'success') {
        activities = activitiesRes.data.data || [];
      } else if (activitiesRes.data && Array.isArray(activitiesRes.data)) {
        activities = activitiesRes.data;
      }

      // Fetch activity reports to get actual spending data
      const reportsRes = await api.get(`/api/v1/manager/reports`);
      let reports = [];

      if (reportsRes.data && reportsRes.data.status === 'success') {
        reports = reportsRes.data.data || [];
      } else if (reportsRes.data && Array.isArray(reportsRes.data)) {
        reports = reportsRes.data;
      }

      console.log('🔍 DEBUG: All Reports:', reports.length);
      console.log('🔍 DEBUG: All Activities:', activities.length);

      // Debug: Show all activities
      console.log('🎯 All Activities for this project:');
      activities.forEach((activity: any, index: number) => {
        console.log(`${index + 1}. ${activity.title} (ID: ${activity._id}) - Budget: ${activity.budget || activity.allocatedBudget || 0}`);
      });

      // Debug: Show all reports with their details
      reports.forEach((report: any, index: number) => {
        console.log(`📋 Report ${index + 1}:`, {
          id: report._id,
          approved: report.approved,
          status: report.status,
          amountSpent: report.amountSpent,
          activityId: report.activity?._id || report.activity?.id,
          activityTitle: report.activity?.title
        });
      });

      // Map activities to budget data with actual spending from approved reports
      const activityBudgetData = activities.map((activity: any, index: number) => {
        const allocatedBudget = activity.budget || activity.allocatedBudget || 0;

        console.log(`\n🎯 Processing Activity: ${activity.title} (ID: ${activity._id})`);

        // Find approved reports for this activity
        const activityReports = reports.filter((report: any) => {
          const isApproved = report.approved === true || report.status === 'approved';
          const reportActivityId = report.activity?._id || report.activity?.id;
          const targetActivityId = activity._id || activity.id;
          const hasActivity = report.activity && reportActivityId === targetActivityId;

          console.log(`   📋 Checking report ${report._id}:`, {
            isApproved,
            hasActivity,
            reportActivityId,
            targetActivityId,
            idsMatch: reportActivityId === targetActivityId,
            amountSpent: report.amountSpent
          });

          return isApproved && hasActivity;
        });

        console.log(`   ✅ Found ${activityReports.length} approved reports for this activity`);

        // Calculate actual spent amount from approved reports
        const spentBudget = activityReports.reduce((sum: number, report: any) => {
          const amount = report.amountSpent || 0;
          console.log(`   💰 Adding ${amount} from report ${report._id}`);
          return sum + amount;
        }, 0);

        const remainingBudget = Math.max(0, allocatedBudget - spentBudget);

        // Calculate progress based on activity completion or budget utilization
        let progress = activity.progress || 0;

        // If no progress is set but there's budget utilization, use that as a proxy
        if (progress === 0 && allocatedBudget > 0 && spentBudget > 0) {
          progress = Math.min((spentBudget / allocatedBudget) * 100, 100);
        }

        console.log(`💰 FINAL Activity: ${activity.title}`);
        console.log(`   Allocated: MWK ${allocatedBudget.toLocaleString()}`);
        console.log(`   Spent: MWK ${spentBudget.toLocaleString()}`);
        console.log(`   Remaining: MWK ${remainingBudget.toLocaleString()}`);
        console.log(`   Progress: ${progress}%`);
        console.log(`   Reports: ${activityReports.length}`);

        return {
          id: activity._id || activity.id || `activity-${index}`,
          name: activity.title || activity.name || `Activity ${index + 1}`,
          allocatedBudget,
          spentBudget,
          remainingBudget,
          progress: Math.round(progress)
        };
      });



      console.log('\n📊 FINAL Activity Budget Data:');
      activityBudgetData.forEach((activity, index) => {
        console.log(`${index + 1}. ${activity.name}:`, {
          allocated: activity.allocatedBudget,
          spent: activity.spentBudget,
          remaining: activity.remainingBudget,
          progress: activity.progress
        });
      });

      setActivityBudgets(activityBudgetData);
    } catch (error) {
      console.error('Failed to fetch activity budgets:', error);
      setActivityBudgets([]);
    } finally {
      setLoadingActivities(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `MWK ${amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on_track':
        return 'text-sky-600';
      case 'over_budget':
        return 'text-red-600';
      case 'under_budget':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'on_track':
        return <CurrencyDollarIcon className="w-5 h-5" />;
      case 'over_budget':
        return <ArrowTrendingUpIcon className="w-5 h-5" />;
      case 'under_budget':
        return <ArrowTrendingDownIcon className="w-5 h-5" />;
      default:
        return null;
    }
  };

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <style jsx>{`
        .thin-scrollbar::-webkit-scrollbar {
          height: 6px;
        }
        .thin-scrollbar::-webkit-scrollbar-track {
          background: #f3f4f6;
          border-radius: 3px;
        }
        .thin-scrollbar::-webkit-scrollbar-thumb {
          background: #d1d5db;
          border-radius: 3px;
        }
        .thin-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #9ca3af;
        }
      `}</style>
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading budget data...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
          <h3 className="font-semibold mb-2">Error Loading Budget Data</h3>
          <p>{error}</p>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Budget Management</h1>
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
            >
              <option value="all">📊 All Projects Overview</option>
              {budgets.map(budget => (
                <option key={budget.id} value={budget.id}>
                  🎯 {budget.name}
                </option>
              ))}
            </select>
          </div>

          {/* Fallback Data Warning */}
          {usingFallbackData && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    <strong>Note:</strong> Budget data may not reflect the latest approved activity reports.
                    Some advanced budget calculations are currently unavailable.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Budget Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">
                {selectedProject === 'all' ? 'Total Budget' : 'Project Budget'}
              </h3>
              <div className="text-xl font-bold text-sky-600">
                {selectedProject === 'all'
                  ? formatCurrency(budgets.reduce((acc, curr) => acc + curr.totalBudget, 0))
                  : formatCurrency(budgets.find(b => b.id === selectedProject)?.totalBudget || 0)
                }
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">
                {selectedProject === 'all' ? 'Total Spent' : 'Project Spent'}
              </h3>
              <div className="text-xl font-bold text-yellow-600">
                {selectedProject === 'all'
                  ? formatCurrency(budgets.reduce((acc, curr) => acc + curr.spent, 0))
                  : formatCurrency(budgets.find(b => b.id === selectedProject)?.spent || 0)
                }
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">
                {selectedProject === 'all' ? 'Total Remaining' : 'Project Remaining'}
              </h3>
              <div className="text-xl font-bold text-green-600">
                {selectedProject === 'all'
                  ? formatCurrency(budgets.reduce((acc, curr) => acc + curr.remaining, 0))
                  : formatCurrency(budgets.find(b => b.id === selectedProject)?.remaining || 0)
                }
              </div>
            </div>
          </div>



          {/* Budget Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">
                {selectedProject === 'all' ? 'Budget Distribution' : 'Activity Budget Breakdown'}
              </h3>
              <div className="h-64">
                {selectedProject === 'all' ? (
                  <DoughnutChart
                    data={{
                      labels: budgets.map(b => b.name),
                      datasets: [{
                        data: budgets.map(b => b.totalBudget),
                        backgroundColor: [
                          'rgb(14, 165, 233)',
                          'rgb(168, 85, 247)',
                          'rgb(59, 130, 246)',
                          'rgb(34, 197, 94)',
                          'rgb(239, 68, 68)',
                          'rgb(245, 158, 11)'
                        ]
                      }]
                    }}
                    options={{
                      cutout: '70%',
                      plugins: {
                        legend: {
                          position: 'bottom'
                        }
                      }
                    }}
                  />
                ) : activityBudgets.length > 0 ? (
                  <DoughnutChart
                    data={{
                      labels: activityBudgets.map(a => a.name),
                      datasets: [{
                        data: activityBudgets.map(a => a.allocatedBudget),
                        backgroundColor: [
                          'rgb(14, 165, 233)',
                          'rgb(168, 85, 247)',
                          'rgb(59, 130, 246)',
                          'rgb(34, 197, 94)',
                          'rgb(239, 68, 68)',
                          'rgb(245, 158, 11)'
                        ]
                      }]
                    }}
                    options={{
                      cutout: '70%',
                      plugins: {
                        legend: {
                          position: 'bottom'
                        }
                      }
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    {loadingActivities ? 'Loading activities...' : 'No activities found for this project'}
                  </div>
                )}
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">
                {selectedProject === 'all' ? 'Overall Budget Utilization' : 'Activity Budget Utilization'}
              </h3>
              <div className="h-64">
                {selectedProject === 'all' ? (
                  <BarChart
                    data={{
                      labels: budgets.map(b => b.name),
                      datasets: [
                        {
                          label: 'Spent',
                          data: budgets.map(b => b.spent),
                          backgroundColor: 'rgb(14, 165, 233)'
                        },
                        {
                          label: 'Remaining',
                          data: budgets.map(b => b.remaining),
                          backgroundColor: 'rgb(226, 232, 240)'
                        }
                      ]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        x: { stacked: true },
                        y: { stacked: true }
                      }
                    }}
                  />
                ) : activityBudgets.length > 0 ? (
                  <BarChart
                    data={{
                      labels: activityBudgets.map(a => a.name),
                      datasets: [
                        {
                          label: 'Spent',
                          data: activityBudgets.map(a => a.spentBudget),
                          backgroundColor: 'rgb(239, 68, 68)'
                        },
                        {
                          label: 'Remaining',
                          data: activityBudgets.map(a => a.remainingBudget),
                          backgroundColor: 'rgb(34, 197, 94)'
                        }
                      ]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: true,
                          position: 'top' as const
                        },
                        tooltip: {
                          callbacks: {
                            label: function(context: any) {
                              const label = context.dataset.label || '';
                              const value = formatCurrency(context.parsed.y);
                              return `${label}: ${value}`;
                            }
                          }
                        }
                      },
                      scales: {
                        x: {
                          stacked: true,
                          ticks: {
                            maxRotation: 45,
                            minRotation: 0
                          }
                        },
                        y: {
                          stacked: true,
                          beginAtZero: true,
                          ticks: {
                            callback: function(value: any) {
                              return formatCurrency(value);
                            }
                          }
                        }
                      }
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    {loadingActivities ? 'Loading activities...' : 'No activities found for this project'}
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* Budget Details Table */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {selectedProject === 'all' ? 'All Projects Budget Details' : 'Activity Budget Details'}
              </h3>
            </div>

            <div
              className="overflow-x-auto thin-scrollbar"
              style={{
                scrollbarWidth: 'thin',
                scrollbarColor: '#d1d5db #f3f4f6'
              }}
            >
              {selectedProject === 'all' ? (
                <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Budget</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spent</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilization</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {budgets.map((budget) => (
                    <tr key={budget.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{budget.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{formatCurrency(budget.totalBudget)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{formatCurrency(budget.spent)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{formatCurrency(budget.remaining)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${Math.min((budget.spent / budget.totalBudget) * 100, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">
                            {Math.round((budget.spent / budget.totalBudget) * 100)}%
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`flex items-center ${getStatusColor(budget.status)}`}>
                          {getStatusIcon(budget.status)}
                          <span className="ml-2">
                            {budget.status.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : activityBudgets.length > 0 ? (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Allocated Budget</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spent</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilization</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {activityBudgets.map((activity) => (
                    <tr key={activity.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{activity.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{formatCurrency(activity.allocatedBudget)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{formatCurrency(activity.spentBudget)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{formatCurrency(activity.remainingBudget)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{ width: `${activity.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">{activity.progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${activity.allocatedBudget > 0 ? Math.min((activity.spentBudget / activity.allocatedBudget) * 100, 100) : 0}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">
                            {activity.allocatedBudget > 0 ? Math.round((activity.spentBudget / activity.allocatedBudget) * 100) : 0}%
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              ) : (
                <div className="px-6 py-8 text-center text-gray-500">
                  {loadingActivities ? (
                    <div>Loading activity budget data...</div>
                  ) : (
                    <div>
                      <p>No activities found for this project.</p>
                      <p className="text-sm mt-1">Activities with budget allocations will appear here.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
} 