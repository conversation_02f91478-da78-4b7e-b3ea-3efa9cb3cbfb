"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ChartBarIcon,
  UserCircleIcon,
  HomeIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ClipboardIcon,
  CogIcon,
  ArrowDownTrayIcon,
  UserGroupIcon,
  ClockIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { Bar } from "react-chartjs-2";
import { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from "chart.js";
import type { MouseEvent } from "react";
import { api } from '@/services/api';
import apiUtil from '@/utils/api';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import html2canvas from 'html2canvas';

Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);

// Interface for activity reports from field officers
interface ActivityReport {
  id: string;
  projectId: string;
  projectName: string;
  activityName: string;
  fieldOfficerId: string;
  fieldOfficerName: string;
  activityBudget: number;
  amountSpent: number;
  reportDate: string;
  status: 'pending' | 'approved' | 'rejected';
  description: string;
  location: string;
  beneficiaries: number;
  attachments?: string[];
}

// Interface for project budget data
interface ProjectBudgetData {
  id: string;
  name: string;
  approved?: number;
  used?: number;
  totalBudget?: number;
  usedBudget?: number;
  remainingBudget?: number;
  activities?: number; // Number of activities, not array
  completedActivities?: number;
  manager?: string;
  utilizationPercentage?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
  monthlySpending?: Array<{month: string; amount: number}>;
  categoryBreakdown?: Array<{category: string; amount: number; percentage: number}>;
}



function getUsageStatus(approved: number, used: number) {
  if (used > approved) return { label: "Over Budget", color: "bg-red-100 text-red-800 border-red-400" };
  if (used < approved * 0.7) return { label: "Underused", color: "bg-yellow-100 text-yellow-800 border-yellow-400" };
  return { label: "On Track", color: "bg-green-100 text-green-800 border-green-400" };
}



export default function AccountantDashboard() {
  const router = useRouter();
  const [userName, setUserName] = useState("Accountant");
  const [isExporting, setIsExporting] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [projectBudgetData, setProjectBudgetData] = useState<ProjectBudgetData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Dashboard metrics state
  const [dashboardStats, setDashboardStats] = useState({
    totalBudget: 0,
    budgetUtilization: 0,
    activeProjects: 0,
    totalUsed: 0,
    recentActivities: [],
    fundUsageData: []
  });



  // Fetch dashboard data from backend
  const fetchDashboardData = async () => {
    setIsLoading(true);
    try {
      // Use the existing api utility instead of direct fetch
      const response = await apiUtil.get('/api/v1/accountant/dashboard/stats');

      if (response.data && response.data.status === 'success') {
        setDashboardStats(response.data.data);
        // Map the fundUsageData to match our ProjectBudgetData interface
        const mappedData = (response.data.data.fundUsageData || []).map((project: any) => ({
          id: project.id || project._id || Math.random().toString(),
          name: project.name,
          approved: project.approved,
          used: project.used,
          totalBudget: project.approved,
          usedBudget: project.used,
          remainingBudget: project.remaining,
          activities: project.activities || 0,
          completedActivities: project.completedActivities || 0,
          manager: project.manager || 'Unassigned',
          utilizationPercentage: project.utilization || 0,
          status: project.status || 'active',
          startDate: project.startDate,
          endDate: project.endDate,
          monthlySpending: project.monthlySpending || [],
          categoryBreakdown: project.categoryBreakdown || []
        }));
        setProjectBudgetData(mappedData);
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);

      setToast({
        message: 'Failed to load dashboard data. Please check your connection and try again.',
        type: "error"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Fetch user profile
    api.getProfile()
      .then(res => {
        setUserName(res.data?.fullName || "Accountant");
      })
      .catch(error => {
        console.error('Error fetching user profile:', error);
        // Keep default name - no need to show error for profile name
        setUserName("Accountant");
      });

    // Fetch dashboard data on component mount
    fetchDashboardData();
  }, []);

  // Generate metrics from dashboard stats
  const getMetrics = () => [
    {
      label: "Total Budget",
      value: formatCurrency(dashboardStats.totalBudget),
      icon: <CurrencyDollarIcon className="w-5 h-5 text-green-600" />,
      href: "/accountant/budgets",
      description: "All requested budgets"
    },
    {
      label: "Utilization",
      value: `${dashboardStats.budgetUtilization}%`,
      icon: <ChartBarIcon className="w-5 h-5 text-green-600" />,
      description: "Budget usage rate"
    },
    {
      label: "Total",
      value: dashboardStats.activeProjects.toString(),
      icon: <UserGroupIcon className="w-5 h-5 text-green-600" />,
      description: "Total projects"
    },
  ];

  const handleCardClick = (href?: string) => {
    if (href) {
      router.push(href);
    }
  };

  const formatCurrency = (amount: number) => {
    return `MWK ${amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  // Compact format for charts
  const formatCurrencyCompact = (amount: number) => {
    if (amount >= **********) {
      return `MWK ${(amount / **********).toFixed(1)}B`;
    } else if (amount >= 1000000) {
      return `MWK ${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `MWK ${(amount / 1000).toFixed(1)}K`;
    } else {
      return `MWK ${amount.toLocaleString()}`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MW', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };







  // Generate chart data from fund usage data
  const generateChartData = () => {
    const fundData = dashboardStats.fundUsageData || [];
    return {
      labels: fundData.map(p => p.name),
      datasets: [
        {
          label: "Budget Used (MWK)",
          data: fundData.map(p => p.used || 0),
          backgroundColor: "rgba(59, 130, 246, 0.7)",
          borderRadius: 6,
        },
        {
          label: "Budget Remaining (MWK)",
          data: fundData.map(p => p.remaining || 0),
          backgroundColor: "rgba(16, 185, 129, 0.5)",
          borderRadius: 6,
        },
      ],
    };
  };

  const barData = generateChartData();

  const barOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' as const },
      title: { display: true, text: "Fund Usage by Project" },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrencyCompact(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return formatCurrencyCompact(value);
          }
        }
      },
    },
  };

  const exportToPDF = async () => {
    setIsExporting(true);

    try {
      const doc = new jsPDF();
      let yPosition = 20;

      // Header Section
      doc.setFontSize(20);
      doc.setFont(undefined, 'bold');
      doc.text('Budget Usage Analysis Report', 20, yPosition);
      yPosition += 20;

      // Report Info
      doc.setFontSize(11);
      doc.setFont(undefined, 'normal');
      doc.text(`Generated on: ${new Date().toLocaleDateString('en-MW')}`, 20, yPosition);
      doc.text(`Generated by: ${userName}`, 120, yPosition);
      yPosition += 15;

      // Add a line separator
      doc.setLineWidth(0.5);
      doc.line(20, yPosition, 190, yPosition);
      yPosition += 15;

      // Summary Statistics Section
      doc.setFontSize(16);
      doc.setFont(undefined, 'bold');
      doc.text('Executive Summary', 20, yPosition);
      yPosition += 12;

      const totalBudget = projectBudgetData.reduce((sum, p) => sum + (p.approved || p.totalBudget || 0), 0);
      const totalUsed = projectBudgetData.reduce((sum, p) => sum + (p.used || p.usedBudget || 0), 0);
      const totalRemaining = totalBudget - totalUsed;
      const utilizationRate = totalBudget > 0 ? ((totalUsed / totalBudget) * 100).toFixed(1) : '0';

      // Summary in a table format
      const summaryData = [
        ['Total Budget Allocated', formatCurrency(totalBudget)],
        ['Total Budget Used', formatCurrency(totalUsed)],
        ['Remaining Budget', formatCurrency(totalRemaining)],
        ['Overall Utilization Rate', `${utilizationRate}%`],
        ['Number of Projects', projectBudgetData.length.toString()]
      ];

      autoTable(doc, {
        head: [['Metric', 'Value']],
        body: summaryData,
        startY: yPosition,
        styles: { fontSize: 10, cellPadding: 4 },
        headStyles: { fillColor: [59, 130, 246], textColor: 255, fontStyle: 'bold' },
        columnStyles: { 0: { fontStyle: 'bold' } },
        margin: { left: 20, right: 20 }
      });

      yPosition = (doc as any).lastAutoTable.finalY + 20;

      // Fund Usage Analytics Chart Section
      doc.setFontSize(16);
      doc.setFont(undefined, 'bold');
      doc.text('Fund Usage Analytics Chart', 20, yPosition);
      yPosition += 12;

      try {
        // Find the chart container element
        const chartContainer = document.getElementById('fund-usage-chart');
        if (chartContainer) {
          // Check if we need a new page for the chart
          if (yPosition > 200) {
            doc.addPage();
            yPosition = 20;
            doc.setFontSize(16);
            doc.setFont(undefined, 'bold');
            doc.text('Fund Usage Analytics Chart', 20, yPosition);
            yPosition += 12;
          }

          // Capture the chart as image
          const canvas = await html2canvas(chartContainer, {
            backgroundColor: '#ffffff',
            scale: 2,
            logging: false,
            useCORS: true,
            width: chartContainer.offsetWidth,
            height: chartContainer.offsetHeight
          });

          const chartImage = canvas.toDataURL('image/png');

          // Add the chart image to PDF with proper sizing
          const maxWidth = 170;
          const maxHeight = 100;
          const imgWidth = Math.min(maxWidth, canvas.width * 0.4);
          const imgHeight = (canvas.height * imgWidth) / canvas.width;

          // Center the chart
          const xPosition = (210 - imgWidth) / 2;

          doc.addImage(chartImage, 'PNG', xPosition, yPosition, imgWidth, Math.min(imgHeight, maxHeight));
          yPosition += Math.min(imgHeight, maxHeight) + 15;
        } else {
          doc.setFontSize(10);
          doc.setFont(undefined, 'normal');
          doc.text('Chart not available for export', 20, yPosition);
          yPosition += 15;
        }
      } catch (chartError) {
        console.error('Failed to capture chart:', chartError);
        doc.setFontSize(10);
        doc.setFont(undefined, 'normal');
        doc.text('Chart capture failed - displaying data in table format below', 20, yPosition);
        yPosition += 15;
      }

      // Project Details Table Section
      if (yPosition > 220) {
        doc.addPage();
        yPosition = 20;
      }

      doc.setFontSize(16);
      doc.setFont(undefined, 'bold');
      doc.text('Project Budget Details', 20, yPosition);
      yPosition += 12;

      if (projectBudgetData.length > 0) {
        const tableData = projectBudgetData.map(project => {
          const approved = project.approved || project.totalBudget || 0;
          const used = project.used || project.usedBudget || 0;
          const remaining = approved - used;
          const status = getUsageStatus(approved, used);
          const utilization = approved > 0 ? ((used / approved) * 100).toFixed(1) : '0';
          return [
            project.name,
            formatCurrency(approved),
            formatCurrency(used),
            formatCurrency(remaining),
            `${utilization}%`,
            status.label
          ];
        });

        autoTable(doc, {
          head: [['Project Name', 'Approved Budget', 'Used Budget', 'Remaining', 'Utilization', 'Status']],
          body: tableData,
          startY: yPosition,
          styles: {
            fontSize: 9,
            cellPadding: 3,
            halign: 'center'
          },
          headStyles: {
            fillColor: [59, 130, 246],
            textColor: 255,
            fontStyle: 'bold',
            halign: 'center'
          },
          columnStyles: {
            0: { halign: 'left', cellWidth: 40 },
            1: { halign: 'right', cellWidth: 25 },
            2: { halign: 'right', cellWidth: 25 },
            3: { halign: 'right', cellWidth: 25 },
            4: { halign: 'center', cellWidth: 20 },
            5: { halign: 'center', cellWidth: 25 }
          },
          margin: { left: 20, right: 20 }
        });

        yPosition = (doc as any).lastAutoTable.finalY + 20;
      } else {
        doc.setFontSize(10);
        doc.setFont(undefined, 'normal');
        doc.text('No project data available', 20, yPosition);
        yPosition += 20;
      }
      // Project Summary Section
      if (yPosition > 200) {
        doc.addPage();
        yPosition = 20;
      }

      doc.setFontSize(16);
      doc.setFont(undefined, 'bold');
      doc.text('Project Summary', 20, yPosition);
      yPosition += 12;

      if (projectBudgetData.length === 0) {
        doc.setFontSize(10);
        doc.setFont(undefined, 'normal');
        doc.text('No project data available', 20, yPosition);
        yPosition += 15;
      } else {
        projectBudgetData.forEach((project, index) => {
          // Check if we need a new page
          if (yPosition > 240) {
            doc.addPage();
            yPosition = 20;
            doc.setFontSize(16);
            doc.setFont(undefined, 'bold');
            doc.text('Project Summary (Continued)', 20, yPosition);
            yPosition += 12;
          }

          // Project header
          doc.setFontSize(12);
          doc.setFont(undefined, 'bold');
          doc.text(`${index + 1}. ${project.name}`, 20, yPosition);
          yPosition += 8;

          // Project details in two columns
          doc.setFontSize(9);
          doc.setFont(undefined, 'normal');

          // Left column
          doc.text(`Activities: ${project.activities || 0}`, 25, yPosition);
          doc.text(`Manager: ${project.manager || 'Unassigned'}`, 25, yPosition + 4);

          // Right column
          doc.text(`Completed: ${project.completedActivities || 0}`, 100, yPosition);
          doc.text(`Utilization: ${project.utilizationPercentage || 0}%`, 100, yPosition + 4);

          yPosition += 12;

          // Add a subtle line separator
          doc.setLineWidth(0.2);
          doc.line(25, yPosition, 185, yPosition);
          yPosition += 6;
        });
      }

      // Add footer to all pages
      const pageCount = doc.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);

        // Footer line
        doc.setLineWidth(0.3);
        doc.line(20, 285, 190, 285);

        // Footer text
        doc.setFontSize(8);
        doc.setFont(undefined, 'normal');
        doc.text('Budget Usage Analysis Report', 20, 290);
        doc.text(`Page ${i} of ${pageCount}`, 170, 290);
        doc.text(`Generated on ${new Date().toLocaleDateString('en-MW')}`, 20, 294);
      }

      // Save the PDF with timestamp
      const timestamp = new Date().toISOString().slice(0, 10);
      doc.save(`budget-usage-analysis-report-${timestamp}.pdf`);

      setToast({
        message: 'Budget usage analysis report exported successfully as PDF!',
        type: "success"
      });

    } catch (error) {
      console.error('Export failed:', error);
      setToast({
        message: 'Failed to export report. Please try again.',
        type: "error"
      });
    } finally {
      setIsExporting(false);
      setTimeout(() => setToast(null), 3000);
    }
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading budget data from activity reports...</p>
        </div>
      </div>
    );
  }

  // Show empty state if no data and no error
  if (dashboardStats.totalBudget === 0 && dashboardStats.activeProjects === 0 && !toast) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ChartBarIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Budget Data Available</h3>
          <p className="text-gray-600 mb-4">There are currently no active projects or budget allocations to display.</p>
          <p className="text-sm text-gray-500">Data will appear here once projects are created and budgets are allocated.</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-8">
        {getMetrics().map((metric, idx) => (
          <div
            key={idx}
            className={`bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-blue-200 hover:border-blue-300 ${metric.href ? 'cursor-pointer hover:scale-105' : ''}`}
            onClick={() => handleCardClick(metric.href)}
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700 leading-tight">{metric.label}</h3>
              <div className="p-2 bg-blue-50 rounded-lg flex-shrink-0">
                {metric.icon}
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-2 break-words leading-tight">
              {metric.value}
            </div>
            <p className="text-gray-600 text-xs leading-tight">
              {metric.description}
            </p>
          </div>
        ))}
      </div>

      {/* Analytics Section */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 mb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">Fund Usage Analytics</h2>
          <div className="flex gap-2">
            <button
              onClick={() => exportToPDF()}
              disabled={isExporting}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowDownTrayIcon className="w-4 h-4" />
              {isExporting ? 'Exporting...' : 'Export PDF'}
            </button>
          </div>
        </div>
        <div className="h-72" id="fund-usage-chart">
          {projectBudgetData.length > 0 ? (
            <Bar data={barData} options={barOptions} />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <ChartBarIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">No project data available</p>
                <p className="text-gray-400 text-sm">Activity reports will appear here once submitted</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Recent Financial Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
            <button className="text-sky-600 text-sm font-semibold hover:underline">View All</button>
          </div>
          <div className="space-y-4">
            {dashboardStats.recentActivities && dashboardStats.recentActivities.length > 0 ? (
              dashboardStats.recentActivities.slice(0, 5).map((activity, index) => (
                <div key={activity._id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {activity.activity?.title || 'Activity Report'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {activity.activity?.project?.name || 'Unknown Project'} • {activity.submittedBy?.fullName || 'Unknown User'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {activity.amountSpent ? formatCurrency(activity.amountSpent) : 'N/A'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {activity.createdAt ? formatDate(activity.createdAt) : 'N/A'}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <ClockIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent activities</p>
                <p className="text-gray-400 text-sm">Your recent actions will appear here</p>
              </div>
            )}
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Total Projects</h2>
            <span className="text-sm text-gray-500">{dashboardStats.activeProjects} projects</span>
          </div>
          <div className="space-y-3">
            {dashboardStats.fundUsageData && dashboardStats.fundUsageData.length > 0 ? (
              dashboardStats.fundUsageData.slice(0, 5).map((project, index) => (
                <div key={project.name || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${
                      project.utilization >= 80 ? 'bg-red-500' :
                      project.utilization >= 50 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}></div>
                    <div>
                      <p className="font-medium text-gray-900">{project.name}</p>
                      <p className="text-sm text-gray-600">
                        {formatCurrency(project.approved)} approved
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {project.utilization}% used
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatCurrency(project.used)} spent
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <UserGroupIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No projects found</p>
                <p className="text-gray-400 text-sm">Projects will appear here once they are created</p>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* Toast Notification */}
      {toast && (
        <div className={`fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg ${
          toast.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`}>
          {toast.message}
        </div>
      )}
    </>
  );
} 


