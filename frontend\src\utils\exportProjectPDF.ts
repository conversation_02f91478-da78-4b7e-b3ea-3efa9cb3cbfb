// utils/exportProjectPDF.ts
import jsPD<PERSON> from "jspdf";
import autoTable from "jspdf-autotable";

// Add TypeScript declarations for jsPDF autotable
declare module "jspdf" {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
    lastAutoTable: any;
  }
}

interface Goal {
  title: string;
  target: string;
}

interface Objective {
  title: string;
  goals: Goal[];
}

interface InputItem {
  name: string;
  quantity: number;
  date?: string;
}

interface OutputItem {
  name: string;
  value: number;
  date?: string;
}

interface KPI {
  name: string;
  target: string;
}

interface Project {
  _id: string;
  name: string;
  sector: string;
  district: string;
  startDate: string;
  endDate: string;
  duration?: string;
  assignedTo?: {
    _id: string;
    fullName: string;
  };
  kpis?: KPI[];
  justification?: string;
  budget?: number | string;
  status?: string;
}

interface ProjectPlan {
  expectedOutcome: string;
  objectives: Objective[];
  inputs: InputItem[];
  outputs: OutputItem[];
}

export function exportProjectPDF(project: Project, plan: ProjectPlan) {
  const doc = new jsPDF();
  doc.setFontSize(16);
  doc.text(`Project Details: ${project.name}`, 14, 20);

  const overviewData = [
    ["Sector", project.sector],
    ["District", project.district],
    ["Start Date", new Date(project.startDate).toLocaleDateString()],
    ["End Date", new Date(project.endDate).toLocaleDateString()],
    ["Duration", project.duration ?? "N/A"],
    ["Assigned To", project.assignedTo?.fullName ?? "N/A"],
    ["Status", project.status ?? "N/A"],
    ["Budget", project.budget ?? "N/A"],
    ["Justification", project.justification ?? "N/A"],
  ];

  doc.autoTable({
    startY: 30,
    head: [["Project Overview", "Value"]],
    body: overviewData,
    styles: { fontSize: 10 },
    headStyles: { fillColor: [41, 128, 185] },
  });

  if (project.kpis && project.kpis.length > 0) {
    const kpiRows = project.kpis.map((kpi) => [kpi.name, kpi.target]);
    doc.autoTable({
      startY: doc.lastAutoTable.finalY + 10,
      head: [["KPI Name", "Target"]],
      body: kpiRows,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [39, 174, 96] },
    });
  }

  if (plan.objectives && plan.objectives.length > 0) {
    const rows: string[][] = [];
    plan.objectives.forEach((obj) => {
      rows.push([obj.title, ""]);
      obj.goals.forEach((goal) => {
        rows.push(["  - " + goal.title, "Target: " + goal.target]);
      });
    });

    doc.autoTable({
      startY: doc.lastAutoTable.finalY + 10,
      head: [["Objectives and Goals", "Details"]],
      body: rows,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [230, 126, 34] },
    });
  }

  if (plan.inputs && plan.inputs.length > 0) {
    const inputRows = plan.inputs.map((input) => [
      input.name,
      input.quantity.toString(),
      input.date ? new Date(input.date).toLocaleDateString() : "N/A",
    ]);

    doc.autoTable({
      startY: doc.lastAutoTable.finalY + 10,
      head: [["Input Name", "Quantity", "Date"]],
      body: inputRows,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [52, 73, 94] },
    });
  }

  if (plan.outputs && plan.outputs.length > 0) {
    const outputRows = plan.outputs.map((output) => [
      output.name,
      output.value.toString(),
      output.date ? new Date(output.date).toLocaleDateString() : "N/A",
    ]);

    doc.autoTable({
      startY: doc.lastAutoTable.finalY + 10,
      head: [["Output Name", "Value", "Date"]],
      body: outputRows,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [142, 68, 173] },
    });
  }

  doc.save(`${project.name}_details.pdf`);
}
