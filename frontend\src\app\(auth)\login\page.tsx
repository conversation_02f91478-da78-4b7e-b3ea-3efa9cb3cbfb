'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import api from '@/utils/api';
import ForgotPasswordModal from '@/components/ForgotPasswordModal';

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    if (!email || !password) {
      setError('Please fill in all fields');
      setIsLoading(false);
      return;
    }

    try {
      console.log('🔐 Attempting login with:', { email });
      // Call backend directly instead of going through Next.js API route
      const res = await api.post('/api/v1/auth/login', { email, password });
      const data = res.data;
      console.log('✅ Login response:', data);
      console.log('✅ Token received:', !!data.token);
      console.log('✅ User role:', data.user?.role?.name);

      if (!data.user || !data.user.role) {
        throw new Error('Invalid user data returned from server');
      }

      // Store user data in localStorage for components to access
      localStorage.setItem('user', JSON.stringify(data.user));
      localStorage.setItem('userId', data.user.id);
      localStorage.setItem('userRole', data.user.role.name);
      localStorage.setItem('token', data.token);

      console.log('💾 Stored user data:', {
        userId: data.user.id,
        role: data.user.role.name,
        fullName: data.user.fullName
      });

      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        const roleKey = data.user.role.name.toLowerCase().replace(/[\s_-]/g, '');
        console.log('🔄 Redirecting user with role:', roleKey);

        switch (roleKey) {
          case 'admin':
            console.log('➡️ Redirecting to admin dashboard');
            router.push('/admin/dashboard');
            break;
          case 'projectmanager':
            console.log('➡️ Redirecting to project manager dashboard');
            router.push('/manager/dashboard');
            break;
          case 'fieldofficer':
            console.log('➡️ Redirecting to field officer dashboard');
            router.push('/field/dashboard');
            break;
          case 'accountant':
            console.log('➡️ Redirecting to accountant dashboard');
            router.push('/accountant/dashboard');
            break;
          case 'seniormanager':
            console.log('➡️ Redirecting to senior manager dashboard');
            router.push('/senior-manager/dashboard');
            break;
          default:
            console.log('❌ Unknown role, redirecting to login');
            router.push('/login');
        }
      }, 1500);
    } catch (err: any) {
      setError(err.message || 'An error occurred during login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md w-full mx-auto">
      <div className="bg-white p-8 rounded-xl shadow-xl relative">
        {/* Success Popup */}
        {showSuccess && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
            <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
              <svg className="w-12 h-12 text-green-500 mb-2 animate-bounce" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
              </svg>
              <p className="text-lg font-semibold text-green-600">Login successful!</p>
            </div>
          </div>
        )}
        <div className="text-center mb-8">
          <div className="mb-6">
            <h1 className="text-5xl font-light text-gray-800 mb-2 tracking-wider">
              <span className="bg-gradient-to-r from-gray-700 via-gray-800 to-gray-900 bg-clip-text text-transparent">
                SPRODETA
              </span>
            </h1>
          </div>
          <h2 className="text-xl text-gray-600 font-medium">Sign in to your account</h2>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6" method="post" autoComplete="off">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <div className="mt-1">
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="off"
                required
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                data-lpignore="true"
              />
            </div>
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <div className="mt-1">
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="off"
                required
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                data-lpignore="true"
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-sky-500 hover:bg-sky-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transition-colors duration-200 ${
                isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isLoading ? (
                <span className="flex items-center gap-2">
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                  </svg>
                  Signing in...
                </span>
              ) : 'Sign in'}
            </button>
          </div>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Forgot your password?{' '}
            <button
              type="button"
              onClick={() => setShowForgotPassword(true)}
              className="font-medium text-sky-600 hover:text-sky-500"
            >
              Reset it here
            </button>
          </p>
        </div>

        {/* Forgot Password Modal */}
        <ForgotPasswordModal
          isOpen={showForgotPassword}
          onClose={() => setShowForgotPassword(false)}
        />
      </div>
    </div>
  );
} 