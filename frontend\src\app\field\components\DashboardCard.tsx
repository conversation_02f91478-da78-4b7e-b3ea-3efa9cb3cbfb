import React from 'react';

interface DashboardCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  trend?: string;
  onClick?: () => void;
  color?: 'blue' | 'yellow' | 'green' | 'purple' | 'red';
}

const colorClasses = {
  blue: {
    bg: 'bg-white',
    border: 'border-blue-200',
    text: 'text-blue-600',
    icon: 'text-blue-600',
    iconBg: 'bg-blue-50',
    progress: 'bg-blue-600'
  },
  yellow: {
    bg: 'bg-white',
    border: 'border-blue-200',
    text: 'text-yellow-600',
    icon: 'text-yellow-600',
    iconBg: 'bg-yellow-50',
    progress: 'bg-yellow-600'
  },
  green: {
    bg: 'bg-white',
    border: 'border-blue-200',
    text: 'text-green-600',
    icon: 'text-green-600',
    iconBg: 'bg-green-50',
    progress: 'bg-green-600'
  },
  purple: {
    bg: 'bg-white',
    border: 'border-blue-200',
    text: 'text-purple-600',
    icon: 'text-purple-600',
    iconBg: 'bg-purple-50',
    progress: 'bg-purple-600'
  },
  red: {
    bg: 'bg-white',
    border: 'border-blue-200',
    text: 'text-red-600',
    icon: 'text-red-600',
    iconBg: 'bg-red-50',
    progress: 'bg-red-600'
  }
};

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  icon,
  trend,
  color = 'blue',
  onClick
}) => {
  const colors = colorClasses[color];

  return (
    <div
      className={`${colors.bg} rounded-xl shadow-sm p-6 border ${colors.border} hover:shadow-md transition-all duration-300 ${onClick ? 'cursor-pointer hover:scale-105' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        <div className={`p-2 ${colors.iconBg} rounded-lg`}>
          <div className={`w-5 h-5 ${colors.icon}`}>
            {icon}
          </div>
        </div>
      </div>
      <div className="mt-4">
        <div className={`text-3xl font-bold ${colors.text}`}>{value}</div>
        {trend && (
          <div className="flex items-center mt-2">
            <span className={`text-sm font-medium ${
              trend.startsWith('+') ? 'text-green-600' :
              trend.startsWith('-') ? 'text-red-600' :
              'text-gray-600'
            }`}>
              {trend}
            </span>
            <span className="text-gray-500 text-sm ml-1">from last month</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardCard;
