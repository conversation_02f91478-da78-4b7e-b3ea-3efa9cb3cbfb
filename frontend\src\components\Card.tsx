import React from 'react';

interface CardProps {
  title: string;
  count: number;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({ title, count, onClick }) => (
  <div
    className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 cursor-pointer"
    onClick={onClick}
  >
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
      <div className="p-2 bg-sky-100 rounded-lg">
        <svg className="w-6 h-6 text-sky-600" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
          <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
        </svg>
      </div>
    </div>
    <div className="mt-4">
      <div className="text-3xl font-bold text-sky-600">{count}</div>
      <p className="text-gray-600 text-sm mt-2">Currently in progress</p>
    </div>
    <div className="mt-4 h-1 bg-sky-100 rounded-full overflow-hidden">
      <div className="h-1 bg-sky-600 rounded-full" style={{ width: '70%' }}></div>
    </div>
  </div>
);

export default Card; 