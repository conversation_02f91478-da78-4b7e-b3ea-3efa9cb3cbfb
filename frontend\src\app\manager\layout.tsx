"use client";
import React, { useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  HomeIcon,
  ChartBarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  BanknotesIcon,
  ClipboardDocumentListIcon,
  UserCircleIcon,
  CalendarDaysIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
} from "@heroicons/react/24/solid";
import CollapsibleSidebar, { getGreeting, getGreetingEmoji } from "@/components/CollapsibleSidebar";
import NotificationBell from "@/components/NotificationBell";

interface ManagerLayoutProps {
  children: React.ReactNode;
}

const navLinks = [
  { name: "Dashboard", href: "/manager/dashboard", icon: HomeIcon },
  {
    name: "Projects",
    href: "/manager/projects",
    icon: ClipboardDocumentListIcon,
    children: [
      { name: "Teams", href: "/manager/teams", icon: UserGroupIcon },
      { name: "Activities", href: "/manager/activities", icon: CalendarDaysIcon },
      { name: "Budget", href: "/manager/budget", icon: BanknotesIcon },
      { name: "Reports", href: "/manager/reports", icon: DocumentTextIcon },
    ]
  },
  { name: "M&E", href: "/manager/m&e", icon: ChartBarIcon },
];

export default function ManagerLayout({ children }: ManagerLayoutProps) {
  const router = useRouter();
  const [userName, setUserName] = useState('Manager');
  const [greeting, setGreeting] = useState('Hello');
  const [greetingEmoji, setGreetingEmoji] = useState('👋');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    // Get user name from localStorage or API
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        setUserName(user.fullName || user.name || 'Manager');
      } catch (error) {
        // Keep default name
      }
    }

    // Set time-based greeting
    setGreeting(getGreeting());
    setGreetingEmoji(getGreetingEmoji());

    // Update greeting every minute
    const interval = setInterval(() => {
      setGreeting(getGreeting());
      setGreetingEmoji(getGreetingEmoji());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-[#f4f6fa]">
      <style jsx global>{`
        /* Custom scrollbar styles for manager pages */
        ::-webkit-scrollbar {
          height: 6px;
          width: 6px;
        }

        ::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #555;
        }

        /* Firefox scrollbar */
        * {
          scrollbar-width: thin;
          scrollbar-color: #888 #f1f1f1;
        }
      `}</style>
      <CollapsibleSidebar
        navLinks={navLinks}
        userName={userName}
        userRole="Project Manager"
        onCollapseChange={setIsSidebarCollapsed}
      >
        {/* Header */}
        <header className="sticky top-0 z-10 flex items-center justify-between bg-white border-b border-gray-100 px-6 py-3">
          <div className="text-lg font-bold text-blue-700 tracking-tight">Dashboard</div>
          <div className="flex-1 flex justify-center">
            <div className="text-base font-medium text-gray-700">
              {greeting}, {userName}! {greetingEmoji}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <NotificationBell userRole="projectManager" />
            <button
              onClick={() => router.push('/manager/profile')}
              className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden hover:bg-blue-200 transition-colors"
            >
              <UserCircleIcon className="w-6 h-6 text-blue-600" />
            </button>
          </div>
        </header>
        <main className="p-6">{children}</main>
      </CollapsibleSidebar>
    </div>
  );
}