'use client';
import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  ClockIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/solid';
import ActivityDetails from '../components/ActivityDetails';
import DashboardCard from '../components/DashboardCard';
import ActivityProgressBar from '../components/ActivityProgressBar';
import { api } from '@/services/api';
import { DashboardStats, Project, ProjectProgress, BudgetData } from '@/services/api';
import { Toaster, toast } from 'react-hot-toast';
import React from 'react';
import Modal from '@/components/Modal';
import { useRouter } from 'next/navigation';

// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// Dynamically import Chart.js components to avoid SSR issues
const Chart = dynamic(() => import('react-chartjs-2').then(mod => mod.Line), { ssr: false });
const DoughnutChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Doughnut), { ssr: false });

export default function FieldOfficerDashboard() {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    inProgressProjects: 0,
    completedProjects: 0,
    totalTeamMembers: 0,
    projectProgress: 0,
    recentProjects: [],
    recentActivities: []
  });

  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [budgetData, setBudgetData] = useState<BudgetData>({
    totalBudget: 0,
    usedBudget: 0,
    remainingBudget: 0
  });
  const [activities, setActivities] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any>(null);

  const [assignedActivities, setAssignedActivities] = useState<any[]>([]);
  const [showProjectsModal, setShowProjectsModal] = useState(false);
  const [showActivitiesModal, setShowActivitiesModal] = useState(false);
  const [activeActivities, setActiveActivities] = useState<any[]>([]);
  const [showReportsModal, setShowReportsModal] = useState(false);
  const [pendingReports, setPendingReports] = useState<any[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch field officer specific data with error handling
        const [statsResponse, activitiesResponse, reportsResponse] = await Promise.allSettled([
          api.getFieldOfficerStats().catch(() => ({ data: null })),
          api.getAssignedActivities().catch(() => ({ data: [] })),
          api.getPendingReportsForFieldOfficer().catch(() => ({ data: [] }))
        ]);

        // Handle stats response
        const statsData = statsResponse.status === 'fulfilled' ? statsResponse.value.data : null;
        if (statsData) {
          setStats(statsData);
        } else {
          // Set empty state instead of fallback data
          setStats({
            totalProjects: 0,
            inProgressProjects: 0,
            completedProjects: 0,
            totalTeamMembers: 0,
            projectProgress: 0,
            recentProjects: [],
            recentActivities: []
          });
        }

        // Handle activities data
        const activitiesResponseData = activitiesResponse.status === 'fulfilled' ? activitiesResponse.value.data : [];
        const activitiesData = Array.isArray(activitiesResponseData)
          ? activitiesResponseData
          : activitiesResponseData?.data || [];

        setAssignedActivities(activitiesData);
        setActiveActivities(activitiesData.filter((activity: any) => activity.status === 'active'));

        // Handle reports data
        const reportsResponseData = reportsResponse.status === 'fulfilled' ? reportsResponse.value.data : [];
        const reportsData = Array.isArray(reportsResponseData)
          ? reportsResponseData
          : reportsResponseData?.data || [];

        setPendingReports(reportsData);

        // Generate chart data from activities
        generateChartData(activitiesData);

      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Generate chart data from activities
  const generateChartData = (activitiesData: any[]) => {
    // Get last 6 months
    const months = [];
    const now = new Date();
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push({
        name: date.toLocaleDateString('en-US', { month: 'short' }),
        value: date.getMonth(),
        year: date.getFullYear()
      });
    }

    // Count completed activities by month
    const completionData = months.map(month => {
      const completedInMonth = activitiesData.filter(activity => {
        if (activity.status !== 'completed' || !activity.updatedAt) return false;
        const activityDate = new Date(activity.updatedAt);
        return activityDate.getMonth() === month.value && activityDate.getFullYear() === month.year;
      }).length;
      return completedInMonth;
    });

    setChartData({
      labels: months.map(m => m.name),
      datasets: [
        {
          label: 'Activities Completed',
          data: completionData,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        },
      ],
    });
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const budgetChartData = {
    labels: ['Used Budget', 'Remaining Budget'],
    datasets: [
      {
        data: [budgetData.usedBudget, budgetData.remainingBudget],
        backgroundColor: ['#3b82f6', '#60a5fa'],
        borderWidth: 0,
      },
    ],
  };

  const budgetChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Toaster position="top-right" />
      
      {/* Modals for details */}
      {showProjectsModal && (
        <Modal onClose={() => setShowProjectsModal(false)} title="Active Activities">
          <ul>
            {activeActivities.length === 0 ? (
              <li>No active activities assigned.</li>
            ) : (
              activeActivities.map((activity) => (
                <li key={activity._id}>{activity.title} ({activity.status})</li>
              ))
            )}
          </ul>
        </Modal>
      )}
      {showReportsModal && (
        <Modal onClose={() => setShowReportsModal(false)} title="Pending Reports">
          <ul>
            {pendingReports.length === 0 ? (
              <li>No pending reports.</li>
            ) : (
              pendingReports.map((report) => (
                <li key={report._id}>{report.activity?.title || 'Unknown Activity'} - {report.status}</li>
              ))
            )}
          </ul>
        </Modal>
      )}

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-6">
        <div className="max-w-7xl mx-auto space-y-8">

          {!isLoading && (
            <>
              {/* Stats Overview */}
              <div className="mb-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <DashboardCard
                    title="Assigned Activities"
                    value={stats.totalAssignedActivities || assignedActivities.length}
                    icon={<ClockIcon className="w-8 h-8" />}
                    color="blue"
                    onClick={() => router.push('/field/activities')}
                  />
                  <DashboardCard
                    title="Active Activities"
                    value={stats.activeActivities || activeActivities.length}
                    icon={<ChartBarIcon className="w-8 h-8" />}
                    color="blue"
                    onClick={() => router.push('/field/activities')}
                  />
                  <DashboardCard
                    title="Pending Reports"
                    value={stats.pendingReports || pendingReports.length}
                    icon={<DocumentTextIcon className="w-8 h-8" />}
                    color="blue"
                    onClick={() => router.push('/field/reports')}
                  />
                  <DashboardCard
                    title="My Completed Activities"
                    value={stats.completedActivities || 0}
                    icon={<CheckCircleIcon className="w-8 h-8" />}
                    color="blue"
                  />
                </div>
              </div>

              {/* Charts and Activity Progress */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h2 className="text-xl font-semibold mb-4 text-gray-800">Activity Progress</h2>
                  <div className="h-64">
                    {chartData ? (
                      <Chart data={chartData} options={chartOptions} />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                          <p className="text-gray-500">Loading chart data...</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h2 className="text-xl font-semibold mb-4 text-gray-800">Recent Activities</h2>
                  <div className="space-y-4">
                    {assignedActivities.slice(0, 5).map((activity) => (
                      <div key={activity._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <h3 className="font-medium text-gray-900">{activity.title}</h3>
                          <p className="text-sm text-gray-600">{activity.project?.name || 'No project'}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          activity.status === 'active' ? 'bg-blue-100 text-blue-800' :
                          activity.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {activity.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>


            </>
          )}
        </div>
      </div>
    </>
  );
}
