'use client';
import React, { useState, useRef, useEffect } from 'react';
import { InformationCircleIcon } from '@heroicons/react/24/outline';

interface HoverModalProps {
  children: React.ReactNode;
  title: string;
  content: React.ReactNode | string;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  delay?: number;
  className?: string;
}

export default function HoverModal({
  children,
  title,
  content,
  position = 'auto',
  delay = 300,
  className = ''
}: HoverModalProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [modalPosition, setModalPosition] = useState({ top: 0, left: 0 });
  const [actualPosition, setActualPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('top');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  const calculatePosition = () => {
    if (!containerRef.current || !modalRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const modalRect = modalRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;
    let finalPosition: 'top' | 'bottom' | 'left' | 'right' = 'top';

    if (position === 'auto') {
      // Auto-calculate best position
      const spaceTop = containerRect.top;
      const spaceBottom = viewportHeight - containerRect.bottom;
      const spaceLeft = containerRect.left;
      const spaceRight = viewportWidth - containerRect.right;

      if (spaceTop >= modalRect.height + 10) {
        // Position above
        finalPosition = 'top';
        top = containerRect.top - modalRect.height - 10;
        left = containerRect.left + (containerRect.width - modalRect.width) / 2;
      } else if (spaceBottom >= modalRect.height + 10) {
        // Position below
        finalPosition = 'bottom';
        top = containerRect.bottom + 10;
        left = containerRect.left + (containerRect.width - modalRect.width) / 2;
      } else if (spaceRight >= modalRect.width + 10) {
        // Position to the right
        finalPosition = 'right';
        top = containerRect.top + (containerRect.height - modalRect.height) / 2;
        left = containerRect.right + 10;
      } else if (spaceLeft >= modalRect.width + 10) {
        // Position to the left
        finalPosition = 'left';
        top = containerRect.top + (containerRect.height - modalRect.height) / 2;
        left = containerRect.left - modalRect.width - 10;
      } else {
        // Default to bottom if no space
        finalPosition = 'bottom';
        top = containerRect.bottom + 10;
        left = containerRect.left + (containerRect.width - modalRect.width) / 2;
      }
    } else {
      // Use specified position
      finalPosition = position;
      switch (position) {
        case 'top':
          top = containerRect.top - modalRect.height - 10;
          left = containerRect.left + (containerRect.width - modalRect.width) / 2;
          break;
        case 'bottom':
          top = containerRect.bottom + 10;
          left = containerRect.left + (containerRect.width - modalRect.width) / 2;
          break;
        case 'left':
          top = containerRect.top + (containerRect.height - modalRect.height) / 2;
          left = containerRect.left - modalRect.width - 10;
          break;
        case 'right':
          top = containerRect.top + (containerRect.height - modalRect.height) / 2;
          left = containerRect.right + 10;
          break;
      }
    }

    // Ensure modal stays within viewport
    left = Math.max(10, Math.min(left, viewportWidth - modalRect.width - 10));
    top = Math.max(10, Math.min(top, viewportHeight - modalRect.height - 10));

    setModalPosition({ top, left });
    setActualPosition(finalPosition);
  };

  const showModal = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      // Calculate position after modal becomes visible
      setTimeout(calculatePosition, 0);
    }, delay);
  };

  const hideModal = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isVisible) {
      calculatePosition();
      const handleResize = () => calculatePosition();
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [isVisible]);

  const getArrowClasses = () => {
    const baseClasses = "absolute w-3 h-3 bg-gray-800 transform rotate-45";
    switch (actualPosition) {
      case 'top':
        return `${baseClasses} -bottom-1.5 left-1/2 -translate-x-1/2`;
      case 'bottom':
        return `${baseClasses} -top-1.5 left-1/2 -translate-x-1/2`;
      case 'left':
        return `${baseClasses} -right-1.5 top-1/2 -translate-y-1/2`;
      case 'right':
        return `${baseClasses} -left-1.5 top-1/2 -translate-y-1/2`;
      default:
        return `${baseClasses} -bottom-1.5 left-1/2 -translate-x-1/2`;
    }
  };

  return (
    <>
      <div
        ref={containerRef}
        className={`relative inline-block ${className}`}
        onMouseEnter={showModal}
        onMouseLeave={hideModal}
      >
        {children}
      </div>

      {isVisible && (
        <div
          ref={modalRef}
          className="fixed z-50 pointer-events-none"
          style={{
            top: modalPosition.top,
            left: modalPosition.left,
          }}
        >
          <div className="bg-gray-800 text-white rounded-lg shadow-xl p-4 max-w-sm animate-in fade-in-0 zoom-in-95 duration-200">
            {/* Arrow */}
            <div className={getArrowClasses()}></div>
            
            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center space-x-2 mb-2">
                <InformationCircleIcon className="w-4 h-4 text-blue-400 flex-shrink-0" />
                <h3 className="font-semibold text-sm text-white">{title}</h3>
              </div>
              <div className="text-sm text-gray-200">
                {typeof content === 'string' ? (
                  <p>{content}</p>
                ) : (
                  content
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Enhanced Dashboard Card with Hover Modal
interface DashboardCardWithModalProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  description?: string;
  detailsContent?: React.ReactNode;
  trend?: string;
  onClick?: () => void;
  color?: 'blue' | 'yellow' | 'green' | 'purple' | 'red' | 'white';
  className?: string;
}

export function DashboardCardWithModal({
  title,
  value,
  icon,
  description,
  detailsContent,
  trend,
  onClick,
  color = 'white',
  className = ''
}: DashboardCardWithModalProps) {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-600',
      icon: 'text-blue-600',
      iconBg: 'bg-blue-100'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-600',
      icon: 'text-yellow-600',
      iconBg: 'bg-yellow-100'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-600',
      icon: 'text-green-600',
      iconBg: 'bg-green-100'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      text: 'text-purple-600',
      icon: 'text-purple-600',
      iconBg: 'bg-purple-100'
    },
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-600',
      icon: 'text-red-600',
      iconBg: 'bg-red-100'
    },
    white: {
      bg: 'bg-white',
      border: 'border-blue-200',
      text: 'text-gray-900',
      icon: 'text-blue-600',
      iconBg: 'bg-blue-50'
    }
  };

  const colors = colorClasses[color];

  const modalContent = detailsContent || (
    <div className="space-y-2">
      <p><strong>Current Value:</strong> {value}</p>
      {description && <p><strong>Description:</strong> {description}</p>}
      {trend && <p><strong>Trend:</strong> {trend}</p>}
      <p className="text-xs text-gray-300 mt-2">Click the card to view more details</p>
    </div>
  );

  return (
    <HoverModal
      title={title}
      content={modalContent}
      className={className}
    >
      <div
        className={`${colors.bg} rounded-xl shadow-sm p-6 border ${colors.border} hover:shadow-md transition-all duration-300 ${onClick ? 'cursor-pointer hover:scale-105' : ''}`}
        onClick={onClick}
      >
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">{title}</h3>
          <div className={`p-2 ${colors.iconBg} rounded-lg`}>
            <div className={`w-5 h-5 ${colors.icon}`}>
              {icon}
            </div>
          </div>
        </div>
        <div className="mb-2">
          <div className={`text-2xl font-bold ${colors.text}`}>{value}</div>
          {trend && (
            <div className="flex items-center mt-1">
              <span className={`text-sm font-medium ${
                trend.startsWith('+') ? 'text-green-600' :
                trend.startsWith('-') ? 'text-red-600' :
                'text-gray-600'
              }`}>
                {trend}
              </span>
              <span className="text-gray-500 text-xs ml-1">from last month</span>
            </div>
          )}
        </div>
        {description && (
          <p className="text-xs text-gray-600">{description}</p>
        )}
      </div>
    </HoverModal>
  );
}
