'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  PowerIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/solid';

interface NavLink {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavLink[];
}

interface CollapsibleSidebarProps {
  navLinks: NavLink[];
  activeItem?: string;
  userName?: string;
  userRole: string;
  children?: React.ReactNode;
  showSearchBar?: boolean;
  onCollapseChange?: (isCollapsed: boolean) => void;
}

export const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return 'Good morning';
  if (hour < 17) return 'Good afternoon';
  return 'Good evening';
};

export const getGreetingEmoji = () => {
  const hour = new Date().getHours();
  if (hour < 12) return '🌅';
  if (hour < 17) return '☀️';
  return '🌙';
};

export default function CollapsibleSidebar({
  navLinks,
  activeItem,
  userName = 'User',
  userRole,
  children,
  showSearchBar = true,
  onCollapseChange
}: CollapsibleSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [greeting, setGreeting] = useState('');
  const [greetingEmoji, setGreetingEmoji] = useState('');
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [expandedDropdowns, setExpandedDropdowns] = useState<Set<string>>(new Set());
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Set time-based greeting only on client to avoid hydration mismatch
    setGreeting(getGreeting());
    setGreetingEmoji(getGreetingEmoji());

    // Update greeting every minute
    const interval = setInterval(() => {
      setGreeting(getGreeting());
      setGreetingEmoji(getGreetingEmoji());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Notify parent component when collapse state changes
  useEffect(() => {
    if (onCollapseChange) {
      onCollapseChange(isCollapsed);
    }
  }, [isCollapsed, onCollapseChange]);

  const handleLogout = () => {
    setShowLogoutConfirm(true);
  };

  const confirmLogout = async () => {
    try {
      console.log('🚪 Logging out user...');

      // Call logout API to clear cookies
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      // Clear all stored data
      localStorage.clear();
      sessionStorage.clear();

      console.log('✅ Logout successful, redirecting to login');

      // Redirect to login
      router.push('/login');
    } catch (error) {
      console.error('❌ Logout error:', error);
      // Still redirect even if API call fails
      router.push('/login');
    }
  };

  const toggleDropdown = (linkName: string) => {
    setExpandedDropdowns(prev => {
      const newSet = new Set(prev);
      if (newSet.has(linkName)) {
        newSet.delete(linkName);
      } else {
        newSet.add(linkName);
      }
      return newSet;
    });
  };

  return (
    <>
      {/* Sidebar */}
      <aside
        className={`${
          isCollapsed ? 'w-16' : 'w-64'
        } min-h-screen bg-white border-r border-blue-200 flex flex-col justify-between fixed left-0 top-0 z-30 transition-all duration-300 ease-in-out shadow-xl`}
      >
        <div>
          {/* Header with Logo and Collapse Button */}
          <div className="bg-white py-4 px-4 flex items-center justify-between border-b border-gray-100">
            {!isCollapsed && (
              <div className="flex items-center">
                <span>
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    strokeWidth={1.5} 
                    stroke="currentColor" 
                    className="w-8 h-8 text-yellow-500"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      d="M12 2v20m0 0c-4-2-6-6-6-10 0-2.5 2-4.5 4.5-4.5S15 7.5 15 10c0 4-2 8-6 10z" 
                    />
                  </svg>
                </span>
                <span
                  className="text-lg font-extrabold tracking-wide text-blue-700 ml-2"
                  style={{ fontFamily: 'cursive, sans-serif' }}
                >
                  Sprodeta
                </span>
              </div>
            )}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              {isCollapsed ? (
                <ChevronRightIcon className="w-5 h-5 text-gray-600" />
              ) : (
                <ChevronLeftIcon className="w-5 h-5 text-gray-600" />
              )}
            </button>
          </div>



          {/* Navigation */}
          <nav className={`${isCollapsed ? 'mt-4' : 'mt-6'} space-y-2 px-2`}>
            {navLinks.map((link) => {
              const isActive = activeItem === link.name || pathname === link.href;
              const isExpanded = expandedDropdowns.has(link.name);
              const hasChildren = link.children && link.children.length > 0;

              return (
                <div key={link.name}>
                  <button
                    onClick={() => {
                      if (hasChildren) {
                        toggleDropdown(link.name);
                      } else {
                        router.push(link.href);
                      }
                    }}
                    className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all duration-200 group ${
                      isActive
                        ? 'bg-blue-100 text-blue-700 font-semibold shadow-sm'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                    title={isCollapsed ? link.name : undefined}
                  >
                    <link.icon
                      className={`w-5 h-5 ${
                        isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'
                      }`}
                    />
                    {!isCollapsed && (
                      <>
                        <span className="truncate flex-1">{link.name}</span>
                        {hasChildren && (
                          <div className="ml-auto">
                            {isExpanded ? (
                              <ChevronUpIcon className="w-4 h-4 text-gray-400" />
                            ) : (
                              <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                            )}
                          </div>
                        )}
                      </>
                    )}
                  </button>

                  {/* Dropdown Children */}
                  {hasChildren && isExpanded && !isCollapsed && (
                    <div className="ml-6 mt-2 space-y-1">
                      {link.children!.map((child) => {
                        const childIsActive = activeItem === child.name || pathname === child.href;

                        return (
                          <button
                            key={child.name}
                            onClick={() => router.push(child.href)}
                            className={`w-full flex items-center gap-3 px-4 py-2 rounded-lg text-left transition-all duration-200 group text-sm ${
                              childIsActive
                                ? 'bg-blue-50 text-blue-600 font-medium'
                                : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700'
                            }`}
                          >
                            <child.icon
                              className={`w-4 h-4 ${
                                childIsActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-600'
                              }`}
                            />
                            <span className="truncate">{child.name}</span>
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>
        </div>

        {/* User Section */}
        <div className={`px-4 py-4 border-t border-gray-100 ${isCollapsed ? 'text-center' : ''}`}>
          {isCollapsed ? (
            <div className="flex justify-center">
              <button
                onClick={handleLogout}
                className="p-2 rounded-lg hover:bg-red-50 transition-colors"
                title="Logout"
              >
                <PowerIcon className="w-5 h-5 text-red-500" />
              </button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div>
                <div className="text-xs font-semibold text-gray-700 truncate">
                  {userName}
                </div>
                <div className="text-xs text-gray-500">
                  {userRole}
                </div>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 rounded-lg hover:bg-red-50 transition-colors"
                title="Logout"
              >
                <PowerIcon className="w-5 h-5 text-red-500" />
              </button>
            </div>
          )}
        </div>
      </aside>

      {/* Main Content */}
      <div className={`flex-1 ${isCollapsed ? 'ml-16' : 'ml-64'} transition-all duration-300 ease-in-out`}>
        {children}
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100 animate-in fade-in-0 zoom-in-95">
            {/* Header */}
            <div className="px-6 pt-6 pb-4">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <PowerIcon className="w-8 h-8 text-red-600" />
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Sign Out</h3>
                <p className="text-gray-600 text-xs leading-relaxed">
                  Are you sure you want to sign out of your account? You'll need to sign in again to access your dashboard.
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="px-6 pb-6">
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => setShowLogoutConfirm(false)}
                  className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 font-medium text-sm hover:scale-105 active:scale-95"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmLogout}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-xl transition-all duration-200 font-medium text-sm flex items-center justify-center space-x-2 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl"
                >
                  <PowerIcon className="w-4 h-4" />
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
