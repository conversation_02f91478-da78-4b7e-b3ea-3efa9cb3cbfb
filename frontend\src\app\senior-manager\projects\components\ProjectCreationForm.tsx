"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/services/api";

interface ProjectFormData {
  title: string;
  description: string;
  objectives: string[];
  goals: string[];
  beneficiaries: string;
  location: string;
  startDate: string;
  endDate: string;
  category: string;
  initialBudget: string;
  attachment: File | null;
}

type ProjectFormErrors = Partial<Omit<ProjectFormData, "objectives" | "goals" | "attachment">> & {
  objectives?: string;
  goals?: string;
  attachment?: string;
};

interface ProjectCreationFormProps {
  onProjectCreated: (projectId: string, projectTitle: string) => void;
}

type FormStep = 1 | 2 | 3 | 4;

export default function ProjectCreationForm({ onProjectCreated }: ProjectCreationFormProps) {
  const [currentStep, setCurrentStep] = useState<FormStep>(1);
  const [formData, setFormData] = useState<ProjectFormData>({
    title: "",
    description: "",
    objectives: [""],
    goals: [""],
    beneficiaries: "",
    location: "",
    startDate: "",
    endDate: "",
    category: "",
    initialBudget: "",
    attachment: null,
  });

  const [errors, setErrors] = useState<ProjectFormErrors>({});
  const [currentUserId, setCurrentUserId] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [districts, setDistricts] = useState<{ _id: string; name: string }[]>([]);
  const [loadingDistricts, setLoadingDistricts] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoadingDistricts(true);
        const [profileRes, districtsRes] = await Promise.all([
          api.getProfile(),
          api.getDistricts()
        ]);

        if (profileRes?._id) setCurrentUserId(profileRes._id);
        setDistricts(districtsRes.data || []);
      } catch (err) {
        console.error("Error loading initial data:", err);
      } finally {
        setLoadingDistricts(false);
      }
    };
    fetchInitialData();
  }, []);

  const validateForm = () => {
    const newErrors: ProjectFormErrors = {};

    // Title validation
    if (!formData.title || !formData.title.trim()) {
      newErrors.title = "Project title is required";
    } else if (formData.title.trim().length < 3) {
      newErrors.title = "Project title must be at least 3 characters long";
    } else if (formData.title.trim().length > 100) {
      newErrors.title = "Project title cannot exceed 100 characters";
    }

    // Description validation
    if (!formData.description || !formData.description.trim()) {
      newErrors.description = "Project description is required";
    } else if (formData.description.trim().length < 10) {
      newErrors.description = "Description must be at least 10 characters long";
    } else if (formData.description.trim().length > 1000) {
      newErrors.description = "Description cannot exceed 1000 characters";
    }

    // Objectives validation
    if (!formData.objectives || formData.objectives.length === 0) {
      newErrors.objectives = "At least one objective is required";
    } else {
      const invalidObjectives = formData.objectives.some(obj => !obj || !obj.trim());
      const emptyObjectives = formData.objectives.filter(obj => obj && obj.trim()).length === 0;
      if (invalidObjectives || emptyObjectives) {
        newErrors.objectives = "All objectives must be filled and cannot be empty";
      }
    }

    // Goals validation
    if (!formData.goals || formData.goals.length === 0) {
      newErrors.goals = "At least one goal is required";
    } else {
      const invalidGoals = formData.goals.some(goal => !goal || !goal.trim());
      const emptyGoals = formData.goals.filter(goal => goal && goal.trim()).length === 0;
      if (invalidGoals || emptyGoals) {
        newErrors.goals = "All goals must be filled and cannot be empty";
      }
    }

    // Beneficiaries validation
    if (!formData.beneficiaries || !formData.beneficiaries.trim()) {
      newErrors.beneficiaries = "Project beneficiaries are required";
    } else if (formData.beneficiaries.trim().length < 10) {
      newErrors.beneficiaries = "Beneficiaries description must be at least 10 characters long";
    }

    // Location validation
    if (!formData.location || !formData.location.trim()) {
      newErrors.location = "Please select a district";
    }

    // Date validation
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison

    if (!formData.startDate) {
      newErrors.startDate = "Start date is required";
    } else {
      const startDate = new Date(formData.startDate);
      if (startDate < today) {
        newErrors.startDate = "Start date cannot be in the past";
      }
    }

    if (!formData.endDate) {
      newErrors.endDate = "End date is required";
    } else if (formData.startDate) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(formData.endDate);

      if (endDate <= startDate) {
        newErrors.endDate = "End date must be after start date";
      }

      // Check if project duration is reasonable (not more than 5 years)
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays > 1825) { // 5 years
        newErrors.endDate = "Project duration cannot exceed 5 years";
      }

      // Check minimum duration (at least 1 day)
      if (diffDays < 1) {
        newErrors.endDate = "Project must be at least 1 day long";
      }
    }

    // Category validation
    if (!formData.category || !formData.category.trim()) {
      newErrors.category = "Project category is required";
    } else if (formData.category.trim().length < 2) {
      newErrors.category = "Category must be at least 2 characters long";
    }

    // Budget validation
    if (!formData.initialBudget || !formData.initialBudget.trim()) {
      newErrors.initialBudget = "Initial budget is required";
    } else {
      const budget = Number(formData.initialBudget);
      if (isNaN(budget)) {
        newErrors.initialBudget = "Budget must be a valid number";
      } else if (budget <= 0) {
        newErrors.initialBudget = "Budget must be greater than 0";
      } else if (budget > 1000000000) { // 1 billion limit
        newErrors.initialBudget = "Budget cannot exceed 1,000,000,000 MWK";
      }
    }

    // Attachment validation (required)
    if (!formData.attachment) {
      newErrors.attachment = "Project approval document is required";
    } else {
      // Validate file type
      const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
      const fileName = formData.attachment.name.toLowerCase();
      const isValidType = allowedTypes.some(type => fileName.endsWith(type));

      if (!isValidType) {
        newErrors.attachment = "Please upload a valid file (PDF, DOC, DOCX, JPG, PNG)";
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (formData.attachment.size > maxSize) {
        newErrors.attachment = "File size cannot exceed 10MB";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);

    // For now, we'll just send the filename as attachment
    // In a real implementation, you would upload the file to a server/cloud storage first
    const payload = {
      title: formData.title,
      description: formData.description,
      objectives: formData.objectives.filter(obj => obj.trim()),
      goals: formData.goals.filter(goal => goal.trim()),
      beneficiaries: formData.beneficiaries,
      location: formData.location, // This will be the district ID
      startDate: formData.startDate,
      endDate: formData.endDate,
      category: formData.category,
      initialBudget: Number(formData.initialBudget),
      attachment: formData.attachment ? formData.attachment.name : null, // Send filename for now
      createdBy: currentUserId,
    };

    try {
      const res = await api.createProject(payload);
      const projectId = res.data?.data?.projectId;
      if (projectId) {
        alert("Project created successfully! Please proceed to project planning.");
        onProjectCreated(projectId, formData.title);
      } else {
        alert("Project creation successful!");
      }
    } catch (err: any) {
      console.error("Failed to create project:", err);

      // Handle validation errors from backend
      if (err.response?.data?.errors && Array.isArray(err.response.data.errors)) {
        const backendErrors = err.response.data.errors.join('\n');
        alert(`Validation failed:\n${backendErrors}`);
      } else if (err.response?.data?.message) {
        alert(`Error: ${err.response.data.message}`);
      } else {
        alert("An error occurred while creating the project. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleObjectiveChange = (index: number, value: string) => {
    const updatedObjectives = [...formData.objectives];
    updatedObjectives[index] = value;
    setFormData({ ...formData, objectives: updatedObjectives });
  };

  const addObjective = () => setFormData({ ...formData, objectives: [...formData.objectives, ""] });

  const removeObjective = (index: number) => {
    const updatedObjectives = formData.objectives.filter((_, i) => i !== index);
    setFormData({ ...formData, objectives: updatedObjectives });
  };

  const handleGoalChange = (index: number, value: string) => {
    const updatedGoals = [...formData.goals];
    updatedGoals[index] = value;
    setFormData({ ...formData, goals: updatedGoals });
  };

  const addGoal = () => setFormData({ ...formData, goals: [...formData.goals, ""] });

  const removeGoal = (index: number) => {
    const updatedGoals = formData.goals.filter((_, i) => i !== index);
    setFormData({ ...formData, goals: updatedGoals });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData({ ...formData, attachment: file });
  };

  const validateStep = (step: FormStep): boolean => {
    const newErrors: ProjectFormErrors = {};

    switch (step) {
      case 1:
        if (!formData.title.trim()) newErrors.title = "Project title is required";
        if (!formData.description.trim()) newErrors.description = "Project description is required";
        break;
      case 2:
        if (formData.objectives.filter(obj => obj.trim()).length === 0) {
          newErrors.objectives = "At least one objective is required";
        }
        if (formData.goals.filter(goal => goal.trim()).length === 0) {
          newErrors.goals = "At least one goal is required";
        }
        if (!formData.beneficiaries.trim()) newErrors.beneficiaries = "Project beneficiaries are required";
        break;
      case 3:
        if (!formData.location) newErrors.location = "Project location is required";
        if (!formData.startDate) newErrors.startDate = "Start date is required";
        if (!formData.endDate) newErrors.endDate = "End date is required";
        if (!formData.category.trim()) newErrors.category = "Project category is required";
        if (formData.startDate && formData.endDate && formData.startDate >= formData.endDate) {
          newErrors.endDate = "End date must be after start date";
        }
        break;
      case 4:
        if (!formData.initialBudget || Number(formData.initialBudget) <= 0) {
          newErrors.initialBudget = "Initial budget must be greater than 0";
        }
        if (!formData.attachment) newErrors.attachment = "Project approval document is required";
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, 4) as FormStep);
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1) as FormStep);
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return "Project Title & Description";
      case 2: return "Objectives, Goals & Beneficiaries";
      case 3: return "Location, Timeline & Category";
      case 4: return "Budget & Approval Documents";
      default: return "Project Creation";
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            {/* Project Title */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Project Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter project title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400"
              />
              {errors.title && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.title}</p>}
            </div>

            {/* Project Description */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Project Description <span className="text-red-500">*</span>
              </label>
              <textarea
                placeholder="Provide a detailed description of the project"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400 h-32 resize-none"
              />
              {errors.description && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.description}</p>}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            {/* Project Objectives */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-700">
                Project Objectives <span className="text-red-500">*</span>
              </label>
              <div className="space-y-3">
                {formData.objectives.map((objective, index) => (
                  <div key={index} className="flex gap-3 items-start">
                    <div className="flex-1">
                      <input
                        placeholder={`Enter objective ${index + 1}`}
                        value={objective}
                        onChange={(e) => handleObjectiveChange(index, e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400"
                      />
                    </div>
                    {formData.objectives.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeObjective(index)}
                        className="mt-3 p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
                        title="Remove objective"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                ))}
              </div>
              <button
                type="button"
                onClick={addObjective}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Objective
              </button>
              {errors.objectives && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.objectives}</p>}
            </div>

            {/* Project Goals */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-700">
                Project Goals <span className="text-red-500">*</span>
              </label>
              <div className="space-y-3">
                {formData.goals.map((goal, index) => (
                  <div key={index} className="flex gap-3 items-start">
                    <div className="flex-1">
                      <input
                        placeholder={`Enter goal ${index + 1}`}
                        value={goal}
                        onChange={(e) => handleGoalChange(index, e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400"
                      />
                    </div>
                    {formData.goals.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeGoal(index)}
                        className="mt-3 p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
                        title="Remove goal"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                ))}
              </div>
              <button
                type="button"
                onClick={addGoal}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Goal
              </button>
              {errors.goals && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.goals}</p>}
            </div>

            {/* Project Beneficiaries */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Project Beneficiaries <span className="text-red-500">*</span>
              </label>
              <textarea
                placeholder="Describe who will benefit from this project"
                value={formData.beneficiaries}
                onChange={(e) => setFormData({ ...formData, beneficiaries: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400 h-24 resize-none"
              />
              {errors.beneficiaries && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.beneficiaries}</p>}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            {/* Location */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Project Location (District) <span className="text-red-500">*</span>
              </label>
              {loadingDistricts ? (
                <div className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  <span className="text-gray-500">Loading districts...</span>
                </div>
              ) : (
                <select
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
                >
                  <option value="">Select a district</option>
                  {districts.map((district) => (
                    <option key={district._id} value={district._id}>
                      {district.name}
                    </option>
                  ))}
                </select>
              )}
              {errors.location && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.location}</p>}
              {!loadingDistricts && districts.length === 0 && (
                <p className="text-amber-600 text-sm mt-1 flex items-center">
                  <span className="mr-1">⚠</span>No districts available. Please contact the administrator.
                </p>
              )}
            </div>

            {/* Timeline */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-700">
                Project Timeline <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                  {errors.startDate && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.startDate}</p>}
                </div>
                <div className="space-y-2">
                  <label className="block text-xs font-medium text-gray-600 uppercase tracking-wide">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                  {errors.endDate && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.endDate}</p>}
                </div>
              </div>
            </div>

            {/* Project Category */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Project Category <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter project category (e.g., Education, Health, Infrastructure)"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400"
              />
              {errors.category && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.category}</p>}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-8">
            {/* Initial Budget */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Initial Budget <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <span className="text-gray-500 text-sm font-medium">MWK</span>
                </div>
                <input
                  type="number"
                  placeholder="0.00"
                  value={formData.initialBudget}
                  onChange={(e) => setFormData({ ...formData, initialBudget: e.target.value })}
                  className="w-full pl-16 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400"
                />
              </div>
              {errors.initialBudget && <p className="text-red-500 text-sm mt-1 flex items-center"><span className="mr-1">⚠</span>{errors.initialBudget}</p>}
            </div>

            {/* Attachment */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Project Approval Documents <span className="text-red-500">*</span>
              </label>
              <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200 ${
                errors.attachment ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-blue-400'
              }`}>
                <div className="space-y-2">
                  <svg className={`mx-auto h-12 w-12 ${errors.attachment ? 'text-red-400' : 'text-gray-400'}`} stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <div className="text-sm text-gray-600">
                    <label htmlFor="file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                      <span>Upload approval document</span>
                      <input
                        id="file-upload"
                        type="file"
                        onChange={handleFileChange}
                        className="sr-only"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        required
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">
                    <span className="font-medium text-red-600">Required:</span> PDF, DOC, DOCX, JPG, PNG up to 10MB
                  </p>
                </div>
              </div>
              {formData.attachment && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm font-medium text-green-800">
                      File selected: {formData.attachment.name}
                    </span>
                  </div>
                  <p className="text-xs text-green-600 mt-1">
                    Size: {(formData.attachment.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              )}
              {errors.attachment && (
                <p className="text-red-500 text-sm mt-2 flex items-center">
                  <span className="mr-1">⚠</span>{errors.attachment}
                </p>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-8 bg-white shadow-lg rounded-xl border border-gray-100">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Create New Project</h2>
        <p className="text-gray-600">Step {currentStep} of 4: {getStepTitle()}</p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          {[1, 2, 3, 4].map((step) => (
            <div
              key={step}
              className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                step <= currentStep
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {step}
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / 4) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Step Content */}
        {renderStepContent()}

        {/* Navigation Buttons */}
        <div className="pt-6 border-t border-gray-200">
          <div className="flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Previous
            </button>

            {currentStep < 4 ? (
              <button
                type="button"
                onClick={nextStep}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                Next
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            ) : (
              <button
                type="submit"
                disabled={isSubmitting}
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating Project...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Project
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
