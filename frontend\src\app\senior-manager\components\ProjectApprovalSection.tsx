'use client';

import React, { useState } from 'react';

interface ProjectDetails {
  id: string;
  name: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string;
  fundingSource: string;
  objectives: {
    title: string;
    goals: { title: string; target: string }[];
  }[];
  budget: {
    category: string;
    amount: number;
  }[];
}

export default function ProjectApprovalSection() {
  const [selectedProject, setSelectedProject] = useState<ProjectDetails | null>(null);

  // Mock data - replace with actual data from your backend
  const pendingProjects: ProjectDetails[] = [];

  const handleApprove = (projectId: string) => {
    // Handle project approval logic here
    console.log('Approving project:', projectId);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'MKW',
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">Project Approval</h2>

      <div className="space-y-4">
        {pendingProjects.map(project => (
          <div key={project.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">{project.name}</h3>
                <p className="text-sm text-gray-600">{project.location}</p>
              </div>
              <button
                onClick={() => setSelectedProject(selectedProject?.id === project.id ? null : project)}
                className="text-blue-600 hover:text-blue-800"
              >
                {selectedProject?.id === project.id ? 'Close Details' : 'View Details'}
              </button>
            </div>

            {selectedProject?.id === project.id && (
              <div className="mt-4 space-y-6">
                {/* Project Details */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Start Date</p>
                    <p className="font-medium">{new Date(project.startDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">End Date</p>
                    <p className="font-medium">{new Date(project.endDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Funding Source</p>
                    <p className="font-medium">{project.fundingSource}</p>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Description</h4>
                  <p className="text-gray-600">{project.description}</p>
                </div>

                {/* Objectives and Goals */}
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Objectives & Goals</h4>
                  <div className="space-y-3">
                    {project.objectives.map((objective, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded">
                        <p className="font-medium text-gray-800">{objective.title}</p>
                        <ul className="mt-2 space-y-1">
                          {objective.goals.map((goal, goalIndex) => (
                            <li key={goalIndex} className="text-sm text-gray-600">
                              • {goal.title}: {goal.target}
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Budget Breakdown */}
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Budget Breakdown</h4>
                  <div className="space-y-2">
                    {project.budget.map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-gray-600">{item.category}</span>
                        <span className="font-medium">{formatCurrency(item.amount)}</span>
                      </div>
                    ))}
                    <div className="border-t pt-2 mt-2 flex justify-between font-medium">
                      <span>Total Budget</span>
                      <span>{formatCurrency(project.budget.reduce((sum, item) => sum + item.amount, 0))}</span>
                    </div>
                  </div>
                </div>

                {/* Approval Button */}
                <div className="flex justify-end">
                  <button
                    onClick={() => handleApprove(project.id)}
                    className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Approve & Launch Project
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}

        {pendingProjects.length === 0 && (
          <p className="text-center text-gray-600">No projects pending approval</p>
        )}
      </div>
    </div>
  );
} 