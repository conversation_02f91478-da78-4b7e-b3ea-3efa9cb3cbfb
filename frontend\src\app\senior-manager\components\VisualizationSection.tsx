'use client';

import React from 'react';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface VisualizationSectionProps {
  barChartData: any;
  pieChartData: any;
  options: any;
  pieOptions: any;
  sectionTitle?: string;
}

const VisualizationSection: React.FC<VisualizationSectionProps> = ({ barChartData, pieChartData, options, pieOptions, sectionTitle }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">{sectionTitle || 'Project Analytics'}</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <Bar data={barChartData} options={options} />
        </div>
        <div>
          <Pie data={pieChartData} options={pieOptions} />
        </div>
      </div>
    </div>
  );
};

export default VisualizationSection; 