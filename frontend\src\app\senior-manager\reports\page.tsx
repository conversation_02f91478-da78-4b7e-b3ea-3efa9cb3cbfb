"use client";
import { useState, useRef, useEffect, useCallback } from 'react';
import { ProjectPlansProvider } from "../projects/components/ProjectPlansContext";
import { Bar, Pie, Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { api } from '../../../services/api';
import api2 from '@/utils/api';
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

interface ReportProject {
  id: string;
  name: string;
  district: string;
  sector: string;
  duration: string;
  status: string;
  objectives: string[];
  goals: string[];
  inputs: { item: string; quantity: number; date: string }[];
  outputs: { result: string; value: number; date: string }[];
  budget: { approved: number; used: number };
  analytics: {
    completion: number;
    budgetUsed: number;
    barChart: number[];
    pieChart: number[];
  };
}

export default function SeniorManagerReportsPage() {
  const [loading, setLoading] = useState(true);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  const [reportsData, setReportsData] = useState<any>(null);
  const [statistics, setStatistics] = useState<any>({});
  const [charts, setCharts] = useState<any>({});
  const [selectedProjectId, setSelectedProjectId] = useState<string>('all');
  const [filteredData, setFilteredData] = useState<any>(null);
  const [projects, setProjects] = useState<any[]>([]);

  // Fetch reports data and projects
  useEffect(() => {
    const fetchReportsData = async () => {
      setLoading(true);
      try {
        // Fetch reports data
        const response = await api2.get('/api/v1/reports/data');
        const data = response.data.data;
        console.log('📊 [Data] Raw reports data:', data);
        console.log('📊 [Data] Projects data:', data.projects);
        console.log('📊 [Data] First project structure:', data.projects?.[0]);

        setReportsData(data.projects);
        setStatistics(data.statistics);
        setCharts(data.charts);

        // Fetch all projects for dropdown
        const projectsResponse = await api.getProjects();
        setProjects(projectsResponse.data || []);

        // Initialize with all projects data
        setFilteredData(data);
      } catch (error) {
        console.error('Failed to fetch reports data:', error);
        setToast({ message: 'Failed to load reports data', type: 'error' });
      } finally {
        setLoading(false);
      }
    };

    fetchReportsData();
  }, []);

 
  const generateProjectSpecificCharts = useCallback(async (project: any) => {
    console.log('🔍 [Charts] Starting chart generation for project:', project.title || project.name);

    const totalBudget = project.initialBudget || project.budget || 0;
    const usedBudget = project.usedBudget || project.budgetUsed || 0;
    const remainingBudget = totalBudget - usedBudget;
    const utilization = totalBudget > 0 ? Math.round((usedBudget / totalBudget) * 100) : 0;





    console.log(' [Charts] Chart generation completed for project:', project.title);

    return {
      projectProgress: {
        labels: [project.title || project.name || 'Project'],
        datasets: [
          {
            label: 'Progress %',
            data: [project.progress || 0],
            backgroundColor: ['rgba(59, 130, 246, 0.8)'],
            borderColor: ['rgba(59, 130, 246, 1)'],
            borderWidth: 1,
          },
        ],
      },
      budgetUtilization: {
        labels: [project.title || project.name || 'Project'],
        datasets: [
          {
            label: 'Budget Utilization %',
            data: [utilization],
            backgroundColor: ['rgba(16, 185, 129, 0.8)'],
            borderColor: ['rgba(16, 185, 129, 1)'],
            borderWidth: 1,
          },
        ],
      },
      overallBudgetPie: {
        labels: ['Used Budget', 'Remaining Budget'],
        datasets: [
          {
            data: [usedBudget, remainingBudget],
            backgroundColor: ['rgba(239, 68, 68, 0.8)', 'rgba(34, 197, 94, 0.8)'],
            borderColor: ['rgba(239, 68, 68, 1)', 'rgba(34, 197, 94, 1)'],
            borderWidth: 1,
          },
        ],
      },

    };
  }, []);

  // Filter data based on selected project
  useEffect(() => {
    if (!reportsData || !statistics || !charts) return;

    if (selectedProjectId === 'all') {
      
      setFilteredData({
        projects: reportsData,
        statistics: statistics,
        charts: charts
      });
    } else {
      
      const selectedProject = reportsData.find((p: any) =>
        p._id === selectedProjectId || p.projectId === selectedProjectId
      );

      if (selectedProject) {
        // Generate filtered statistics and charts for the selected project asynchronously
        console.log('🚀 [Main] About to call chart generation functions...');
        console.log('📊 [Main] Selected project:', selectedProject);
        console.log('📊 [Main] Reports data length:', reportsData?.length);

        Promise.all([
          generateProjectSpecificStats(selectedProject),
          generateProjectSpecificCharts(selectedProject)
        ]).then(([projectStats, projectCharts]) => {
          console.log('✅ [Main] Chart generation completed');
          console.log('📊 [Main] Project charts result:', projectCharts);
          // Use the selected project without additional KPI data
          const updatedProject = selectedProject;

          setFilteredData({
            projects: [updatedProject],
            statistics: projectStats,
            charts: projectCharts
          });
        }).catch(error => {
          console.error('Error generating project data:', error);
          // Fallback to basic data without KPI information
          const basicStats = {
            totalProjects: 1,
            activeProjects: selectedProject.status === 'inprogress' ? 1 : 0,
            completedProjects: selectedProject.status === 'completed' ? 1 : 0,
            totalBudget: selectedProject.initialBudget || selectedProject.budget || 0,
            usedBudget: selectedProject.usedBudget || selectedProject.budgetUsed || 0,
            remainingBudget: (selectedProject.initialBudget || selectedProject.budget || 0) - (selectedProject.usedBudget || selectedProject.budgetUsed || 0),
            budgetUtilization: selectedProject.initialBudget > 0 ? Math.round(((selectedProject.usedBudget || 0) / selectedProject.initialBudget) * 100) : 0,
            totalActivities: selectedProject.totalActivities || 0,
            completedActivities: selectedProject.completedActivities || 0,
            inProgressActivities: selectedProject.inProgressActivities || 0,
            pendingActivities: (selectedProject.totalActivities || 0) - (selectedProject.completedActivities || 0) - (selectedProject.inProgressActivities || 0)
          };
          const basicCharts = generateBasicProjectCharts(selectedProject);

          setFilteredData({
            projects: [selectedProject],
            statistics: basicStats,
            charts: basicCharts
          });
        });
      }
    }
  }, [selectedProjectId, reportsData, statistics, charts, generateProjectSpecificCharts]);

  // Generate project-specific statistics
  const generateProjectSpecificStats = async (project: any) => {
    const totalBudget = project.initialBudget || project.budget || 0;
    const usedBudget = project.usedBudget || project.budgetUsed || 0;
    const remainingBudget = totalBudget - usedBudget;
    const utilization = totalBudget > 0 ? Math.round((usedBudget / totalBudget) * 100) : 0;



    return {
      totalProjects: 1,
      activeProjects: project.status === 'inprogress' ? 1 : 0,
      completedProjects: project.status === 'completed' ? 1 : 0,
      totalBudget: totalBudget,
      usedBudget: usedBudget,
      remainingBudget: remainingBudget,
      budgetUtilization: utilization,
      totalActivities: project.totalActivities || 0,
      completedActivities: project.completedActivities || 0,
      inProgressActivities: project.inProgressActivities || 0,
      pendingActivities: (project.totalActivities || 0) - (project.completedActivities || 0) - (project.inProgressActivities || 0)
    };
  };



  // Generate basic project charts (fallback without KPI data)
  const generateBasicProjectCharts = (project: any) => {
    const totalBudget = project.initialBudget || project.budget || 0;
    const usedBudget = project.usedBudget || project.budgetUsed || 0;
    const remainingBudget = totalBudget - usedBudget;
    const utilization = totalBudget > 0 ? Math.round((usedBudget / totalBudget) * 100) : 0;



    return {
      projectProgress: {
        labels: [project.title || project.name || 'Project'],
        datasets: [
          {
            label: 'Progress %',
            data: [project.progress || 0],
            backgroundColor: ['rgba(59, 130, 246, 0.8)'],
            borderColor: ['rgba(59, 130, 246, 1)'],
            borderWidth: 1,
          },
        ],
      },
      budgetUtilization: {
        labels: [project.title || project.name || 'Project'],
        datasets: [
          {
            label: 'Budget Utilization %',
            data: [utilization],
            backgroundColor: ['rgba(16, 185, 129, 0.8)'],
            borderColor: ['rgba(16, 185, 129, 1)'],
            borderWidth: 1,
          },
        ],
      },
      overallBudgetPie: {
        labels: ['Used Budget', 'Remaining Budget'],
        datasets: [
          {
            data: [usedBudget, remainingBudget],
            backgroundColor: ['rgba(239, 68, 68, 0.8)', 'rgba(34, 197, 94, 0.8)'],
            borderColor: ['rgba(239, 68, 68, 1)', 'rgba(34, 197, 94, 1)'],
            borderWidth: 1,
          },
        ],
      }
    };
  };

  // Helper function to convert chart to image
  const chartToImage = (chartId: string): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.querySelector(`#${chartId} canvas`) as HTMLCanvasElement;
      if (canvas) {
        resolve(canvas.toDataURL('image/png'));
      } else {
        resolve('');
      }
    });
  };

  // Export overall analytics PDF
  const exportOverallAnalyticsPDF = async () => {
    try {
      setToast({ message: 'Generating overall analytics report...', type: 'success' });

      // Create PDF document
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      let yPosition = 20;

      // Title
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Overall Portfolio Analytics Report', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 15;

      // Date
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 20;

      // Portfolio Summary
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Portfolio Summary', 20, yPosition);
      yPosition += 10;

      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'normal');
      const summaryData = [
        `Total Projects: ${filteredData?.statistics?.totalProjects || 0}`,
        `Active Projects: ${filteredData?.statistics?.activeProjects || 0}`,
        `Completed Projects: ${filteredData?.statistics?.completedProjects || 0}`,
        `Total Budget: MWK ${(filteredData?.statistics?.totalBudget || 0).toLocaleString()}`,
        `Used Budget: MWK ${(filteredData?.statistics?.usedBudget || 0).toLocaleString()}`,
        `Budget Utilization: ${filteredData?.statistics?.budgetUtilization || 0}%`,
        `Total Activities: ${filteredData?.statistics?.totalActivities || 0}`,
        `Completed Activities: ${filteredData?.statistics?.completedActivities || 0}`
      ];

      summaryData.forEach((item, index) => {
        pdf.text(item, 25, yPosition + (index * 6));
      });
      yPosition += summaryData.length * 6 + 15;

      // Add charts if available
      if (filteredData?.charts) {
        // Project Progress Chart
        try {
          const progressChart = document.getElementById('project-progress-chart');
          if (progressChart) {
            const canvas = progressChart.querySelector('canvas');
            if (canvas) {
              if (yPosition > pageHeight - 80) {
                pdf.addPage();
                yPosition = 20;
              }
              pdf.setFontSize(14);
              pdf.setFont('helvetica', 'bold');
              pdf.text('Project Progress Overview', 20, yPosition);
              yPosition += 10;

              const imgData = canvas.toDataURL('image/png');
              pdf.addImage(imgData, 'PNG', 20, yPosition, 170, 60);
              yPosition += 70;
            }
          }
        } catch (error) {
          console.warn('Could not add progress chart to PDF:', error);
        }

        // Budget Utilization Chart
        try {
          const budgetChart = document.getElementById('budget-utilization-chart');
          if (budgetChart) {
            const canvas = budgetChart.querySelector('canvas');
            if (canvas) {
              if (yPosition > pageHeight - 80) {
                pdf.addPage();
                yPosition = 20;
              }
              pdf.setFontSize(14);
              pdf.setFont('helvetica', 'bold');
              pdf.text('Budget Utilization Analysis', 20, yPosition);
              yPosition += 10;

              const imgData = canvas.toDataURL('image/png');
              pdf.addImage(imgData, 'PNG', 20, yPosition, 170, 60);
              yPosition += 70;
            }
          }
        } catch (error) {
          console.warn('Could not add budget chart to PDF:', error);
        }

        // Budget Distribution Pie Chart
        try {
          const budgetPieChart = document.getElementById('overall-budget-pie-chart');
          if (budgetPieChart) {
            const canvas = budgetPieChart.querySelector('canvas');
            if (canvas) {
              if (yPosition > pageHeight - 80) {
                pdf.addPage();
                yPosition = 20;
              }
              pdf.setFontSize(14);
              pdf.setFont('helvetica', 'bold');
              pdf.text('Budget Distribution', 20, yPosition);
              yPosition += 10;

              const imgData = canvas.toDataURL('image/png');
              pdf.addImage(imgData, 'PNG', 20, yPosition, 170, 60);
              yPosition += 70;
            }
          }
        } catch (error) {
          console.warn('Could not add budget pie chart to PDF:', error);
        }


      }

      // Footer
      const totalPages = pdf.internal.pages.length - 1;
      for (let i = 1; i <= totalPages; i++) {
        pdf.setPage(i);
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        pdf.text(`Page ${i} of ${totalPages}`, pageWidth - 30, pageHeight - 10, { align: 'right' });
        pdf.text('© 2025 SPRODETA. All rights reserved.', 20, pageHeight - 10);
      }

      // Save the PDF
      pdf.save(`Overall_Portfolio_Analytics_${new Date().toISOString().split('T')[0]}.pdf`);
      setToast({ message: 'Overall analytics report exported successfully!', type: 'success' });
    } catch (error) {
      console.error('Error exporting overall analytics PDF:', error);
      setToast({ message: 'Failed to export overall analytics report', type: 'error' });
    }
  };

  const handleExportProjectPDF = async (project: any) => {
    if (!project) return;

    try {
      setToast({ message: 'Generating PDF report...', type: 'success' });

      // Capture chart images before generating PDF
      const [projectProgressImage, budgetUtilizationImage, budgetPieImage] = await Promise.all([
        chartToImage('project-progress-chart'),
        chartToImage('budget-utilization-chart'),
        chartToImage('overall-budget-pie-chart')
      ]);

      // Fetch detailed project data
      const [projectDetailsResponse, projectPlanResponse] = await Promise.all([
        api2.get(`/api/v1/project/${project._id || project.projectId}`).catch(() => ({ data: null })),
        api2.get(`/api/v1/project-plan/plan/${project._id || project.projectId}`).catch(() => ({ data: null }))
      ]);

      // Try to get project activities, but don't fail if not accessible
      let projectActivitiesResponse = { data: { data: [] } };
      try {
        projectActivitiesResponse = await api2.get(`/api/v1/project/${project._id || project.projectId}/activities`);
      } catch (error) {
        console.log('Could not fetch project activities, using project data instead');
      }

      const projectDetails = projectDetailsResponse.data?.projects?.[0] || project;
      const projectPlan = projectPlanResponse.data;
      const projectActivities = projectActivitiesResponse.data?.data || [];

      const doc = new jsPDF();
      let yPosition = 20;

      // Title
      doc.setFontSize(20);
      doc.setFont('helvetica', 'bold');
      doc.text(`Project Report: ${projectDetails.title || projectDetails.name}`, 14, yPosition);
      yPosition += 20;

      // Project Details Section
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('1. Project Details', 14, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      const projectInfo = [
        `Project Name: ${projectDetails.title || projectDetails.name}`,
        `Description: ${projectDetails.description || 'N/A'}`,
        `Status: ${projectDetails.status || 'N/A'}`,
        `Start Date: ${projectDetails.startDate ? new Date(projectDetails.startDate).toLocaleDateString() : 'N/A'}`,
        `End Date: ${projectDetails.endDate ? new Date(projectDetails.endDate).toLocaleDateString() : 'N/A'}`,
        `Location: ${projectDetails.location?.name || projectDetails.location || 'N/A'}`,
        `Category: ${projectDetails.category || 'N/A'}`,
        `Created By: ${projectDetails.createdBy?.fullName || 'N/A'}`,
        `Assigned To: ${projectDetails.assignedTo?.fullName || 'N/A'}`
      ];

      projectInfo.forEach(info => {
        if (yPosition > 270) {
          doc.addPage();
          yPosition = 20;
        }
        doc.text(info, 20, yPosition);
        yPosition += 8;
      });

      yPosition += 10;

      // Budget Information
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('Budget Information', 14, yPosition);
      yPosition += 8;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      const totalBudget = projectDetails.initialBudget || projectDetails.budget || 0;
      const usedBudget = projectDetails.usedBudget || projectDetails.budgetUsed || 0;
      const remainingBudget = totalBudget - usedBudget;
      const utilization = totalBudget > 0 ? Math.round((usedBudget / totalBudget) * 100) : 0;

      const budgetInfo = [
        `Total Budget: MK ${totalBudget.toLocaleString()}`,
        `Budget Used: MK ${usedBudget.toLocaleString()}`,
        `Budget Remaining: MK ${remainingBudget.toLocaleString()}`,
        `Budget Utilization: ${utilization}%`
      ];

      budgetInfo.forEach(info => {
        if (yPosition > 270) {
          doc.addPage();
          yPosition = 20;
        }
        doc.text(info, 20, yPosition);
        yPosition += 8;
      });

      yPosition += 15;

      // Add Charts Section
      if (projectProgressImage || budgetUtilizationImage || budgetPieImage) {
        if (yPosition > 200) {
          doc.addPage();
          yPosition = 20;
        }

        doc.setFontSize(16);
        doc.setFont('helvetica', 'bold');
        doc.text('Charts and Analytics', 14, yPosition);
        yPosition += 15;

        // Project Progress Chart
        if (projectProgressImage) {
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.text('Project Progress Chart', 14, yPosition);
          yPosition += 10;

          try {
            doc.addImage(projectProgressImage, 'PNG', 14, yPosition, 180, 100);
            yPosition += 110;
          } catch (error) {
            console.log('Could not add project progress chart to PDF');
            yPosition += 10;
          }
        }

        // Budget Utilization Chart
        if (budgetUtilizationImage && yPosition < 200) {
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.text('Budget Utilization Chart', 14, yPosition);
          yPosition += 10;

          try {
            doc.addImage(budgetUtilizationImage, 'PNG', 14, yPosition, 180, 100);
            yPosition += 110;
          } catch (error) {
            console.log('Could not add budget utilization chart to PDF');
            yPosition += 10;
          }
        }

        // Add new page for pie charts if needed
        if (budgetPieImage && yPosition > 150) {
          doc.addPage();
          yPosition = 20;
        }

        // Budget Distribution Pie Chart
        if (budgetPieImage) {
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.text('Budget Distribution', 14, yPosition);
          yPosition += 10;

          try {
            doc.addImage(budgetPieImage, 'PNG', 14, yPosition, 90, 90);
          } catch (error) {
            console.log('Could not add budget pie chart to PDF');
          }
        }



        yPosition += 100;
      }

      // Project Plan Section
      if (projectPlan) {
        if (yPosition > 200) {
          doc.addPage();
          yPosition = 20;
        }

        doc.setFontSize(16);
        doc.setFont('helvetica', 'bold');
        doc.text('2. Project Plan', 14, yPosition);
        yPosition += 10;

        // Activities
        if (projectPlan.activities && projectPlan.activities.length > 0) {
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.text('Planned Activities:', 20, yPosition);
          yPosition += 8;

          doc.setFontSize(11);
          doc.setFont('helvetica', 'normal');
          projectPlan.activities.forEach((activity: any, index: number) => {
            if (yPosition > 270) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(`${index + 1}. ${activity.title}`, 25, yPosition);
            yPosition += 6;
            if (activity.description) {
              const splitDescription = doc.splitTextToSize(`   ${activity.description}`, 160);
              splitDescription.forEach((line: string) => {
                if (yPosition > 270) {
                  doc.addPage();
                  yPosition = 20;
                }
                doc.text(line, 25, yPosition);
                yPosition += 5;
              });
            }
            yPosition += 3;
          });
        }

        // Milestones
        if (projectPlan.milestones && projectPlan.milestones.length > 0) {
          yPosition += 5;
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.text('Milestones:', 20, yPosition);
          yPosition += 8;

          doc.setFontSize(11);
          doc.setFont('helvetica', 'normal');
          projectPlan.milestones.forEach((milestone: any, index: number) => {
            if (yPosition > 270) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(`${index + 1}. ${milestone.title} (Due: ${new Date(milestone.deadline).toLocaleDateString()})`, 25, yPosition);
            yPosition += 6;
            if (milestone.description) {
              const splitDescription = doc.splitTextToSize(`   ${milestone.description}`, 160);
              splitDescription.forEach((line: string) => {
                if (yPosition > 270) {
                  doc.addPage();
                  yPosition = 20;
                }
                doc.text(line, 25, yPosition);
                yPosition += 5;
              });
            }
            yPosition += 3;
          });
        }

        // Deliverables
        if (projectPlan.deliverables && projectPlan.deliverables.length > 0) {
          yPosition += 5;
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.text('Deliverables:', 20, yPosition);
          yPosition += 8;

          doc.setFontSize(11);
          doc.setFont('helvetica', 'normal');
          projectPlan.deliverables.forEach((deliverable: any, index: number) => {
            if (yPosition > 270) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(`${index + 1}. ${deliverable.title} (Due: ${new Date(deliverable.deadline).toLocaleDateString()})`, 25, yPosition);
            yPosition += 6;
            if (deliverable.description) {
              const splitDescription = doc.splitTextToSize(`   ${deliverable.description}`, 160);
              splitDescription.forEach((line: string) => {
                if (yPosition > 270) {
                  doc.addPage();
                  yPosition = 20;
                }
                doc.text(line, 25, yPosition);
                yPosition += 5;
              });
            }
            yPosition += 3;
          });
        }
      }

      // Add new page for charts and progress
      doc.addPage();
      yPosition = 20;

      // Project Progress Section
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('3. Project Progress Analysis', 14, yPosition);
      yPosition += 15;

      // Activity Progress
      const totalActivities = projectActivities.length || project.totalActivities || 0;
      const completedActivities = projectActivities.filter((a: any) => a.status === 'completed').length || project.completedActivities || 0;
      const inProgressActivities = projectActivities.filter((a: any) => a.status === 'inprogress').length || project.inProgressActivities || 0;
      const pendingActivities = totalActivities - completedActivities - inProgressActivities;

      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('Activity Progress:', 14, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(`Total Activities: ${totalActivities}`, 20, yPosition);
      yPosition += 8;
      doc.text(`Completed Activities: ${completedActivities}`, 20, yPosition);
      yPosition += 8;
      doc.text(`In Progress Activities: ${inProgressActivities}`, 20, yPosition);
      yPosition += 8;
      doc.text(`Pending Activities: ${pendingActivities}`, 20, yPosition);
      yPosition += 8;
      doc.text(`Completion Rate: ${totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0}%`, 20, yPosition);
      yPosition += 15;

      // Budget Utilization Chart (Text representation)
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('4. Budget Utilization Analysis', 14, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(`Budget Utilization: ${utilization}%`, 20, yPosition);
      yPosition += 8;

      // Draw a simple progress bar for budget utilization
      const barWidth = 100;
      const barHeight = 10;
      const filledWidth = (utilization / 100) * barWidth;

      doc.setDrawColor(200, 200, 200);
      doc.rect(20, yPosition, barWidth, barHeight);

      if (utilization > 0) {
        doc.setFillColor(59, 130, 246);
        doc.rect(20, yPosition, filledWidth, barHeight, 'F');
      }

      yPosition += 20;

      // Overall Budget Distribution
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('5. Budget Distribution', 14, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(`Used: MK ${usedBudget.toLocaleString()} (${utilization}%)`, 20, yPosition);
      yPosition += 8;
      doc.text(`Remaining: MK ${remainingBudget.toLocaleString()} (${100 - utilization}%)`, 20, yPosition);
      yPosition += 15;

      // Activity Details Table
      if (projectActivities.length > 0) {
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('6. Activity Details', 14, yPosition);
        yPosition += 10;

        const tableData = projectActivities.map((activity: any) => [
          activity.title || 'N/A',
          activity.status || 'N/A',
          activity.priority || 'N/A',
          activity.assignedTo?.fullName || 'Unassigned',
          activity.budget ? `MK ${activity.budget.toLocaleString()}` : 'N/A'
        ]);

        (doc as any).autoTable({
          startY: yPosition,
          head: [['Activity', 'Status', 'Priority', 'Assigned To', 'Budget']],
          body: tableData,
          theme: 'grid',
          styles: { fontSize: 9 },
          headStyles: { fillColor: [59, 130, 246] },
          columnStyles: {
            0: { cellWidth: 50 },
            1: { cellWidth: 25 },
            2: { cellWidth: 25 },
            3: { cellWidth: 40 },
            4: { cellWidth: 30 }
          }
        });
      }

      // Footer
      const pageCount = doc.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 285);
        doc.text(`Page ${i} of ${pageCount}`, 180, 285);
      }

      doc.save(`${projectDetails.title || projectDetails.name}_Comprehensive_Report_${new Date().toISOString().split('T')[0]}.pdf`);
      setToast({ message: 'Comprehensive PDF report exported successfully!', type: 'success' });
      setTimeout(() => setToast(null), 3000);
    } catch (err) {
      console.error('PDF export error:', err);
      setToast({ message: 'Failed to export comprehensive PDF report.', type: 'error' });
      setTimeout(() => setToast(null), 3000);
    }
  };



  // Chart options
  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Project Analytics',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
      },
    },
  };

  const pieOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  };

  return (
    <ProjectPlansProvider>
      <div className="min-h-screen bg-gray-100">
        <div className="p-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
            {selectedProjectId === 'all' && (
              <button
                onClick={() => exportOverallAnalyticsPDF()}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors shadow-md hover:shadow-lg"
                title="Export Overall Analytics Report"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export PDF
              </button>
            )}
          </div>

          {/* Project Selection */}
          <div className="bg-white rounded-xl shadow-md p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Select Project for Analysis</h2>
              {selectedProjectId !== 'all' && (
                <div className="text-sm text-gray-600">
                  Showing analytics for: <span className="font-medium text-blue-600">
                    {projects.find(p => p._id === selectedProjectId)?.title || 'Selected Project'}
                  </span>
                </div>
              )}
            </div>

            <div className="max-w-md">
              <select
                value={selectedProjectId}
                onChange={(e) => setSelectedProjectId(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
              >
                <option value="all">All Projects - Overall Analytics</option>
                {projects.map((project) => (
                  <option key={project._id} value={project._id}>
                    {project.title || project.name} - {project.status || 'Unknown Status'}
                  </option>
                ))}
              </select>
            </div>

            {/* Quick Stats for Selected Project */}
            {selectedProjectId !== 'all' && filteredData?.statistics && (
              <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {filteredData.statistics.budgetUtilization}%
                  </div>
                  <div className="text-sm text-gray-600">Budget Utilization</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {filteredData.statistics.completedActivities}
                  </div>
                  <div className="text-sm text-gray-600">Completed Activities</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {filteredData.statistics.inProgressActivities}
                  </div>
                  <div className="text-sm text-gray-600">In Progress Activities</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    MWK {filteredData.statistics.remainingBudget?.toLocaleString() || '0'}
                  </div>
                  <div className="text-sm text-gray-600">Remaining Budget</div>
                </div>
              </div>
            )}
          </div>

          {/* Toast Notification */}
          {toast && (
            <div className={`fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
              toast.type === 'success' ? 'bg-green-500' : 'bg-red-500'
            }`}>
              {toast.message}
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>


              {/* Charts Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                {/* Project Progress Chart */}
                <div className="bg-white p-6 rounded-xl shadow-md" id="project-progress-chart">
                  <h3 className="text-lg font-semibold mb-4">
                    {selectedProjectId === 'all' ? 'Overall Project Progress' : 'Project Progress'}
                  </h3>
                  {filteredData?.charts?.projectProgress ? (
                    <Bar data={filteredData.charts.projectProgress} options={chartOptions} />
                  ) : (
                    <div className="h-64 flex items-center justify-center text-gray-500">
                      No project progress data available
                    </div>
                  )}
                </div>

                {/* Budget Utilization Chart */}
                <div className="bg-white p-6 rounded-xl shadow-md" id="budget-utilization-chart">
                  <h3 className="text-lg font-semibold mb-4">
                    {selectedProjectId === 'all' ? 'Overall Budget Utilization' : 'Budget Utilization'}
                  </h3>
                  {filteredData?.charts?.budgetUtilization ? (
                    <Bar data={filteredData.charts.budgetUtilization} options={chartOptions} />
                  ) : (
                    <div className="h-64 flex items-center justify-center text-gray-500">
                      No budget utilization data available
                    </div>
                  )}
                </div>

                {/* Budget Distribution Pie Chart */}
                <div className="bg-white p-6 rounded-xl shadow-md" id="overall-budget-pie-chart">
                  <h3 className="text-lg font-semibold mb-4">
                    {selectedProjectId === 'all' ? 'Overall Budget Distribution' : 'Budget Distribution'}
                  </h3>
                  {filteredData?.charts?.overallBudgetPie ? (
                    <Pie data={filteredData.charts.overallBudgetPie} options={pieOptions} />
                  ) : (
                    <div className="h-64 flex items-center justify-center text-gray-500">
                      No budget distribution data available
                    </div>
                  )}
                </div>




              </div>

              {/* Individual Project Table - Only show when specific project is selected */}
              {selectedProjectId !== 'all' && (
               
                <div className="bg-white rounded-xl shadow-md p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">
                    {selectedProjectId === 'all' ? 'All Projects Details' : 'Selected Project Details'}
                  </h3>
                  {selectedProjectId !== 'all' && (
                    <div className="text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                      Individual Project Analysis
                    </div>
                  )}
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full text-sm">
                    <thead>
                      <tr className="text-gray-500 text-xs uppercase border-b">
                        <th className="px-4 py-3 text-left">Project Name</th>
                        <th className="px-4 py-3 text-left">Status</th>
                        <th className="px-4 py-3 text-left">Progress</th>
                        <th className="px-4 py-3 text-left">Budget</th>
                        <th className="px-4 py-3 text-left">Used</th>
                        <th className="px-4 py-3 text-left">Utilization</th>
                        <th className="px-4 py-3 text-left">Activities</th>
                        <th className="px-4 py-3 text-left">Export</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredData?.projects && filteredData.projects.length > 0 ? (
                        filteredData.projects.map((project: any, index: number) => (
                          <tr key={index} className="border-b hover:bg-gray-50">
                            <td className="px-4 py-3 font-medium text-gray-900">
                              {project.name || project.title || 'Unnamed Project'}
                            </td>
                            <td className="px-4 py-3">
                              <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                                project.status === 'inprogress' ? 'bg-blue-100 text-blue-700' :
                                project.status === 'completed' ? 'bg-green-100 text-green-700' :
                                'bg-yellow-100 text-yellow-700'
                              }`}>
                                {project.status || 'Unknown'}
                              </span>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex items-center">
                                <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                  <div
                                    className="bg-blue-600 h-2 rounded-full"
                                    style={{ width: `${project.progress || 0}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm">{project.progress || 0}%</span>
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              MK {project.budget?.toLocaleString() || project.initialBudget?.toLocaleString() || '0'}
                            </td>
                            <td className="px-4 py-3">
                              MK {project.budgetUsed?.toLocaleString() || project.usedBudget?.toLocaleString() || '0'}
                            </td>
                            <td className="px-4 py-3">
                              {project.budgetUtilization || project.budgetUtilization === 0 ? project.budgetUtilization : 0}%
                            </td>
                            <td className="px-4 py-3">
                              {project.completedActivities || 0}/{project.totalActivities || 0}
                            </td>
                            <td className="px-4 py-3">
                              <button
                                onClick={() => handleExportProjectPDF(project)}
                                className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 flex items-center gap-1"
                                title="Export Project PDF"
                              >
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                PDF
                              </button>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                            {selectedProjectId === 'all' ? 'No project data available' : 'Selected project data not found'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
              )}
            </>
          )}
        </div>
      </div>
    </ProjectPlansProvider>
  );
}
