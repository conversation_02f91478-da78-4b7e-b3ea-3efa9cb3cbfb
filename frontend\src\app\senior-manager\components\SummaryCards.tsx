'use client';

import React, { useState } from 'react';

interface KPICardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  onClick?: () => void;
}

const KPICard: React.FC<KPICardProps> = ({ title, value, icon, onClick }) => (
  <div
    className="bg-white rounded-lg shadow-sm p-6 border border-blue-200 hover:shadow-md transition-shadow cursor-pointer"
    onClick={onClick}
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-gray-600 text-sm font-medium">{title}</p>
        <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
      </div>
      <div className="text-blue-600 bg-blue-50 p-2 rounded-lg">{icon}</div>
    </div>
  </div>
);

interface Project {
  _id: string;
  title?: string;
  name?: string;
  status: string;
  progress?: number;
  assignedTo?: {
    fullName?: string;
    name?: string;
  };
}

interface SummaryCardsProps {
  totalProjects: number;
  completedProjects: number;
  ongoingProjects: number;
  allProjects?: Project[];
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  projects: Project[];
}

const ProjectModal: React.FC<ModalProps> = ({ isOpen, onClose, title, projects }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {projects.length > 0 ? (
            <div className="space-y-4">
              {projects.map((project, index) => (
                <div key={project._id || index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-900">
                      {project.title || project.name || 'Unnamed Project'}
                    </h4>
                    <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                      project.status === 'completed' ? 'bg-green-100 text-green-700' :
                      project.status === 'inprogress' ? 'bg-blue-100 text-blue-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {project.status === 'inprogress' ? 'In Progress' :
                       project.status === 'completed' ? 'Completed' :
                       project.status || 'Unknown'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>
                      Assigned to: {project.assignedTo?.fullName || project.assignedTo?.name || 'Unassigned'}
                    </span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            (project.progress || 0) >= 100 ? 'bg-green-500' :
                            (project.progress || 0) >= 75 ? 'bg-blue-500' :
                            (project.progress || 0) >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${Math.min(project.progress || 0, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-xs font-medium min-w-[35px]">
                        {project.progress != null ? `${project.progress}%` : '0%'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <p>No projects found in this category.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const SummaryCards: React.FC<SummaryCardsProps> = ({
  totalProjects,
  completedProjects,
  ongoingProjects,
  allProjects = []
}) => {
  console.log('📊 SummaryCards received props:', {
    totalProjects,
    completedProjects,
    ongoingProjects,
    allProjectsCount: allProjects.length
  });

  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    title: string;
    projects: Project[];
  }>({
    isOpen: false,
    title: '',
    projects: []
  });

  const openModal = (title: string, filterFn: (project: Project) => boolean) => {
    console.log('🔍 Opening modal:', title);
    console.log('📊 All projects available:', allProjects.length);
    console.log('📊 All projects data:', allProjects);

    const filteredProjects = allProjects.filter(filterFn);
    console.log('📊 Filtered projects:', filteredProjects.length);

    setModalState({
      isOpen: true,
      title,
      projects: filteredProjects
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      title: '',
      projects: []
    });
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-1">
        <KPICard
          title="Total Projects"
          value={totalProjects}
          onClick={() => openModal('All Projects', () => true)}
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          }
        />
        <KPICard
          title="In Progress"
          value={ongoingProjects}
          onClick={() => openModal('Projects In Progress', (project) => project.status === 'inprogress')}
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
        <KPICard
          title="Completed"
          value={completedProjects}
          onClick={() => openModal('Completed Projects', (project) => project.status === 'completed')}
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
      </div>

      <ProjectModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        title={modalState.title}
        projects={modalState.projects}
      />
    </>
  );
};

export default SummaryCards;
