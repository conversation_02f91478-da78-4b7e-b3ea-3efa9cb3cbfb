'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeftIcon,
  UserIcon,
  BuildingOfficeIcon,
  CalendarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PaperAirplaneIcon,
  DocumentTextIcon
} from '@heroicons/react/24/solid';
import { TrashIcon } from '@heroicons/react/24/outline';
import { api } from '@/services/api';
import { toast } from 'react-hot-toast';

interface Activity {
  _id: string;
  title: string;
  description: string;
  project: {
    _id: string;
    name: string;
    assignedTo: {
      _id: string;
      fullName: string;
      email: string;
    };
  };
  endDate: string;
  budget: number;
  location: string;
  targetOutcome: string;
}

export default function SubmitReportPage() {
  const router = useRouter();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [reportContent, setReportContent] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  // Customizable form fields state
  const [formFields, setFormFields] = useState([
    { id: 'activity_id', type: 'select', label: 'Activity', required: true },
    { id: 'description', type: 'textarea', label: 'Report Description', required: true },
    { id: 'amountSpent', type: 'number', label: 'Amount Spent (MWK)', required: true },
  ]);
  const [showAddField, setShowAddField] = useState(false);
  const [newField, setNewField] = useState({ id: '', type: 'text', label: '', required: false });
  const [formValues, setFormValues] = useState<{[key: string]: string | number}>({
    activity_id: '',
    description: '',
    amountSpent: '',
  });
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    fetchAssignedActivities();

    // Monitor network status for ODK functionality
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const fetchAssignedActivities = async () => {
    try {
      setLoading(true);
      const response = await api.getAssignedActivities();
      const activitiesData = Array.isArray(response.data) ? response.data : response.data?.data || [];
      
      // Filter out activities that already have reports
      const pendingActivities = activitiesData.filter((activity: any) => 
        activity.status !== 'completed' && !activity.hasReport
      );
      
      setActivities(pendingActivities);
    } catch (error) {
      console.error('Failed to fetch activities:', error);
      toast.error('Failed to load activities');
    } finally {
      setLoading(false);
    }
  };

  const handleActivitySelect = (activity: Activity) => {
    setSelectedActivity(activity);
    // Update form values when activity is selected
    setFormValues(prev => ({ ...prev, activity_id: activity._id }));
  };

  // Handle adding new form fields
  const handleAddField = () => {
    if (newField.id && newField.label) {
      // Check if field ID already exists
      if (formFields.some(field => field.id === newField.id.toLowerCase().replace(/\s+/g, '_'))) {
        toast.error('Field ID already exists. Please use a unique ID.');
        return;
      }

      setFormFields([...formFields, { ...newField, id: newField.id.toLowerCase().replace(/\s+/g, '_') }]);
      setNewField({ id: '', type: 'text', label: '', required: false });
      setShowAddField(false);
      toast.success(`Field "${newField.label}" added successfully!`);
    } else {
      toast.error('Please provide both field ID and label');
    }
  };

  const handleDeleteField = (fieldId: string) => {
    // Prevent deletion of required default fields
    const defaultFields = ['activity_id', 'description', 'amountSpent'];
    if (defaultFields.includes(fieldId)) {
      toast.error('Cannot delete required default fields');
      return;
    }

    // Find the field to get its label for confirmation
    const fieldToDelete = formFields.find(field => field.id === fieldId);
    const fieldLabel = fieldToDelete?.label || 'this field';

    // Confirm deletion
    if (window.confirm(`Are you sure you want to delete "${fieldLabel}"? This action cannot be undone.`)) {
      // Remove field from formFields array
      setFormFields(formFields.filter(field => field.id !== fieldId));

      // Remove field value from formValues
      const updatedFormValues = { ...formValues };
      delete updatedFormValues[fieldId];
      setFormValues(updatedFormValues);

      toast.success(`Field "${fieldLabel}" deleted successfully!`);
    }
  };

  // Handle form field changes
  const handleFieldChange = async (id: string, value: string) => {
    setFormValues(prev => ({ ...prev, [id]: value }));

    // If activity is selected, update selected activity details
    if (id === 'activity_id' && value) {
      const activity = activities.find(a => a._id === value);
      if (activity) {
        setSelectedActivity(activity);
      }
    } else if (id === 'activity_id' && !value) {
      setSelectedActivity(null);
    }

    // Update report content if description field changes
    if (id === 'description') {
      setReportContent(value);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setAttachments(Array.from(e.target.files));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    const missingFields = formFields
      .filter(field => field.required && !formValues[field.id])
      .map(field => field.label);

    if (missingFields.length > 0) {
      toast.error(`Please fill in required fields: ${missingFields.join(', ')}`);
      return;
    }

    if (!selectedActivity) {
      toast.error('Please select an activity');
      return;
    }

    setSubmitting(true);
    try {
      // Prepare form data including custom fields
      const amountSpentValue = parseFloat(formValues.amountSpent as string) || 0;
      console.log('💰 Amount spent value:', amountSpentValue, 'Original:', formValues.amountSpent);

      const reportData = {
        activity_id: formValues.activity_id,
        description: formValues.description,
        amountSpent: amountSpentValue,
        attachments: attachments,
        customFields: Object.keys(formValues)
          .filter(key => !['activity_id', 'description', 'amountSpent'].includes(key))
          .reduce((obj, key) => {
            obj[key] = formValues[key];
            return obj;
          }, {} as {[key: string]: string})
      };

      console.log('📋 Report data being sent:', reportData);
      console.log('🔍 Selected activity details:', selectedActivity);
      console.log('🔍 Form values:', formValues);

      await api.submitActivityReport(reportData);

      toast.success(`Report submitted successfully to ${selectedActivity.project?.assignedTo?.fullName || 'Project Manager'}!`);

      // Reset form
      setSelectedActivity(null);
      setFormValues({
        activity_id: '',
        description: '',
        amountSpent: 0,
      });
      setAttachments([]);

      // Refresh activities list
      fetchAssignedActivities();

      // Navigate back to dashboard after a short delay
      setTimeout(() => {
        router.push('/field/dashboard');
      }, 2000);

    } catch (error: any) {
      console.error('Failed to submit report:', error);
      toast.error(error.message || 'Failed to submit report');
    } finally {
      setSubmitting(false);
    }
  };

  const isOverdue = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading activities...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Submit Activity Report</h1>
          <p className="mt-2 text-gray-600">
            Submit reports for your assigned activities to the supervising project manager
          </p>
        </div>

        <div className="space-y-6">
          {/* Activity Selection */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Select Activity</h2>
            
            {activities.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircleIcon className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <p className="text-gray-600">No pending activities found</p>
                <p className="text-sm text-gray-500 mt-2">
                  All your activities have been reported or completed
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-3">
                {activities.map((activity) => (
                  <div
                    key={activity._id}
                    onClick={() => handleActivitySelect(activity)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedActivity?._id === activity._id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium text-gray-900 text-sm truncate">{activity.title}</h3>
                          {isOverdue(activity.endDate) && (
                            <ExclamationTriangleIcon className="w-4 h-4 text-red-500 flex-shrink-0" />
                          )}
                        </div>
                        <p className="text-xs text-gray-600 truncate mt-1">{activity.project?.title || activity.project?.name || 'Unknown Project'}</p>
                      </div>
                      <div className="flex items-center text-xs text-gray-500 ml-4 flex-shrink-0">
                        <CalendarIcon className="w-3 h-3 mr-1" />
                        <span className="whitespace-nowrap">
                          {new Date(activity.endDate).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Customizable Report Form */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Customizable Report Form</h2>
                <div className="flex items-center space-x-2 mt-1">
                  <p className="text-sm text-gray-600">
                    Add custom fields as needed for comprehensive reporting
                  </p>
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                    isOnline ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-blue-500' : 'bg-red-500'}`}></div>
                    <span>{isOnline ? 'Online' : 'Offline'}</span>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAddField(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  + Add Field
                </button>
                <button
                  type="button"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  title="Use ODK for offline reporting - data will sync when network is available"
                  onClick={() => toast.info('ODK offline mode: Reports will be stored locally and synced when network is available')}
                >
                  📱 ODK Mode
                </button>
              </div>
            </div>

            {/* Add Field Modal */}
            {showAddField && (
              <div className="mb-6 p-6 border border-gray-200 rounded-lg bg-gray-50">
                <h3 className="text-lg font-semibold mb-4 text-gray-800">Add Custom Field</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Field ID</label>
                    <input
                      type="text"
                      value={newField.id}
                      onChange={(e) => setNewField({ ...newField, id: e.target.value })}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., location, budget_used"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Field Label</label>
                    <input
                      type="text"
                      value={newField.label}
                      onChange={(e) => setNewField({ ...newField, label: e.target.value })}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., Location, Budget Used"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Field Type</label>
                    <select
                      value={newField.type}
                      onChange={(e) => setNewField({ ...newField, type: e.target.value })}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="text">Text</option>
                      <option value="textarea">Text Area</option>
                      <option value="number">Number</option>
                      <option value="date">Date</option>
                      <option value="email">Email</option>
                      <option value="tel">Phone</option>
                    </select>
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={newField.required}
                        onChange={(e) => setNewField({ ...newField, required: e.target.checked })}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label className="ml-2 text-sm text-gray-700">Required field</label>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end space-x-3 mt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddField(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleAddField}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add Field
                  </button>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Dynamic Form Fields */}
              {formFields.map((field) => {
                const isDefaultField = ['activity_id', 'description', 'amountSpent'].includes(field.id);

                return (
                  <div key={field.id} className="relative">
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-sm font-medium text-gray-700">
                        {field.label}
                        {field.required && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      {!isDefaultField && (
                        <button
                          type="button"
                          onClick={() => handleDeleteField(field.id)}
                          className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                          title="Delete this field"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  {field.type === 'textarea' ? (
                    <textarea
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={field.id === 'description' ? 6 : 4}
                      required={field.required}
                      placeholder={`Enter ${field.label.toLowerCase()}`}
                      value={formValues[field.id] || ''}
                      onChange={e => handleFieldChange(field.id, e.target.value)}
                    />
                  ) : field.type === 'select' && field.id === 'activity_id' ? (
                    <select
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required={field.required}
                      value={formValues[field.id] || ''}
                      onChange={e => handleFieldChange(field.id, e.target.value)}
                    >
                      <option value="">Select {field.label.toLowerCase()}</option>
                      {activities.map((activity) => (
                        <option key={activity._id} value={activity._id}>
                          {activity.title}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type={field.type}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required={field.required}
                      placeholder={`Enter ${field.label.toLowerCase()}`}
                      value={formValues[field.id] || ''}
                      onChange={e => handleFieldChange(field.id, e.target.value)}
                    />
                  )}
                  </div>
                );
              })}

                {/* Attachments */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Attachments (Optional)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      multiple
                      onChange={handleFileChange}
                      className="hidden"
                      id="file-upload"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    />
                    <label htmlFor="file-upload" className="cursor-pointer">
                      <div className="text-gray-600">
                        <svg className="mx-auto h-8 w-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <span className="text-sm">Click to upload files</span>
                        <p className="text-xs text-gray-500 mt-1">PDF, DOC, JPG, PNG up to 10MB each</p>
                      </div>
                    </label>
                  </div>
                  {attachments.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">Selected files:</p>
                      <ul className="text-xs text-gray-500">
                        {attachments.map((file, index) => (
                          <li key={index}>• {file.name}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

              {/* Project Manager Info - Show when activity is selected */}
              {selectedActivity && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <UserIcon className="w-4 h-4 text-blue-600" />
                    </div>
                    <h3 className="text-sm font-semibold text-blue-800">📋 Report Submission Details</h3>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex flex-col">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Activity</span>
                        <span className="font-medium text-gray-900">{selectedActivity.title}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Project</span>
                        <span className="font-medium text-gray-900">{selectedActivity.project?.title || selectedActivity.project?.name || 'Unknown Project'}</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex flex-col">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Submit To (Project Manager)</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">
                              {selectedActivity.project?.assignedTo?.fullName?.charAt(0) || 'PM'}
                            </span>
                          </div>
                          <span className="font-semibold text-blue-700">
                            {selectedActivity.project?.assignedTo?.fullName || 'Project Manager'}
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Manager Email</span>
                        <span className="text-sm text-gray-600">
                          {selectedActivity.project?.assignedTo?.email || 'No email available'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <CheckCircleIcon className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div className="text-xs text-blue-700">
                        <strong>Notification Process:</strong> When you submit this report, it will be automatically sent to the project manager above via email and in-app notification.
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={submitting || !formValues.activity_id}
                className="w-full flex items-center justify-center py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting Report...
                  </>
                ) : selectedActivity ? (
                  <>
                    <PaperAirplaneIcon className="w-4 h-4 mr-2" />
                    Submit Report to {selectedActivity.project?.assignedTo?.fullName || 'Project Manager'}
                  </>
                ) : (
                  <>
                    <PaperAirplaneIcon className="w-4 h-4 mr-2" />
                    Submit Activity Report
                  </>
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
