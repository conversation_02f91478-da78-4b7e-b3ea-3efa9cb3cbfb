"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  HomeIcon,
  ClipboardIcon,
  ChartBarIcon,
  UserCircleIcon,
  CogIcon,
  BellIcon,
  ArrowRightOnRectangleIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/solid";
import CollapsibleSidebar, { getGreeting, getGreetingEmoji } from "@/components/CollapsibleSidebar";
import NotificationBell from "@/components/NotificationBell";


const navLinks = [
  { name: "Home", href: "/senior-manager/dashboard", icon: HomeIcon },
  {
    name: "Projects",
    href: "/senior-manager/projects",
    icon: ClipboardIcon,
    children: [
      { name: "Projects", href: "/senior-manager/projects", icon: ClipboardIcon },
      { name: "Project Progress", href: "/senior-manager/project-progress", icon: ChartBarIcon },
      { name: "Reports", href: "/senior-manager/reports", icon: DocumentTextIcon },
    ]
  },
];

export default function SeniorManagerLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [userName, setUserName] = useState('Senior Manager');
  const [greeting, setGreeting] = useState('Hello');
  const [greetingEmoji, setGreetingEmoji] = useState('👋');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    // Get user name from localStorage or API
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        setUserName(user.fullName || user.name || 'Senior Manager');
      } catch (error) {
        // Keep default name
      }
    }

    // Set time-based greeting
    setGreeting(getGreeting());
    setGreetingEmoji(getGreetingEmoji());

    // Update greeting every minute
    const interval = setInterval(() => {
      setGreeting(getGreeting());
      setGreetingEmoji(getGreetingEmoji());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-[#f4f6fa]">
      <style jsx global>{`
        /* Custom scrollbar styles for senior manager pages */
        ::-webkit-scrollbar {
          height: 6px;
          width: 6px;
        }

        ::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #555;
        }

        /* Firefox scrollbar */
        * {
          scrollbar-width: thin;
          scrollbar-color: #888 #f1f1f1;
        }
      `}</style>
      <CollapsibleSidebar
        navLinks={navLinks}
        userName={userName}
        userRole="Senior Manager"
        onCollapseChange={setIsSidebarCollapsed}
      >
        {/* Header */}
        <header className="fixed top-0 left-0 right-0 z-20 flex items-center justify-between bg-white border-b border-gray-100 px-6 py-3 shadow-sm">
          <div className="text-lg font-bold text-sky-700 tracking-tight">Dashboard</div>
          <div className="flex-1 flex justify-center">
            <div className="text-base font-medium text-gray-700">
              {greeting}, {userName}! {greetingEmoji}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <NotificationBell userRole="seniorManager" />
            <button
              onClick={() => router.push('/senior-manager/profile')}
              className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden hover:bg-gray-300 transition-colors"
            >
              <UserCircleIcon className="w-6 h-6 text-sky-400" />
            </button>
          </div>
        </header>
        <main className="pt-16 p-4">{children}</main>
      </CollapsibleSidebar>
    </div>
  );
}