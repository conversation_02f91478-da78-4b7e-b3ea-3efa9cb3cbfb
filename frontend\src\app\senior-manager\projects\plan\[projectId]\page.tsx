"use client";

import { useParams, useSearchParams } from "next/navigation";
import ProjectPlanView from "../../components/ProjectPlanView";

export default function ProjectPlanPage() {
  const { projectId } = useParams();
  const searchParams = useSearchParams();

  const startDate = searchParams.get("startDate");
  const endDate = searchParams.get("endDate");

  let duration: number | null = null;
  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const msPerDay = 1000 * 60 * 60 * 24;
    duration = Math.ceil((end.getTime() - start.getTime()) / msPerDay);
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white shadow rounded">
      <ProjectPlanView
        projectId={projectId as string}
        duration={duration}
        onSubmit={async (data) => {
          console.log("Plan submitted:", data);
          try {
            // Import API service dynamically
            const { api } = await import('@/services/api');
            await api.createProjectPlan(data);

            // Redirect to dashboard after successful submission
            window.location.href = '/senior-manager/dashboard';
          } catch (error) {
            console.error('Failed to submit project plan:', error);
            // You could add a toast notification here
            alert('Failed to submit project plan. Please try again.');
          }
        }}
      />
    </div>
  );
}
