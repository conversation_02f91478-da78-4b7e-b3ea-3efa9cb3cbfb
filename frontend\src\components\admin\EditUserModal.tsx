import React, { useState, useEffect } from "react";
import { User } from "@/types/types";
import api from "@/utils/api";

interface EditUserModalProps {
  user: User;
  setIsEditUserModalOpen: (isOpen: boolean) => void;
  callBack: () => void;
}

const EditUserModal: React.FC<EditUserModalProps> = ({
  user,
  setIsEditUserModalOpen,
  callBack,
}) => {
  const [formData, setFormData] = useState({
    fullName: user.fullName || user.name || "",
    email: user.email || "",
    phoneNumber: user.phoneNumber || "",
    district: user.district || "",
    department: user.department || "",
    bio: user.bio || "",
    experience: user.experience || 0,
    skills: user.skills || [],
    role: user.role || "",
  });

  const [districts, setDistricts] = useState([]);
  const [roles, setRoles] = useState([]);
  const [isUpdatingUser, setIsUpdatingUser] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  useEffect(() => {
    fetchDistricts();
    fetchRoles();
  }, []);

  const fetchDistricts = async () => {
    try {
      const response = await api.get('/api/v1/district');
      const districts = Array.isArray(response.data) ? response.data : response.data?.data || [];
      setDistricts(districts);
    } catch (error) {
      console.error("Failed to fetch districts:", error);
      setError("Failed to load districts. Please refresh the page.");
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await api.get('/api/v1/roles');
      const roles = Array.isArray(response.data) ? response.data : response.data?.data || [];
      setRoles(roles);
    } catch (error) {
      console.error("Failed to fetch roles:", error);
      setError("Failed to load roles. Please refresh the page.");
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "experience" ? Number(value) : value,
    }));
  };

  const handleSkillsChange = (skills: string[]) => {
    setFormData((prev) => ({
      ...prev,
      skills,
    }));
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdatingUser(true);
    setError("");
    setSuccess("");

    // Validate required fields
    if (!formData.fullName || !formData.email || !formData.phoneNumber) {
      setError("Please fill in all required fields");
      setIsUpdatingUser(false);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError("Please enter a valid email address");
      setIsUpdatingUser(false);
      return;
    }

    try {
      const response = await api.patch(`/api/v1/user/${user._id || user.id}`, formData);
      
      if (response.data && (response.data.status === 'success' || response.data._id)) {
        setSuccess("User updated successfully!");
        await callBack();
        
        // Close modal after a short delay to show success message
        setTimeout(() => {
          setIsEditUserModalOpen(false);
        }, 1500);
      }
    } catch (error: any) {
      console.error("Error updating user:", error);

      // Safe error message extraction
      let errorMessage = "Failed to update user. Please try again.";

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.status === 400) {
        errorMessage = "User with this email or phone number already exists";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsUpdatingUser(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto">
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-sky-600 text-2xl font-bold"
          onClick={() => setIsEditUserModalOpen(false)}
        >
          &times;
        </button>
        <h2 className="text-xl font-bold text-sky-700 mb-6">Edit User</h2>
        
        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
            {error}
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-lg text-sm">
            {success}
          </div>
        )}

        <form onSubmit={handleUpdateUser} className="space-y-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.fullName}
              onChange={handleInputChange}
              name="fullName"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.email}
              onChange={handleInputChange}
              name="email"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Phone Number *
            </label>
            <input
              type="tel"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              name="phoneNumber"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              District
            </label>
            <select
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.district}
              onChange={handleInputChange}
              name="district"
            >
              <option value="">Select District</option>
              {districts.map((district: any) => (
                <option key={district._id} value={district._id}>
                  {district.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Department
            </label>
            <input
              type="text"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.department}
              onChange={handleInputChange}
              name="department"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Bio
            </label>
            <textarea
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.bio}
              onChange={handleInputChange}
              name="bio"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Experience (years)
            </label>
            <input
              type="number"
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.experience}
              onChange={handleInputChange}
              name="experience"
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Role
            </label>
            <select
              className="w-full px-4 py-2 border border-sky-200 rounded-lg focus:ring-2 focus:ring-sky-400 focus:outline-none"
              value={formData.role}
              onChange={handleInputChange}
              name="role"
            >
              <option value="">Select Role</option>
              {roles.map((role: any) => (
                <option key={role._id} value={role._id}>
                  {role.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              className="px-4 py-2 rounded-lg border border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100 font-semibold"
              onClick={() => setIsEditUserModalOpen(false)}
              disabled={isUpdatingUser}
            >
              Cancel
            </button>
            <button
              disabled={isUpdatingUser}
              type="submit"
              className="px-6 py-2 rounded-lg bg-sky-600 text-white font-semibold hover:bg-sky-700 shadow transition disabled:opacity-50"
            >
              {isUpdatingUser ? "Updating..." : "Update User"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditUserModal;
