"use client";
import React, { useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  HomeIcon,
  UserGroupIcon,
  BanknotesIcon,
  ClipboardIcon,
  UserCircleIcon,
  CalendarDaysIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
} from "@heroicons/react/24/solid";
import CollapsibleSidebar, { getGreeting, getGreetingEmoji } from "@/components/CollapsibleSidebar";
import NotificationBell from "@/components/NotificationBell";

interface AccountantLayoutProps {
  children: React.ReactNode;
}

const navLinks = [
  { name: "Dashboard", href: "/accountant/dashboard", icon: HomeIcon },
  { name: "Budgets", href: "/accountant/budgets", icon: BanknotesIcon },
];

export default function AccountantLayout({ children }: AccountantLayoutProps) {
  const router = useRouter();
  const [userName, setUserName] = useState('Accountant');
  const [greeting, setGreeting] = useState('Hello');
  const [greetingEmoji, setGreetingEmoji] = useState('👋');

  useEffect(() => {
    // Get user name from localStorage or API
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        setUserName(user.fullName || user.name || 'Accountant');
      } catch (error) {
        // Keep default name
      }
    }

    // Set time-based greeting
    setGreeting(getGreeting());
    setGreetingEmoji(getGreetingEmoji());

    // Update greeting every minute
    const interval = setInterval(() => {
      setGreeting(getGreeting());
      setGreetingEmoji(getGreetingEmoji());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-[#f4f6fa]">
      <style jsx global>{`
        /* Custom scrollbar styles for accountant pages */
        ::-webkit-scrollbar {
          height: 6px;
          width: 6px;
        }

        ::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #555;
        }

        /* Firefox scrollbar */
        * {
          scrollbar-width: thin;
          scrollbar-color: #888 #f1f1f1;
        }
      `}</style>
      <CollapsibleSidebar
        navLinks={navLinks}
        userName={userName}
        userRole="Accountant"
      >
        {/* Header */}
        <header className="sticky top-0 z-10 flex items-center justify-between bg-white border-b border-gray-100 px-6 py-3">
          <div className="text-lg font-bold text-sky-700 tracking-tight">Dashboard</div>
          <div className="flex-1 flex justify-center">
            <div className="text-base font-medium text-gray-700">
              {greeting}, {userName}! {greetingEmoji}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <NotificationBell userRole="accountant" />
            <button
              onClick={() => router.push('/accountant/profile')}
              className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden hover:bg-gray-300 transition-colors"
            >
              <UserCircleIcon className="w-6 h-6 text-sky-400" />
            </button>
          </div>
        </header>
        <main className="p-6">{children}</main>
      </CollapsibleSidebar>
    </div>
  );
}