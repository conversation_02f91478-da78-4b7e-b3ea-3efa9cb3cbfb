"use client";

import React, { useState, useEffect } from "react";
import { api } from "@/services/api";
import { XMarkIcon, CalendarIcon, MapPinIcon, CurrencyDollarIcon, UserIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface ProjectsListProps {
  // Add any props if needed
}

export default function ProjectsList({ }: ProjectsListProps) {
  const [projects, setProjects] = useState<any[]>([]);
  const [loadingProjects, setLoadingProjects] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [projectDetails, setProjectDetails] = useState<any>(null);
  const [loadingDetails, setLoadingDetails] = useState(false);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoadingProjects(true);
      const res = await api.getProjects();
      setProjects(res.data || []);
    } catch (err) {
      setProjects([]);
    } finally {
      setLoadingProjects(false);
    }
  };

  const fetchProjectDetails = async (projectId: string) => {
    try {
      setLoadingDetails(true);

      // Try to fetch additional project details
      let projectData = null;
      let objectives = [];
      let goals = [];
      let activities = [];

      try {
        const projectRes = await api.getProjectById(projectId);
        projectData = projectRes.data;
      } catch (error) {
        console.warn("Could not fetch project details:", error);
      }

      try {
        const objectivesRes = await api.getProjectObjectives(projectId);
        objectives = objectivesRes.data || [];
      } catch (error) {
        console.warn("Could not fetch project objectives:", error);
      }

      try {
        const goalsRes = await api.getProjectGoals(projectId);
        goals = goalsRes.data || [];
      } catch (error) {
        console.warn("Could not fetch project goals:", error);
      }

      try {
        const activitiesRes = await api.getProjectActivities(projectId);
        activities = activitiesRes.data || [];
      } catch (error) {
        console.warn("Could not fetch project activities:", error);
      }

      setProjectDetails({
        project: projectData,
        objectives,
        goals,
        activities
      });
    } catch (err) {
      console.error("Failed to fetch project details:", err);
      setProjectDetails({
        project: null,
        objectives: [],
        goals: [],
        activities: []
      });
    } finally {
      setLoadingDetails(false);
    }
  };

  const handleProjectClick = (project: any) => {
    console.log("🔍 Selected project data:", project);
    console.log("📋 Assigned manager info:", {
      assignedTo: project.assignedTo,
      assignedManager: project.assignedManager,
      projectManager: project.projectManager,
      manager: project.manager
    });
    setSelectedProject(project);
    setShowModal(true);
    fetchProjectDetails(project._id);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedProject(null);
    setProjectDetails(null);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return "MWK 0.00";
    return `MWK ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">All Projects</h2>
        <button
          onClick={loadProjects}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>

      {loadingProjects ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading projects...</span>
        </div>
      ) : projects.length === 0 ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No projects</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new project.</p>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Project Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {projects.map((project) => (
                <tr
                  key={project._id}
                  onClick={() => handleProjectClick(project)}
                  className="hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {project.title || project.name}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 max-w-xs">
                      <p className="line-clamp-2">{project.description || "No description provided"}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      project.status === 'completed'
                        ? 'bg-green-100 text-green-800'
                        : project.status === 'inprogress'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {project.status === 'inprogress' ? 'In Progress' :
                       project.status === 'completed' ? 'Completed' :
                       project.status || 'Active'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {project.district?.name ||
                       (typeof project.location === 'string' ? project.location : project.location?.name) ||
                       "Not specified"}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Project Details Modal */}
      {showModal && selectedProject && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">
                {selectedProject.title || selectedProject.name}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {loadingDetails ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading project details...</span>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Project Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <DocumentTextIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Description</p>
                        <p className="text-sm text-gray-600">{selectedProject.description || "No description provided"}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Location</p>
                        <p className="text-sm text-gray-600">
                          {selectedProject.district?.name ||
                           (typeof selectedProject.location === 'string' ? selectedProject.location : selectedProject.location?.name) ||
                           "Not specified"}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CalendarIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Timeline</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(selectedProject.startDate)} - {formatDate(selectedProject.endDate)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <CurrencyDollarIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Budget</p>
                        <p className="text-sm text-gray-600">{formatCurrency(selectedProject.initialBudget || selectedProject.budget)}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <UserIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Assigned Manager</p>
                        <p className="text-sm text-gray-600">
                          {(() => {
                            // Try different possible field structures
                            if (selectedProject.assignedTo?.fullName) return selectedProject.assignedTo.fullName;
                            if (selectedProject.assignedTo?.name) return selectedProject.assignedTo.name;
                            if (selectedProject.assignedManager?.fullName) return selectedProject.assignedManager.fullName;
                            if (selectedProject.assignedManager?.name) return selectedProject.assignedManager.name;
                            if (selectedProject.projectManager?.fullName) return selectedProject.projectManager.fullName;
                            if (selectedProject.projectManager?.name) return selectedProject.projectManager.name;
                            if (selectedProject.manager?.fullName) return selectedProject.manager.fullName;
                            if (selectedProject.manager?.name) return selectedProject.manager.name;

                            // If assignedTo is just an ID string, show that it's assigned but we don't have the name
                            if (selectedProject.assignedTo && typeof selectedProject.assignedTo === 'string') {
                              return "Assigned (Manager details not loaded)";
                            }

                            return "Not assigned";
                          })()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="h-5 w-5 text-gray-400 mt-0.5">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Category</p>
                        <p className="text-sm text-gray-600">{selectedProject.category || "Not specified"}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Basic Project Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 mb-2">Project Details</h4>
                  <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Status: </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        selectedProject.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : selectedProject.status === 'inprogress'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedProject.status === 'inprogress' ? 'In Progress' :
                         selectedProject.status === 'completed' ? 'Completed' :
                         selectedProject.status || 'Active'}
                      </span>
                    </div>
                    {selectedProject.category && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Category: </span>
                        <span className="text-sm text-gray-600">{selectedProject.category}</span>
                      </div>
                    )}
                    {selectedProject.beneficiaries && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Beneficiaries: </span>
                        <span className="text-sm text-gray-600">{selectedProject.beneficiaries}</span>
                      </div>
                    )}
                    {(selectedProject.initialBudget || selectedProject.budget) && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Budget: </span>
                        <span className="text-sm text-gray-600">{formatCurrency(selectedProject.initialBudget || selectedProject.budget)}</span>
                      </div>
                    )}
                    {selectedProject.createdAt && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Created: </span>
                        <span className="text-sm text-gray-600">{formatDate(selectedProject.createdAt)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Objectives */}
                {projectDetails?.objectives && projectDetails.objectives.length > 0 && (
                  <div>
                    <h4 className="text-md font-semibold text-gray-900 mb-2">Objectives</h4>
                    <ul className="list-disc list-inside space-y-1">
                      {projectDetails.objectives.map((obj: any, index: number) => (
                        <li key={index} className="text-sm text-gray-600">{obj.description || obj.objective}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Goals */}
                {projectDetails?.goals && projectDetails.goals.length > 0 && (
                  <div>
                    <h4 className="text-md font-semibold text-gray-900 mb-2">Goals</h4>
                    <ul className="list-disc list-inside space-y-1">
                      {projectDetails.goals.map((goal: any, index: number) => (
                        <li key={index} className="text-sm text-gray-600">{goal.description || goal.goal}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Activities */}
                {projectDetails?.activities && projectDetails.activities.length > 0 && (
                  <div>
                    <h4 className="text-md font-semibold text-gray-900 mb-2">Activities ({projectDetails.activities.length})</h4>
                    <div className="max-h-40 overflow-y-auto">
                      <ul className="list-disc list-inside space-y-1">
                        {projectDetails.activities.map((activity: any, index: number) => (
                          <li key={index} className="text-sm text-gray-600">{activity.title || activity.name}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
