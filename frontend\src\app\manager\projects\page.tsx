'use client';
import { useState, useEffect } from 'react';
import {
  ClipboardDocumentListIcon,
  CalendarIcon,
  UserGroupIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentArrowDownIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/solid';
import { api } from '@/services/api';
import apiUtil from '@/utils/api';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

interface Project {
  id: string;
  _id?: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: 'ongoing' | 'completed' | 'delayed';
  progress: number;
  teamSize: number;
  budget: {
    total: number;
    used: number;
  };
  objectives?: Array<{_id: string; description: string; priority?: string; status?: string} | string>;
  plan?: string;
  goals?: Array<{_id: string; description: string; priority?: string; status?: string} | string>;
}

interface ActivityReport {
  _id: string;
  activity: {
    _id: string;
    title: string;
    location?: string;
  };
  submittedBy: {
    fullName: string;
    email: string;
  };
  content: Array<{
    fieldName: string;
    entry: string;
    required: boolean;
  }>;
  amountSpent: number;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  approvedAt?: string;
  approvedBy?: {
    fullName: string;
  };
}

export default function ProjectsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [projectPlan, setProjectPlan] = useState<any>(null);
  const [loadingPlan, setLoadingPlan] = useState(false);
  const [reportProject, setReportProject] = useState<Project | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [activityReports, setActivityReports] = useState<ActivityReport[]>([]);
  const [loadingReports, setLoadingReports] = useState(false);
  const [sendingEmail, setSendingEmail] = useState(false);

  useEffect(() => {
    async function fetchProjects() {
      try {
        const res = await api.getProjects();
        // Map backend data to Project interface
        setProjects(res.data.map((p: any) => ({
          id: p._id || p.id,
          _id: p._id,
          name: p.title || p.name, // Use title from new model, fallback to name
          description: p.description || '',
          startDate: p.startDate || '',
          endDate: p.endDate || '',
          status: p.status || 'ongoing',
          progress: p.progress || 0,
          teamSize: p.teamSize || 0,
          budget: {
            total: p.initialBudget || p.budget?.total || 0,
            used: p.budget?.used || 0,
          },
          objectives: p.objectives || [],
          plan: p.plan || '',
          goals: p.goals || [],
        })));
      } catch (err: any) {
        console.error('Failed to fetch projects:', err);

        // Set empty state and show user-friendly error
        setProjects([]);

        // You could add a toast notification here if available
        // toast.error('Failed to load projects. Please check your connection and try again.');
      }
    }
    fetchProjects();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'delayed':
        return 'text-red-600';
      default:
        return 'text-sky-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
      case 'delayed':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />;
      default:
        return <ClockIcon className="w-5 h-5 text-sky-600" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'MKW'
    }).format(amount);
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Fetch activity reports for a specific project
  const fetchActivityReports = async (projectId: string) => {
    setLoadingReports(true);
    try {
      const response = await api.get(`/api/v1/manager/reports`);
      if (response.data && response.data.status === 'success') {
        // Filter reports for the specific project
        const projectReports = response.data.data.filter((report: ActivityReport) =>
          report.activity && report.activity._id &&
          // We need to check if the activity belongs to this project
          // For now, we'll get all reports and let the backend handle filtering
          true
        );
        setActivityReports(projectReports);
      }
    } catch (error) {
      console.error('Failed to fetch activity reports:', error);
      setActivityReports([]);
    } finally {
      setLoadingReports(false);
    }
  };

  // Generate PDF report
  const generatePDFReport = async (project: Project, reports: ActivityReport[]) => {
    const doc = new jsPDF();
    let yPosition = 20;

    // Title
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text(`Project Report: ${project.name}`, 14, yPosition);
    yPosition += 20;

    // Project Details Section
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Project Details', 14, yPosition);
    yPosition += 10;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`Description: ${project.description}`, 14, yPosition);
    yPosition += 8;
    doc.text(`Status: ${project.status}`, 14, yPosition);
    yPosition += 8;
    doc.text(`Progress: ${project.progress}%`, 14, yPosition);
    yPosition += 8;
    doc.text(`Budget: MWK ${project.budget.used.toLocaleString()} / MWK ${project.budget.total.toLocaleString()}`, 14, yPosition);
    yPosition += 8;
    doc.text(`Team Size: ${project.teamSize}`, 14, yPosition);
    yPosition += 15;

    // Activity Reports Section
    if (reports.length > 0) {
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Activity Reports', 14, yPosition);
      yPosition += 10;

      reports.forEach((report, index) => {
        if (yPosition > 250) {
          doc.addPage();
          yPosition = 20;
        }

        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text(`${index + 1}. ${report.activity?.title || 'Unknown Activity'}`, 14, yPosition);
        yPosition += 8;

        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        doc.text(`Submitted by: ${report.submittedBy?.fullName || 'Unknown'}`, 20, yPosition);
        yPosition += 6;
        doc.text(`Status: ${report.status || 'Unknown'}`, 20, yPosition);
        yPosition += 6;
        doc.text(`Amount Spent: MWK ${(report.amountSpent || 0).toLocaleString()}`, 20, yPosition);
        yPosition += 6;
        doc.text(`Location: ${report.activity?.location || 'N/A'}`, 20, yPosition);
        yPosition += 6;
        doc.text(`Date: ${report.createdAt ? new Date(report.createdAt).toLocaleDateString() : 'N/A'}`, 20, yPosition);
        yPosition += 8;

        // Report content
        if (report.content && report.content.length > 0) {
          doc.text('Report Details:', 20, yPosition);
          yPosition += 6;
          report.content.forEach((item) => {
            if (yPosition > 270) {
              doc.addPage();
              yPosition = 20;
            }
            doc.text(`${item.fieldName || 'Field'}: ${item.entry || 'No data'}`, 25, yPosition);
            yPosition += 6;
          });
        }
        yPosition += 10;
      });
    }

    // Footer
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 285);
      doc.text(`Page ${i} of ${pageCount}`, 180, 285);
    }

    return doc;
  };

  // Download PDF
  const downloadPDF = async () => {
    if (!reportProject) return;

    const doc = await generatePDFReport(reportProject, activityReports);
    doc.save(`${reportProject.name}_Project_Report_${new Date().toISOString().split('T')[0]}.pdf`);
  };

  // Test simple endpoint without multer


  // Send to Senior Manager
  const sendToSeniorManager = async () => {
    if (!reportProject) {
      alert('No project selected');
      return;
    }

    setSendingEmail(true);
    try {
      console.log('🔄 Generating PDF report...');
      const doc = await generatePDFReport(reportProject, activityReports);
      const pdfBlob = doc.output('blob');

      console.log('📄 PDF generated, size:', pdfBlob.size);
      console.log('📄 PDF MIME type:', pdfBlob.type);

      // Create a proper PDF blob with correct MIME type
      const pdfFile = new File([pdfBlob], `${reportProject.name}_Project_Report.pdf`, {
        type: 'application/pdf'
      });

      console.log('📄 PDF file created, type:', pdfFile.type);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('projectId', reportProject.id);
      formData.append('projectName', reportProject.name);
      formData.append('report', pdfFile);

      console.log('📋 FormData created with:');
      console.log('- Project ID:', reportProject.id);
      console.log('- Project Name:', reportProject.name);
      console.log('- File size:', pdfFile.size);
      console.log('- File type:', pdfFile.type);

      // Test basic connectivity first
      console.log('🔍 Testing API connectivity...');
      try {
        const testResponse = await apiUtil.get('/api/v1/manager/dashboard/stats');
        console.log('✅ API connectivity test passed');
      } catch (testError: any) {
        console.log('❌ API connectivity test failed:', testError?.message || testError);
        // Don't throw here, continue with the actual request
      }

      // Send email with attachment
      console.log('📧 Sending request to backend...');
      const response = await apiUtil.post('/api/v1/manager/send-report-to-senior', formData);

      console.log('✅ Response received:', response);
      alert('Report sent to Senior Manager successfully!');
    } catch (error: any) {
      console.error('❌ Failed to send report:', error);

      // Handle different types of errors
      let errorMessage = 'Failed to send report. Please try again.';

      try {
        if (error?.response) {
          // Axios error with response
          console.error('Error response exists');
          console.error('Response object:', JSON.stringify(error.response, null, 2));

          const status = error.response?.status;
          const data = error.response?.data;

          if (status) {
            console.error('Error status:', status);
          }
          if (data) {
            console.error('Error data:', data);
          }

          errorMessage = data?.message || `Server error: ${status || 'Unknown'}`;
        } else if (error?.request) {
          // Network error
          console.error('Network error - no response received');
          console.error('Request object:', error.request);
          errorMessage = 'Network error. Please check your connection.';
        } else if (error?.message) {
          // Other error
          console.error('Error message:', error.message);
          errorMessage = error.message;
        } else {
          // Unknown error
          console.error('Unknown error type:', typeof error);
          console.error('Error object:', error);
        }
      } catch (loggingError) {
        console.error('Error while processing error:', loggingError);
        errorMessage = 'An unexpected error occurred.';
      }

      alert(`Error: ${errorMessage}`);
    } finally {
      setSendingEmail(false);
    }
  };



  // Handle opening report modal
  const handleGenerateReport = async (project: Project) => {
    setReportProject(project);
    await fetchActivityReports(project.id);
  };

  // Add function to fetch project plan
  const fetchProjectPlan = async (projectId: string) => {
    console.log('🔍 Fetching project plan for project ID:', projectId);
    setLoadingPlan(true);
    try {
      const response = await apiUtil.get(`/api/v1/project-plan/plan/${projectId}`);
      console.log('📋 Project plan response:', response);
      console.log('📋 Project plan data:', response.data);
      console.log('📋 Expected outcome:', response.data?.expectedOutcome);
      console.log('📋 Objectives:', response.data?.objectives);
      console.log('📋 Goals:', response.data?.goals);
      console.log('📋 Inputs:', response.data?.inputs);
      console.log('📋 Outputs:', response.data?.outputs);
      setProjectPlan(response.data);
    } catch (error) {
      console.error("❌ Error fetching project plan:", error);
      console.log("❌ Error details:", error.response?.data || error.message);
      setProjectPlan(null);
    } finally {
      setLoadingPlan(false);
    }
  };

  // Update the project selection handler
  const handleProjectSelect = (project: Project) => {
    console.log('🎯 Selected project:', project);
    console.log('🎯 Project ID:', project._id || project.id);
    console.log('🎯 Project objectives:', project.objectives);
    console.log('🎯 Project goals:', project.goals);
    console.log('🎯 Project plan:', project.plan);
    console.log('🎯 Project description:', project.description);
    setSelectedProject(project);
    setProjectPlan(null);
    if (project._id || project.id) {
      fetchProjectPlan(project._id || project.id);
    }
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-6">Projects Management</h1>
        
        {/* Search and Filter Bar */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 border rounded-md focus:ring-2 focus:ring-sky-500"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="p-2 border rounded-md focus:ring-2 focus:ring-sky-500"
          >
            <option value="all">All Status</option>
            <option value="ongoing">Ongoing</option>
            <option value="completed">Completed</option>
            <option value="delayed">Delayed</option>
          </select>
        </div>

        {/* Projects Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold">Projects Overview</h2>
          </div>
          
          {filteredProjects.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Found</h3>
              <p className="text-gray-600">No projects match your current filters.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Project
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Team
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Budget
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timeline
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredProjects.map((project) => (
                    <tr 
                      key={project.id} 
                      className="hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleProjectSelect(project)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{project.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{project.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(project.status)}
                          <span className={`ml-2 text-sm ${getStatusColor(project.status)}`}>
                            {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${project.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">{project.progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <svg className="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                          {project.teamSize} members
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">MWK {project.budget.used.toLocaleString()}</div>
                          <div className="text-gray-500">of MWK {project.budget.total.toLocaleString()}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div>{project.startDate}</div>
                          <div className="text-gray-500">to {project.endDate}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              window.location.href = `/manager/activities?projectId=${project.id}`;
                            }}
                            className="text-green-600 hover:text-green-900"
                          >
                            Manage
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleGenerateReport(project);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Report
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Comprehensive Project Details Modal */}
      {selectedProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-6xl w-full max-h-[95vh] overflow-y-auto mx-4">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900">{selectedProject.name}</h2>
                  <div className="flex items-center mt-2">
                    {getStatusIcon(selectedProject.status)}
                    <span className={`ml-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedProject.status)}`}>
                      {selectedProject.status.charAt(0).toUpperCase() + selectedProject.status.slice(1)}
                    </span>
                  </div>
                </div>
                <button
                  className="text-gray-400 hover:text-gray-600 p-2"
                  onClick={() => setSelectedProject(null)}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-8 py-6">
              {/* Project Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                <div className="lg:col-span-2">
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Project Description</h3>
                    <p className="text-gray-700 leading-relaxed">{selectedProject.description}</p>
                  </div>

                  {/* Progress Section */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Progress Overview</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-2">
                        <span>Overall Progress</span>
                        <span>{selectedProject.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div 
                          className="bg-green-600 h-3 rounded-full transition-all duration-300" 
                          style={{ width: `${selectedProject.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Key Metrics */}
                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Metrics</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Team Size</span>
                        <span className="font-medium">{selectedProject.teamSize} members</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Start Date</span>
                        <span className="font-medium">{selectedProject.startDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">End Date</span>
                        <span className="font-medium">{selectedProject.endDate}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Budget Overview</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Budget</span>
                        <span className="font-medium">MWK {selectedProject.budget.total.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Used</span>
                        <span className="font-medium text-red-600">MWK {selectedProject.budget.used.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Remaining</span>
                        <span className="font-medium text-green-600">MWK {(selectedProject.budget.total - selectedProject.budget.used).toLocaleString()}</span>
                      </div>
                      <div className="pt-2 border-t">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-red-500 h-2 rounded-full" 
                            style={{ width: `${(selectedProject.budget.used / selectedProject.budget.total) * 100}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {((selectedProject.budget.used / selectedProject.budget.total) * 100).toFixed(1)}% utilized
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Project Plan Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Plan</h3>
                <div className="bg-gray-50 rounded-lg p-6 space-y-6">
                    {/* Show project objectives and goals from project data */}
                    {selectedProject?.objectives && selectedProject.objectives.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Project Objectives</h4>
                        <div className="space-y-3">
                          {selectedProject.objectives.map((obj: any, idx: number) => (
                            <div key={idx} className="bg-white rounded-lg p-4 border border-gray-200">
                              <div className="flex items-start justify-between">
                                <p className="text-gray-800 flex-1">
                                  {typeof obj === 'string' ? obj : obj.description || obj.title}
                                </p>
                                {obj.priority && (
                                  <span className={`ml-3 px-2 py-1 text-xs rounded-full font-medium ${
                                    obj.priority === 'high' ? 'bg-red-100 text-red-800' :
                                    obj.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                  }`}>
                                    {obj.priority}
                                  </span>
                                )}
                              </div>
                              {obj.status && (
                                <div className="mt-2">
                                  <span className={`inline-flex px-2 py-1 text-xs rounded-full font-medium ${
                                    obj.status === 'completed' ? 'bg-green-100 text-green-800' :
                                    obj.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {obj.status}
                                  </span>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Show project goals */}
                    {selectedProject?.goals && selectedProject.goals.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Project Goals</h4>
                        <div className="space-y-3">
                          {selectedProject.goals.map((goal: any, idx: number) => (
                            <div key={idx} className="bg-white rounded-lg p-4 border border-gray-200">
                              <div className="flex items-start justify-between">
                                <p className="text-gray-800 flex-1">
                                  {typeof goal === 'string' ? goal : goal.description || goal.title}
                                </p>
                                {goal.priority && (
                                  <span className={`ml-3 px-2 py-1 text-xs rounded-full font-medium ${
                                    goal.priority === 'high' ? 'bg-red-100 text-red-800' :
                                    goal.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                  }`}>
                                    {goal.priority}
                                  </span>
                                )}
                              </div>
                              {goal.target && (
                                <p className="text-sm text-gray-600 mt-1">Target: {goal.target}</p>
                              )}
                              {goal.status && (
                                <div className="mt-2">
                                  <span className={`inline-flex px-2 py-1 text-xs rounded-full font-medium ${
                                    goal.status === 'completed' ? 'bg-green-100 text-green-800' :
                                    goal.status === 'inprogress' ? 'bg-blue-100 text-blue-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {goal.status}
                                  </span>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Project Plan Description */}
                    {selectedProject?.plan && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Project Plan Description</h4>
                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                          <p className="text-gray-700">{selectedProject.plan}</p>
                        </div>
                      </div>
                    )}

                    {/* Project Information Summary */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Project Overview</h4>
                      <div className="bg-white rounded-lg p-4 border border-gray-200 space-y-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <span className="font-medium text-gray-900">Description:</span>
                            <p className="text-gray-700 mt-1">{selectedProject?.description}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-900">Status:</span>
                            <p className="text-gray-700 mt-1 capitalize">{selectedProject?.status}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-900">Progress:</span>
                            <div className="mt-1">
                              <div className="flex items-center">
                                <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                  <div
                                    className="bg-blue-600 h-2 rounded-full"
                                    style={{ width: `${selectedProject?.progress || 0}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium text-gray-700">{selectedProject?.progress || 0}%</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-900">Team Size:</span>
                            <p className="text-gray-700 mt-1">{selectedProject?.teamSize} members</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Budget Information */}
                    {selectedProject?.budget && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Budget Information</h4>
                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <span className="font-medium text-gray-900">Total Budget:</span>
                              <p className="text-gray-700 mt-1">MWK {selectedProject.budget.total?.toLocaleString() || 'N/A'}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-900">Used:</span>
                              <p className="text-gray-700 mt-1">MWK {selectedProject.budget.used?.toLocaleString() || 'N/A'}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-900">Remaining:</span>
                              <p className="text-gray-700 mt-1">
                                MWK {((selectedProject.budget.total || 0) - (selectedProject.budget.used || 0)).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          {selectedProject.budget.total > 0 && (
                            <div className="mt-3">
                              <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                                <span>Budget Utilization</span>
                                <span>{Math.round(((selectedProject.budget.used || 0) / selectedProject.budget.total) * 100)}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full"
                                  style={{ width: `${Math.min(((selectedProject.budget.used || 0) / selectedProject.budget.total) * 100, 100)}%` }}
                                ></div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Show message if no detailed plan data */}
                    {(!selectedProject?.objectives || selectedProject.objectives.length === 0) &&
                     (!selectedProject?.goals || selectedProject.goals.length === 0) &&
                     !selectedProject?.plan && (
                      <div className="text-center py-8">
                        <div className="text-gray-400 mb-4">
                          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h4 className="text-lg font-medium text-gray-900 mb-2">Limited Project Plan Data</h4>
                        <p className="text-gray-500 mb-4">This project has basic information but lacks detailed planning data such as objectives, goals, and comprehensive plans.</p>

                        {/* Debug button to create sample project plan */}
                        <button
                          onClick={async () => {
                            if (!selectedProject) return;
                            try {
                              console.log('🔧 Creating sample project plan for project:', selectedProject.id);
                              const samplePlan = {
                                project: selectedProject._id || selectedProject.id,
                                expectedOutcome: "Sample expected outcome for testing the project plan display functionality",
                                objectives: [],
                                goals: [],
                                inputs: [
                                  { name: "Sample Input 1", quantity: "10 units" },
                                  { name: "Sample Input 2", quantity: "5 pieces" }
                                ],
                                outputs: [
                                  { name: "Sample Output 1", value: "Expected result 1" },
                                  { name: "Sample Output 2", value: "Expected result 2" }
                                ]
                              };

                              const response = await apiUtil.post('/api/v1/project-plan', samplePlan);
                              console.log('✅ Sample project plan created:', response);
                              alert('Sample project plan created! Close and reopen the modal to see it.');
                            } catch (error) {
                              console.error('❌ Error creating sample project plan:', error);
                              alert('Error creating sample project plan. Check console for details.');
                            }
                          }}
                          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                        >
                          Create Sample Plan (Debug)
                        </button>
                      </div>
                    )}
                  </div>
              </div>



              {/* Action Buttons */}
              <div className="flex justify-end space-x-4 pt-6 border-t">
                <button
                  onClick={() => setSelectedProject(null)}
                  className="px-6 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setSelectedProject(null);
                    window.location.href = `/manager/activities?projectId=${selectedProject.id}`;
                  }}
                  className="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 transition-colors"
                >
                  Manage Activities
                </button>
                <button
                  onClick={() => {
                    handleGenerateReport(selectedProject);
                  }}
                  className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Generate Report
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Project Report Modal */}
      {reportProject && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto p-8 relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
              onClick={() => setReportProject(null)}
            >
              <span className="text-2xl">&times;</span>
            </button>
            <h2 className="text-2xl font-bold mb-4">{reportProject.name} - Project Report</h2>
            <div className="mb-4">
              <div className="mb-2"><span className="font-medium">Progress:</span> {reportProject.progress}%</div>
              <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div className="bg-sky-600 h-3 rounded-full" style={{ width: `${reportProject.progress}%` }}></div>
              </div>
              <div className="mb-2"><span className="font-medium">Budget Used:</span> MWK {reportProject.budget.used.toLocaleString()} / MWK {reportProject.budget.total.toLocaleString()}</div>
              {/* Pie chart for budget usage */}
              <div className="flex justify-center my-4">
                <svg width="120" height="120" viewBox="0 0 32 32">
                  <circle r="16" cx="16" cy="16" fill="#e5e7eb" />
                  <circle
                    r="16"
                    cx="16"
                    cy="16"
                    fill="transparent"
                    stroke="#0ea5e9"
                    strokeWidth="4"
                    strokeDasharray={`${(reportProject.budget.used / reportProject.budget.total) * 100} 100`}
                    strokeDashoffset="25"
                  />
                  <text x="16" y="18" textAnchor="middle" fontSize="8" fill="#0ea5e9">
                    {Math.round((reportProject.budget.used / reportProject.budget.total) * 100)}%
                  </text>
                </svg>
              </div>
              <div className="mb-2"><span className="font-medium">Team Size:</span> {reportProject.teamSize}</div>
              <div className="mb-2"><span className="font-medium">Status:</span> {reportProject.status}</div>
            </div>
            <div className="mb-4">
              <h3 className="font-semibold mb-1">Objectives</h3>
              <ul className="list-disc ml-6">
                {reportProject.objectives?.map((obj, idx) => (
                  <li key={idx}>
                    {typeof obj === 'string' ? obj : obj.description}
                    {typeof obj === 'object' && obj.priority && (
                      <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                        obj.priority === 'high' ? 'bg-red-100 text-red-800' :
                        obj.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {obj.priority}
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </div>
            <div className="mb-4">
              <h3 className="font-semibold mb-1">Goals</h3>
              <ul className="list-disc ml-6">
                {reportProject.goals?.map((goal, idx) => (
                  <li key={idx}>
                    {typeof goal === 'string' ? goal : goal.description}
                    {typeof goal === 'object' && goal.priority && (
                      <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                        goal.priority === 'high' ? 'bg-red-100 text-red-800' :
                        goal.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {goal.priority}
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </div>

            {/* Activity Reports Section */}
            <div className="mb-6">
              <h3 className="font-semibold mb-3">Activity Reports</h3>
              {loadingReports ? (
                <div className="text-center py-4">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600"></div>
                  <p className="mt-2 text-gray-600">Loading activity reports...</p>
                </div>
              ) : activityReports.length > 0 ? (
                <div className="space-y-4 max-h-60 overflow-y-auto">
                  {activityReports.map((report) => (
                    <div key={report._id} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900">{report.activity?.title || 'Unknown Activity'}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full font-semibold ${
                          report.status === 'approved' ? 'bg-green-100 text-green-800' :
                          report.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {report.status}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <div><span className="font-medium">Submitted by:</span> {report.submittedBy?.fullName || 'Unknown'}</div>
                        <div><span className="font-medium">Amount Spent:</span> MWK {(report.amountSpent || 0).toLocaleString()}</div>
                        <div><span className="font-medium">Location:</span> {report.activity?.location || 'N/A'}</div>
                        <div><span className="font-medium">Date:</span> {report.createdAt ? new Date(report.createdAt).toLocaleDateString() : 'N/A'}</div>
                        {report.status === 'approved' && report.approvedBy && (
                          <div><span className="font-medium">Approved by:</span> {report.approvedBy.fullName}</div>
                        )}
                      </div>
                      {report.content && report.content.length > 0 && (
                        <div className="mt-3">
                          <p className="text-sm font-medium text-gray-700 mb-1">Report Details:</p>
                          <div className="text-sm text-gray-600 space-y-1">
                            {report.content.slice(0, 2).map((item, idx) => (
                              <div key={idx}><span className="font-medium">{item.fieldName}:</span> {item.entry}</div>
                            ))}
                            {report.content.length > 2 && (
                              <div className="text-xs text-gray-500">... and {report.content.length - 2} more fields</div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  No activity reports found for this project.
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 pt-4 border-t">
              <button
                onClick={downloadPDF}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <DocumentArrowDownIcon className="w-5 h-5 mr-2" />
                Generate PDF
              </button>
              <button
                onClick={sendToSeniorManager}
                disabled={sendingEmail}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PaperAirplaneIcon className="w-5 h-5 mr-2" />
                {sendingEmail ? 'Sending...' : 'Send to Senior Manager'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 





