import axiosInstance from '@/utils/api';
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000/api/v1';

interface SettingsPayload {
  emailNotifications?: boolean;
  projectUpdates?: boolean;
  activityReminders?: boolean;
  reportNotifications?: boolean;
  systemAlerts?: boolean;
  theme?: string;
  language?: string;
  timezone?: string;
}

interface ChangePasswordPayload {
  currentPassword: string;
  newPassword: string;
}

export interface DashboardStats {
  totalBudget?(totalBudget: any): number;
  totalDisbursed?(totalDisbursed: any): number;
  activeProjects?: number;
  pendingReports?: number;
  fieldOfficers?: number;
  // Field Officer specific stats
  totalAssignedActivities?: number;
  activeActivities?: number;
  completedActivities?: number;
  teamMembers?: number;
  projectProgress?: number;
  // Legacy fields for backward compatibility
  totalProjects?: number;
  inProgressProjects?: number;
  completedProjects?: number;
  totalTeamMembers?: number;
  recentProjects?: any[];
  recentActivities?: any[];
}

export interface Project {
  id: string;
  name: string;
}

export interface ProjectProgress {
  labels: string[];
  data: number[];
}

export interface BudgetData {
  approved: number;
  used: number;
  remaining: number;
}

export interface ProjectInsight {
  project: string;
  lessons: string;
  recommendations: string;
  date: string;
}

export interface ApiResponse<T> {
  data: T;
  error?: string;
}

async function handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'An error occurred');
  }
  const data = await response.json();
  return { data };
}

export const api = {
  // Generic HTTP methods
  get: async (url: string): Promise<any> => {
    const { data } = await axiosInstance.get(url);
    return { data };
  },

  post: async (url: string, payload: any): Promise<any> => {
    const { data } = await axiosInstance.post(url, payload);
    return { data };
  },

  put: async (url: string, payload: any): Promise<any> => {
    const { data } = await axiosInstance.put(url, payload);
    return { data };
  },

  delete: async (url: string): Promise<any> => {
    const { data } = await axiosInstance.delete(url);
    return { data };
  },

  getSeniorManagerStats: async (): Promise<ApiResponse<DashboardStats>> => {
    const { data } = await axiosInstance.get('/api/v1/senior-manager/dashboard/stats');
    return { data };
  },

  getManagerDashboardStats: async (): Promise<ApiResponse<DashboardStats>> => {
    const { data } = await axiosInstance.get('/api/v1/dashboard/stats/project-manager');
    return { data };
  },

  getFieldOfficerStats: async (): Promise<ApiResponse<DashboardStats>> => {
    const { data } = await axiosInstance.get('/api/v1/dashboard/stats/field-officer');
    return { data };
  },

  getAccountantStats : async (): Promise<ApiResponse<DashboardStats>> => {
    const { data } = await axiosInstance.get('/api/v1/dashboard/stats/accountant');
    return { data };
  },
  
  getProjects: async (): Promise<ApiResponse<Project[]>> => {
    const { data } = await axiosInstance.get('/api/v1/project');
    const projects = Array.isArray(data) ? data : data.projects || [];
    // Map projects to ensure consistent naming
    const mappedProjects = projects.map((p: any) => ({
      ...p,
      name: p.title || p.name, // Ensure name field exists for backward compatibility
    }));
    return { data: mappedProjects };
  },

  // Project Progress
  getProjectProgress: async (projectId?: string): Promise<ApiResponse<ProjectProgress>> => {
    const url = projectId
      ? `/api/v1/project/progress?projectId=${projectId}`
      : '/api/v1/project/progress';
    const { data } = await axiosInstance.get(url);
    return { data };
  },

  getBudgetOverview: async (projectId?: string): Promise<ApiResponse<BudgetData>> => {
    const url = projectId
      ? `/api/v1/project/budget/overview?projectId=${projectId}`
      : '/api/v1/project/budget/overview';
    const { data } = await axiosInstance.get(url);
    return { data };
  },

  // Manager-specific budget overview
  getManagerBudgetOverview: async (): Promise<ApiResponse<BudgetData>> => {
    const { data } = await axiosInstance.get('/api/v1/manager/budget/overview');
    return { data };
  },
  
  approveBudget: async (projectId: string) => {
    return await axiosInstance.put(`/api/v1/project/${projectId}/approve`);
  },

  rejectBudget: async (projectId: string, reason: string) => {
    return await axiosInstance.put(`/project/${projectId}/reject`, { reason });
  },
  
  // Project Insights
  submitProjectInsight: async (data: ProjectInsight): Promise<ApiResponse<void>> => {
    const payload = {
      projectId: data.project,
      lessons: data.lessons,
      recommendations: data.recommendations
    };
    await axiosInstance.post('/api/v1/manager/insights', payload);
    return { data: undefined };
  },

  getProjectInsights: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/manager/insights');
    return { data };
  },

  // Auth
  logout: async (): Promise<ApiResponse<void>> => {
    await axiosInstance.post('/api/v1/auth/logout');
    return { data: undefined };
  },

  // Activity Reports
  submitActivityReport: async (data: {
    activity_id: string;
    description: string;
    attachments?: File[];
    customFields?: {[key: string]: string};
    amountSpent?: number;
    submitted_by?: string;
    report_date?: string;
    due_date?: string;
  }): Promise<ApiResponse<void>> => {
    try {
      // Use the new format that matches the backend controller expectations
      const reportData = {
        activity_id: data.activity_id,
        description: data.description,
        customFields: data.customFields,
        amountSpent: data.amountSpent || 0,
        attachments: data.attachments
      };

      console.log('🔍 Submitting activity report:', reportData);

      const response = await axiosInstance.post('/api/v1/project-report', reportData, {
        headers: { 'Content-Type': 'application/json' }
      });

      console.log('✅ Report submitted successfully:', response.data);
      return { data: undefined };
    } catch (error: any) {
      console.error('❌ Report submission error:', error);
      console.error('❌ Error response:', error?.response?.data);

      // Re-throw with more specific error message
      const errorMessage = error?.response?.data?.message || error?.message || 'Failed to submit report';
      throw new Error(errorMessage);
    }
  },

  // Notifications
  getNotifications: async (): Promise<ApiResponse<any[]>> => {
    try {
      console.log('🔔 Fetching notifications from API...');
      console.log('🔔 API Base URL:', axiosInstance.defaults.baseURL);
      console.log('🔔 Request URL:', '/api/v1/notifications');

      const { data } = await axiosInstance.get('/api/v1/notifications');
      console.log('✅ Notifications response:', data);
      return { data: data.notifications || data.data || [] };
    } catch (error: any) {
      console.error('❌ Error fetching notifications:', error);
      console.error('❌ Error details:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        baseURL: error.config?.baseURL
      });
      throw error;
    }
  },

  getUnreadNotificationCount: async (): Promise<ApiResponse<{ count: number }>> => {
    const { data } = await axiosInstance.get('/api/v1/notifications/unread-count');
    return { data: data.data || { count: 0 } };
  },

  // Mark notification as read
  markNotificationAsRead: async (notificationId: string): Promise<ApiResponse<void>> => {
    const { data } = await axiosInstance.patch(`/api/v1/notifications/${notificationId}/read`);
    return { data: data.data };
  },

  // Mark all notifications as read
  markAllNotificationsAsRead: async (): Promise<ApiResponse<void>> => {
    const { data } = await axiosInstance.patch('/api/v1/notifications/mark-all-read');
    return { data: data.data };
  },

  deleteNotification: async (notificationId: string): Promise<ApiResponse<void>> => {
    const { data } = await axiosInstance.delete(`/api/v1/notifications/${notificationId}`);
    return { data: data.data };
  },

  getProfile: async () => {
    const res = await axiosInstance.get('/api/v1/user/me');
    console.log("👤 Profile response:", res.data);
    return res.data?.user ?? res.data?.data ?? res.data;
  },
  
  updateProfile: async (payload: FormData | Record<string, any>) => {
    const { data } = await axiosInstance.patch('/api/v1/user/me', payload);
    return { data: data.data };
  },

  getUser: (params = {}) => axiosInstance.get("/api/v1/user", { params }),

  // Test Field Officer Routes
  testFieldOfficerRoutes: async (): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.get('/api/v1/field-officer/test');
    return { data };
  },

  // Assigned Activities for Field Officer
  getAssignedActivities: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/field-officer/activities/assigned');
    return { data };
  },



  // Active Projects for Field Officer
  getActiveProjectsForFieldOfficer: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/field-officer/activities/active-projects');
    return { data };
  },

  // Replace the old getActivities and getActivitiesByProject with these:

  getActivities: async (): Promise<any[]> => {
    const { data } = await axiosInstance.get('/api/v1/manager/activities');
    return Array.isArray(data.data) ? data.data : [];
  },

  getActivitiesByProject: async (projectId: string): Promise<any[]> => {
    const { data } = await axiosInstance.get(`/api/v1/project/${projectId}/activities`);
    return Array.isArray(data.data) ? data.data : [];
  },
  
  
  

  getPendingReportsForFieldOfficer: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/field-officer/reports/pending');
    return { data };
  },

  getAllReportsForFieldOfficer: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/field-officer/reports/all');
    return { data };
  },

  // Team for an Activity
  getTeamForActivity: async (activityId: string): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get(`/api/v1/field-officer/activities/${activityId}/team`);
    return { data };
  },

  // Team Management
  createTeam: async (teamData: {
    name: string;
    description?: string;
    projectManager: string;
    fieldOfficers: string[];
  }): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.post('/api/v1/team', teamData);
    return { data };
  },

  getAllTeams: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    projectManager?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.get('/api/v1/team', { params });
    return { data };
  },

  getTeamById: async (teamId: string): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.get(`/api/v1/team/${teamId}`);
    return { data };
  },

  updateTeam: async (teamId: string, teamData: {
    name?: string;
    description?: string;
    projectManager?: string;
    fieldOfficers?: string[];
  }): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.put(`/api/v1/team/${teamId}`, teamData);
    return { data };
  },

  deleteTeam: async (teamId: string): Promise<ApiResponse<void>> => {
    await axiosInstance.delete(`/api/v1/team/${teamId}`);
    return { data: undefined };
  },

  assignTeamToManager: async (teamId: string, assignmentData: {
    projectManagerId: string;
    message?: string;
  }): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.post(`/api/v1/team/${teamId}/assign`, assignmentData);
    return { data };
  },

  // ========== PROJECT MANAGER TEAM MANAGEMENT ==========

  // Get projects for team creation (project managers only)
  getProjectsForTeamCreation: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/team/manager/projects');
    return { data };
  },

  // Get field officers by project location (project managers only)
  getFieldOfficersByProjectLocation: async (projectId: string): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.get(`/api/v1/team/manager/project/${projectId}/field-officers`);
    return { data };
  },

  // Create team for a specific project (project managers only)
  createProjectTeam: async (teamData: {
    name: string;
    description?: string;
    projectId: string;
    fieldOfficers: string[];
  }): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.post('/api/v1/team/manager/create', teamData);
    return { data };
  },

  // Get teams created by project manager
  getProjectManagerTeams: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/team/manager/my-teams');
    return { data };
  },

  // Get field officers from team for a specific project (for activity assignment)
  getTeamFieldOfficersForProject: async (projectId: string): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.get(`/api/v1/team/manager/project/${projectId}/team-officers`);
    return { data };
  },

// In your api.ts
createActivity: async (data: any) => {
  const res = await axiosInstance.post('/api/v1/manager/activities', data);
  return res.data;
},

updateActivity: async (id: string, data: any) => {
  const res = await axiosInstance.put(`/api/v1/project/activity/${id}`, data);
  return res.data;
},



  uploadAvatar: async (file: File): Promise<ApiResponse<any>> => {
    const formData = new FormData();
    formData.append('avatar', file);
    const { data } = await axiosInstance.patch('/api/v1/user/me/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      withCredentials: true,
    });
    return { data };
  },

  getDistricts: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/district');
    return { data: Array.isArray(data) ? data : data.districts || [] };
  },
              
  getDepartments: async () => {
    const res = await axiosInstance.get('/api/v1/department');
    return res.data;
  },



  // Project Plan
  getProjectPlan: async (projectId: string): Promise<ApiResponse<any>> => {
    const { data } = await axiosInstance.get(`/api/v1/project-plan/plan/${projectId}`, {
      withCredentials: true,
    });
    return { data };
  },

  getProjectManagers: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/user?role=projectManager' );
    return { data: Array.isArray(data) ? data : data.users || [] };
  },

  createProject: async (data: any): Promise<ApiResponse<any>> => {
    const res = await axiosInstance.post('/api/v1/project', data);
    return { data: res.data };
  },
  
  createProjectGoal: async (data: any) => {
    const res = await axiosInstance.post('/api/v1/goal', data);
    return res.data;
  },
  
  createObjective: async (data: any) => {
    const res = await axiosInstance.post('/api/v1/objective', data);
    return res.data;
  },

  createProjectPlan: async (data: any) => {
    const res = await axiosInstance.post('/api/v1/project/plan', data);
    return res.data;
  },

  assignProjectToManager: async (data: any) => {
    const res = await axiosInstance.post('/api/v1/project/assign', data);
    return res.data;
  },

  getProjectProgress: async (projectId?: string) => {
    const url = projectId ? `/api/v1/project/progress?projectId=${projectId}` : '/api/v1/project/progress';
    const res = await axiosInstance.get(url);
    return res.data;
  },
  


  getAccountants: async (): Promise<ApiResponse<any[]>> => {
    const { data } = await axiosInstance.get('/api/v1/user', { params: { role: 'accountant' } });
    return { data: Array.isArray(data) ? data : data.users || [] };
  },

  getUserSettings: async () => {
    const { data } = await axiosInstance.get('/api/v1/user/settings');
    return { data: data.data };
  },

  updateUserSettings: async (payload: SettingsPayload) => {
    const { data } = await axiosInstance.put('/api/v1/user/settings', payload);
    return { data: data.data };
  },

  changePassword: async (payload: ChangePasswordPayload) => {
    const { data } = await axiosInstance.post('/api/v1/user/change-password', payload);
    return { data: data.data };
  },



  getRecentActivities: async () => {
    const res = await axiosInstance.get('/api/v1/recentActivities/recent')
    return res.data;
  },
};

