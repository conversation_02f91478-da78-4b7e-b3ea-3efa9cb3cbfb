import Link from "next/link";
import { HomeIcon, ClipboardIcon, CogIcon } from "@heroicons/react/24/outline";
import React, { useState, useEffect } from "react";
import CollapsibleSidebar from "@/components/CollapsibleSidebar";

const navLinks = [
  { name: "Home", icon: HomeIcon, href: "/accountant" },
  { name: "Budgets", icon: ClipboardIcon, href: "/accountant/budgets" },
  { name: "Setting<PERSON>", icon: CogIcon, href: "/accountant/settings" },
];

export default function Sidebar({
  activeItem,
  setActiveItem,
  userName,
  children
}: {
  activeItem: string,
  setActiveItem: (item: string) => void,
  userName?: string,
  children?: React.ReactNode
}) {
  const [displayName, setDisplayName] = useState(userName || 'Accountant');

  useEffect(() => {
    // Get user name from localStorage or API if not provided
    if (!userName) {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          setDisplayName(user.fullName || user.name || 'Accountant');
        } catch (error) {
          // Keep default name
        }
      }
    } else {
      setDisplayName(userName);
    }
  }, [userName]);

  return (
    <CollapsibleSidebar
      navLinks={navLinks}
      activeItem={activeItem}
      userName={displayName}
      userRole="Accountant"
    >
      {children}
    </CollapsibleSidebar>
  );
}