'use client';
import { useState, useEffect } from 'react';
import {
  HomeIcon,
  CogIcon,
  UserGroupIcon,
  DocumentTextIcon,
  BanknotesIcon,
  BellIcon,
  Bars3Icon,
  XMarkIcon,
  ClipboardDocumentListIcon,
  UserCircleIcon,
  CalendarDaysIcon,
  PlusIcon,
  ArrowRightOnRectangleIcon,
  CheckIcon,
  BuildingOfficeIcon,
  ChevronRightIcon,
  PowerIcon,
} from '@heroicons/react/24/solid';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import NotificationBell from '@/components/NotificationBell';
import { api } from '@/services/api';
import { toast } from 'react-hot-toast';
import { DashboardCardWithModal } from '@/components/HoverModal';

// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// Dynamically import Chart.js components to avoid SSR issues
const Chart = dynamic(() => import('react-chartjs-2').then(mod => mod.Line), { ssr: false });
const BarChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Bar), { ssr: false });
const DoughnutChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Doughnut), { ssr: false });
const PieChart = dynamic(() => import('react-chartjs-2').then(mod => mod.Pie), { ssr: false });

export default function DashboardPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('home');
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [selectedProject, setSelectedProject] = useState('');
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  
  const [stats, setStats] = useState<any>({
    totalActivities: 0,
    inProgressActivities: 0,
    completedActivities: 0,
    teamMembers: 0,
    assignedProjects: 0,
    recentActivities: []
  });
  
  const [projects, setProjects] = useState<any[]>([]);
  const [projectProgress, setProjectProgress] = useState<any>({
    labels: [],
    data: []
  });
  const [budgetData, setBudgetData] = useState<any>({
    used: 0,
    remaining: 0
  });
  const [selectedProjectForProgress, setSelectedProjectForProgress] = useState('');
  const [projectActivities, setProjectActivities] = useState<any[]>([]);
  const [activityKPIs, setActivityKPIs] = useState<any[]>([]);

  const userName = 'Manager'; 

  useEffect(() => {
  const fetchDashboardData = async () => {
    setIsLoading(true);
    try {
      // Import the API utility
      const apiUtil = (await import('@/utils/api')).default;

      // Fetch manager dashboard stats
      const statsRes = await apiUtil.get('/api/v1/manager/dashboard/stats');

      // Handle different response formats
      let statsData: any;
      if (statsRes.data && statsRes.data.status === 'success') {
        statsData = statsRes.data.data;
      } else if (statsRes.data) {
        // If response has data but no status, use it directly
        statsData = statsRes.data;
      } else {
        statsData = {
          totalActivities: 0,
          inProgressActivities: 0,
          completedActivities: 0,
          teamMembers: 0,
          assignedProjects: 0,
          recentActivities: []
        };
      }

      // Ensure we have the right data structure
      const finalStats = {
        totalActivities: statsData.totalActivities || 0,
        inProgressActivities: statsData.inProgressActivities || 0,
        completedActivities: statsData.completedActivities || 0,
        teamMembers: statsData.teamMembers || 0,
        assignedProjects: statsData.assignedProjects || 0,
        recentActivities: statsData.recentActivities || []
      };

      setStats(finalStats);

      // Set assigned projects from dashboard stats
      if (statsData.projects && Array.isArray(statsData.projects)) {
        setProjects(statsData.projects);
      }



      // Fetch budget overview
      const budgetRes = await apiUtil.get('/api/v1/manager/budget/overview');
      if (budgetRes.data && budgetRes.data.status === 'success') {
        setBudgetData(budgetRes.data.data);
      }



    } catch (error) {
      console.error("❌ Error fetching dashboard data:", error);

      // Set empty state instead of fallback data
      setStats({
        totalActivities: 0,
        inProgressActivities: 0,
        completedActivities: 0,
        teamMembers: 0,
        assignedProjects: 0,
        recentActivities: []
      });

      toast.error("Failed to load dashboard data. Please check your connection and try again.");
    } finally {
      setIsLoading(false);
    }
  };

  fetchDashboardData();
}, []);

// Fetch activities when project is selected
useEffect(() => {
  if (selectedProjectForProgress) {
    fetchProjectActivities(selectedProjectForProgress);
  }
}, [selectedProjectForProgress]);




  const handleTabClick = (tab: string) => {
    switch (tab) {
      case 'reports':
        router.push('/manager/reports');
        break;
      case 'budget':
        router.push('/manager/budget');
        break;
      case 'team':
        router.push('/manager/teams');
        break;
      case 'projects':
        router.push('/manager/projects');
        break;
    
      case 'm&e':
        router.push('/manager/m&e');
        break;
      
      case 'activities':
        router.push('/manager/activities');
        break;
      default:
        setActiveTab(tab);
    }
  };



  const fetchProjectActivities = async (projectId: string) => {
    if (!projectId) {
      setProjectActivities([]);
      setActivityKPIs([]);
      return;
    }

    try {
      const apiUtil = (await import('@/utils/api')).default;

      // Fetch activities for the selected project
      const activitiesRes = await apiUtil.get(`/api/v1/manager/activities?projectId=${projectId}`);
      let activities = [];

      if (activitiesRes.data && activitiesRes.data.status === 'success') {
        activities = activitiesRes.data.data || [];
      } else if (activitiesRes.data && Array.isArray(activitiesRes.data)) {
        activities = activitiesRes.data;
      }

      // Fetch activity reports to calculate progress
      const reportsRes = await api.get('/api/v1/manager/reports');
      const reports = reportsRes.data?.data || [];

      // Calculate progress for each activity based on reports and budget utilization
      const activitiesWithProgress = activities.map((activity: any) => {
        let calculatedProgress = activity.progress || 0;

        // If no progress is set, calculate based on approved reports
        if (calculatedProgress === 0) {
          const activityReports = reports.filter((report: any) =>
            (report.approved === true || report.status === 'approved') &&
            report.activity &&
            (report.activity._id === activity._id || report.activity.id === activity.id)
          );

          if (activityReports.length > 0) {
            // Calculate progress based on budget utilization if budget is available
            const allocatedBudget = activity.budget || activity.allocatedBudget || 0;
            if (allocatedBudget > 0) {
              const spentBudget = activityReports.reduce((sum: number, report: any) =>
                sum + (report.amountSpent || 0), 0
              );
              calculatedProgress = Math.min((spentBudget / allocatedBudget) * 100, 100);
            } else {
              // If no budget, assume some progress based on number of reports
              calculatedProgress = Math.min(activityReports.length * 25, 100); // 25% per report, max 100%
            }
          }
        }

        return {
          ...activity,
          progress: Math.round(calculatedProgress)
        };
      });

      // Debug: Check activity progress values
      console.log('🎯 Dashboard Activities with calculated progress:');
      activitiesWithProgress.forEach((activity, index) => {
        console.log(`${index + 1}. ${activity.title || activity.name}: ${activity.progress}% (original: ${activity.progress || 0})`);
      });

      setProjectActivities(activitiesWithProgress);

      // Generate KPI data from activities with actual outcomes from reports (reuse reports already fetched)
      const kpiData = activities.reduce((acc: any[], activity: any) => {
        if (activity.kpis && activity.kpis.length > 0) {
          // Get approved reports for this activity
          const activityReports = reports.filter((report: any) =>
            (report.approved === true || report.status === 'approved') &&
            report.activity &&
            report.activity._id === activity._id
          );

          // Process each KPI for this activity
          activity.kpis.forEach((kpi: any) => {
            const targetValue = parseFloat(kpi.target) || 0;
            let actualValue = 0;

            // Extract actual value from latest approved report
            if (activityReports.length > 0) {
              const latestReport = activityReports[activityReports.length - 1];

              if (latestReport.content && Array.isArray(latestReport.content)) {
                // Try exact match first (handle underscores and spaces)
                const exactMatch = latestReport.content.find((content: any) => {
                  if (!content.fieldName || content.entry === undefined || content.entry === null) return false;
                  const fieldName = content.fieldName.toLowerCase().trim().replace(/[_\s]+/g, ' ');
                  const kpiName = kpi.name.toLowerCase().trim().replace(/[_\s]+/g, ' ');
                  return fieldName === kpiName;
                });

                if (exactMatch && exactMatch.entry !== undefined && exactMatch.entry !== null && exactMatch.entry !== '') {
                  const numericValue = parseFloat(String(exactMatch.entry));
                  if (!isNaN(numericValue)) {
                    actualValue = numericValue;
                  }
                } else {
                  // Try partial match (handle underscores and spaces)
                  const partialMatch = latestReport.content.find((content: any) => {
                    if (!content.fieldName || content.entry === undefined || content.entry === null) return false;
                    const fieldName = content.fieldName.toLowerCase().trim().replace(/[_\s]+/g, ' ');
                    const kpiName = kpi.name.toLowerCase().trim().replace(/[_\s]+/g, ' ');

                    const kpiWords = kpiName.split(/\s+/).filter(word => word.length > 2);
                    const fieldWords = fieldName.split(/\s+/).filter(word => word.length > 2);

                    return kpiWords.some(kpiWord =>
                      fieldWords.some(fieldWord =>
                        fieldWord.includes(kpiWord) || kpiWord.includes(fieldWord)
                      )
                    );
                  });

                  if (partialMatch && partialMatch.entry !== undefined && partialMatch.entry !== null && partialMatch.entry !== '') {
                    const numericValue = parseFloat(String(partialMatch.entry));
                    if (!isNaN(numericValue)) {
                      actualValue = numericValue;
                    }
                  }
                }
              }
            }

            acc.push({
              name: kpi.name || kpi.indicator || 'Unnamed KPI',
              activityName: activity.title || activity.name,
              targetOutcome: targetValue,
              actualOutcome: actualValue,
              progress: targetValue > 0 ? Math.min((actualValue / targetValue) * 100, 100) : 0,
              status: actualValue >= targetValue ? 'completed' : 'pending'
            });
          });
        }
        return acc;
      }, []);

      setActivityKPIs(kpiData);
    } catch (error) {
      console.error('Failed to fetch project activities:', error);
      setProjectActivities([]);
      setActivityKPIs([]);
    }
  };

  const handleLogout = () => {
    setShowLogoutConfirm(true);
  };

  const confirmLogout = async () => {
    try {
      await api.logout();
      router.push('/login');
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error('Failed to logout');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-sky-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>


      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCardWithModal
          title="Total Activities"
          value={isLoading ? '...' : (stats.totalActivities ?? '0')}
          icon={<ClipboardDocumentListIcon className="w-5 h-5" />}
          description="Activities in assigned projects"
          detailsContent={
            <div className="space-y-2">
              <p><strong>Total Activities:</strong> {stats.totalActivities ?? '0'}</p>
              <p><strong>In Progress:</strong> {stats.inProgressActivities ?? '0'}</p>
              <p><strong>Completed:</strong> {stats.completedActivities ?? '0'}</p>
              <p><strong>Projects:</strong> {stats.assignedProjects ?? '0'}</p>
              <p className="text-xs text-gray-300 mt-2">Click to view all activities</p>
            </div>
          }
          onClick={() => router.push('/manager/activities')}
          color="white"
        />
        <DashboardCardWithModal
          title="In Progress"
          value={isLoading ? '...' : (stats?.inProgressActivities ?? '0')}
          icon={<Bars3Icon className="w-5 h-5" />}
          description="Activities awaiting reports"
          detailsContent={
            <div className="space-y-2">
              <p><strong>In Progress:</strong> {stats?.inProgressActivities ?? '0'}</p>
              <p><strong>Status:</strong> Activities assigned to field officers</p>
              <p><strong>Action:</strong> Awaiting field officer reports</p>
              <p className="text-xs text-gray-300 mt-2">Click to view in-progress activities</p>
            </div>
          }
          onClick={() => router.push('/manager/activities')}
          color="white"
        />
        <DashboardCardWithModal
          title="Completed"
          value={isLoading ? '...' : (stats.completedActivities ?? '0')}
          icon={<CheckIcon className="w-5 h-5" />}
          description="Activities with approved reports"
          detailsContent={
            <div className="space-y-2">
              <p><strong>Completed:</strong> {stats.completedActivities ?? '0'}</p>
              <p><strong>Status:</strong> Reports approved by project manager</p>
              <p><strong>Progress:</strong> {stats.totalActivities > 0 ? Math.round((stats.completedActivities / stats.totalActivities) * 100) : 0}% of total activities</p>
              <p className="text-xs text-gray-300 mt-2">Click to view completed activities</p>
            </div>
          }
          onClick={() => router.push('/manager/reports')}
          color="white"
        />
        <DashboardCardWithModal
          title="Assigned Projects"
          value={isLoading ? '...' : (stats.assignedProjects ?? '0')}
          icon={<BuildingOfficeIcon className="w-5 h-5" />}
          description="Projects under your management"
          detailsContent={
            <div className="space-y-2">
              <p><strong>Assigned Projects:</strong> {stats.assignedProjects ?? '0'}</p>
              <p><strong>Team Members:</strong> {stats.teamMembers ?? '0'}</p>
              <p><strong>Status:</strong> Active project management</p>
              <p><strong>Responsibility:</strong> Activity assignment and report approval</p>
              <p className="text-xs text-gray-300 mt-2">Click to view your projects</p>
            </div>
          }
          onClick={() => router.push('/manager/projects')}
          color="white"
        />
      </div>



      {/* Project Progress Section */}
      <div className="mt-8 bg-white rounded-lg shadow-sm p-6 border border-gray-100">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">Project Progress & KPI Performance</h2>
        </div>

        {/* Project Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Select Project</label>
          <select
            value={selectedProjectForProgress}
            onChange={(e) => setSelectedProjectForProgress(e.target.value)}
            className="w-full max-w-md p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Choose a project to view progress...</option>
            {projects.map(project => (
              <option key={project.id} value={project.id}>
                {project.name || project.title}
              </option>
            ))}
          </select>
        </div>

        {/* Charts Section */}
        {selectedProjectForProgress && (projectActivities.length > 0 || activityKPIs.length > 0) ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Progress Bar Chart */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Activity Progress</h3>
              <div className="h-64">
                <BarChart
                  data={(() => {
                    const chartData = {
                      labels: projectActivities.map(activity => activity.title || activity.name),
                      datasets: [
                        {
                          label: 'Progress (%)',
                          data: projectActivities.map(activity => activity.progress || 0),
                          backgroundColor: 'rgba(59, 130, 246, 0.8)',
                          borderColor: 'rgba(59, 130, 246, 1)',
                          borderWidth: 1,
                        }
                      ]
                    };

                    console.log('📊 Activity Progress Chart Data:');
                    console.log('   Labels:', chartData.labels);
                    console.log('   Progress Values:', chartData.datasets[0].data);
                    console.log('   Individual Activities:');
                    projectActivities.forEach((activity, index) => {
                      console.log(`   ${index + 1}. ${activity.title || activity.name}: ${activity.progress || 0}%`);
                    });

                    return chartData;
                  })()}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false
                      },
                      title: {
                        display: true,
                        text: 'Activity Completion Progress'
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                          callback: function(value) {
                            return value + '%';
                          }
                        }
                      },
                      x: {
                        ticks: {
                          maxRotation: 45,
                          minRotation: 0
                        }
                      }
                    }
                  }}
                />
              </div>
            </div>

            {/* KPI Performance Chart */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">KPI Performance</h3>
              <div className="h-64">
                <BarChart
                  data={{
                    labels: activityKPIs.map(kpi => kpi.name),
                    datasets: [
                      {
                        label: 'Target Outcome',
                        data: activityKPIs.map(kpi => kpi.targetOutcome),
                        backgroundColor: 'rgba(34, 197, 94, 0.8)',
                        borderColor: 'rgba(34, 197, 94, 1)',
                        borderWidth: 1,
                      },
                      {
                        label: 'Actual Outcome',
                        data: activityKPIs.map(kpi => kpi.actualOutcome),
                        backgroundColor: 'rgba(239, 68, 68, 0.8)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1,
                      }
                    ]
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: true,
                        position: 'top'
                      },
                      title: {
                        display: true,
                        text: 'Target vs Actual Outcomes'
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true
                      },
                      x: {
                        ticks: {
                          maxRotation: 45,
                          minRotation: 0
                        }
                      }
                    }
                  }}
                />
              </div>
            </div>
          </div>
        ) : selectedProjectForProgress ? (
          <div className="text-center py-8 text-gray-500">
            <p>Loading project activities...</p>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>Select a project to view progress and KPI performance charts.</p>
          </div>
        )}



      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100 animate-in fade-in-0 zoom-in-95">
            {/* Header */}
            <div className="px-6 pt-6 pb-4">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <PowerIcon className="w-8 h-8 text-red-600" />
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Sign Out</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Are you sure you want to sign out of your account?
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="px-6 pb-6">
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => setShowLogoutConfirm(false)}
                  className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 font-medium text-sm hover:scale-105 active:scale-95"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmLogout}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-xl transition-all duration-200 font-medium text-sm flex items-center justify-center space-x-2 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl"
                >
                  <PowerIcon className="w-4 h-4" />
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}