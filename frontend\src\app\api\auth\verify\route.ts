import { NextResponse } from 'next/server';
import { getUserFromTokenApi } from '@/lib/auth';

export async function GET(request: Request) {
  try {
    const user = await getUserFromTokenApi(request);

    if (!user) {
      return NextResponse.json(
        { message: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      role: user.role,
    });
  } catch (error) {
    return NextResponse.json(
      { message: 'Token verification failed' },
      { status: 401 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
} 