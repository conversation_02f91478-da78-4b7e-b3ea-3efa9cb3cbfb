"use client";

import React, { useState, useEffect } from "react";
import DashboardLayout from "@/components/DashboardLayout";
import api from "@/utils/api";
import { roleOptions } from "@/constants/roleOptions";
import Header from "@/components/admin/Header";
import { User } from "@/types/types";
import UsersTable from "@/components/admin/UsersTable";
import AddUserModal from "@/components/admin/AddUserModal";


export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);


  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone_number: "",
    distrit: "",
    department: "",
    bio: "",
    experience: 0,
    skills: [] as string[],
    role: "",
    role_id: 0,
  });

  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [search, setSearch] = useState("");
  const [districts, setDistricts] = useState([]);
  const [roles, setRoles] = useState([]);

  const fetchUsers = async () => {
    try {
      const { data } = await api.get("/api/v1/user");
      if (Array.isArray(data)) {
        setUsers(data);
      } else if (data.users) {
        setUsers(data.users);
      } else {
        setError("Failed to fetch users");
      }
    } catch (error) {
      setError("Failed to fetch users");
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);


  const handleDeactivateUser = async (userId: string) => {
    if (!window.confirm("Are you sure you want to deactivate this user?")) return;
    try {
      // Instead of deleting, we'll update the user's isActive status
      const { data } = await api.patch(`/api/v1/user/${userId}`, { isActive: false });
      if (data.success) {
        setSuccess("User deactivated successfully");
        fetchUsers();
      } else {
        setError(data.message || "Failed to delete user");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to deactivate user");
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "experience" ? Number(value) : value,
    }));
  };

  const handleSkillsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({
      ...prev,
      skills: e.target.value
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean),
    }));
  };

  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selected = roleOptions.find((r) => r.value === e.target.value);
    setFormData((prev) => ({
      ...prev,
      role: selected?.value || "",
      role_id: selected?.id || 0,
    }));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      phone_number: "",
      distrit: "",
      department: "",
      bio: "",
      experience: 0,
      skills: [],
      role: "FIELD_OFFICER",
      role_id: 3,
    });

  };



  return (
    <div className="w-full max-w-6xl mx-auto p-8 ">
      <Header setIsAddUserModalOpen={setIsAddUserModalOpen} />
      {success && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 text-blue-700 rounded-lg shadow-sm">
          {success}
        </div>
      )}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg shadow-sm">
          {error}
        </div>
      )}
      <UsersTable
        users={users}
        search={search}
        onUserUpdated={fetchUsers}
      />

      {isAddUserModalOpen && (
        <AddUserModal
          setIsAddUserModalOpen={setIsAddUserModalOpen}
          handleSkillsChange={handleSkillsChange}
          callBack={fetchUsers}
        />
      )}


    </div>
  );
}
