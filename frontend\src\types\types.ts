
export type UserRole = 'FIELD_OFFICER' | 'PROJECT_MANAGER' | 'ACCOUNTANT' | 'ADMIN' | 'SENIOR_MANAGER';

export interface User {
  id: string;
  _id?: string;
  name?: string;
  fullName?: string;
  email: string;
  phone_number?: string;
  phoneNumber?: string;
  distrit?: string;
  district?: {
    _id: string;
    name: string;
  };
  department?: {
    _id: string;
    name: string;
  } | string;
  bio?: string;
  experience?: number;
  skills?: string[];
  role?: {
    _id: string;
    name: string;
  } | string;
  roleName?: string;
  role_id?: number;
  permissions?: string[];
  createdAt?: string;
  isActive?: boolean;
  profilePicture?: string;
  isOnline?: boolean;
  lastSeen?: string;
  lastLogin?: string;
}