'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BellIcon, XMarkIcon } from '@heroicons/react/24/solid';
import { api } from '@/services/api';

interface Notification {
  _id: string;
  type: string;
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  createdAt: string;
  actionUrl?: string;
  relatedEntity?: {
    entityType: string;
    entityId: string;
  };
}

interface NotificationBellProps {
  userRole?: string;
  className?: string;
}

// Helper function to get the correct notifications path based on user role
const getNotificationsPath = (userRole?: string) => {
  switch (userRole) {
    case 'projectManager':
      return '/manager/notifications';
    case 'seniorManager':
      return '/senior-manager/notifications';
    case 'fieldOfficer':
      return '/field/notifications';
    case 'accountant':
      return '/accountant/notifications';
    case 'admin':
      return '/admin/notifications';
    default:
      return '/notifications';
  }
};

export default function NotificationBell({ userRole, className = '' }: NotificationBellProps) {
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      // Check if user is authenticated before making API calls
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('⚠️ No authentication token found, skipping notifications fetch');
        setNotifications([]);
        setUnreadCount(0);
        return;
      }

      setLoading(true);
      console.log('🔔 Fetching notifications...');
      console.log('🔔 API Base URL:', process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000');
      console.log('🔔 User Role:', userRole);
      console.log('🔔 Token present:', !!token);

      // Test basic connectivity first
      try {
        const testResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000'}/api/v1/auth/health`);
        console.log('🔔 Health check response:', testResponse.status, testResponse.statusText);
      } catch (healthError) {
        console.error('🔔 Health check failed:', healthError);
      }

      const response = await api.getNotifications();

      console.log('📋 Notifications response:', response);

      if (response.data && Array.isArray(response.data)) {
        setNotifications(response.data);
        setUnreadCount(response.data.filter((n: Notification) => !n.read).length);
        console.log('✅ Notifications loaded:', response.data.length);
      } else {
        console.warn('⚠️ Invalid notifications response format:', response);
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error: any) {
      console.error('❌ Failed to fetch notifications:', error);

      // Handle different types of errors
      if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
        console.error('🌐 Network error - backend may be down');
        console.error('🌐 Attempting to reconnect in 5 seconds...');

        // Try to reconnect after a delay
        setTimeout(() => {
          console.log('🔄 Retrying notification fetch...');
          fetchNotifications();
        }, 5000);

      } else if (error.response?.status === 401) {
        console.error('🔐 Authentication error - redirecting to login');
        // Clear stored authentication data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('userId');
        localStorage.removeItem('userRole');

        // Redirect to login page
        window.location.href = '/login';
        return; // Don't continue processing
      } else if (error.response?.status === 500) {
        console.error('🔥 Server error - backend issue');
      }

      // Set empty state on error (only if not redirecting)
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };



  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('⚠️ No authentication token, cannot mark notification as read');
        return;
      }

      await api.markNotificationAsRead(notificationId);

      setNotifications(prev =>
        prev.map(notification =>
          notification._id === notificationId
            ? { ...notification, read: true }
            : notification
        )
      );

      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('⚠️ No authentication token, cannot mark all notifications as read');
        return;
      }

      await api.markAllNotificationsAsRead();

      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      setUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'project_assigned':
      case 'activity_assigned':
        return '📋';
      case 'activity_report_submitted':
      case 'project_manager_report_submitted':
        return '📄';
      case 'late_report_submission':
      case 'activity_overdue':
        return '⚠️';
      case 'budget_overuse':
      case 'budget_overuse_alert':
        return '💸';
      case 'budget_underuse':
      case 'budget_underuse_alert':
        return '💰';
      case 'schedule_slippage':
        return '⏰';
      case 'report_approved':
        return '✅';
      case 'report_rejected':
        return '❌';
      case 'activity_due_reminder':
        return '🔔';
      default:
        return '📢';
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'low':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Format time ago
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification._id);
    }
    
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
    
    setShowDropdown(false);
  };

  // Fetch notifications on component mount
  useEffect(() => {
    console.log('🔔 NotificationBell component mounted, userRole:', userRole);
    fetchNotifications();

    // Set up more frequent polling for real-time updates
    const interval = setInterval(() => {
      console.log('🔄 Polling for notifications...');
      fetchNotifications();
    }, 15000); // Poll every 15 seconds

    // Also fetch when the window gains focus
    const handleFocus = () => {
      console.log('🔍 Window focused, fetching notifications...');
      fetchNotifications();
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Additional effect to check for new notifications more frequently during active use
  useEffect(() => {
    let activeInterval: NodeJS.Timeout;

    const handleUserActivity = () => {
      // Clear existing interval
      if (activeInterval) {
        clearInterval(activeInterval);
      }

      // Set up more frequent checking when user is active
      activeInterval = setInterval(fetchNotifications, 10000); // Poll every 10 seconds when active

      // Reset to normal interval after 2 minutes of inactivity
      setTimeout(() => {
        if (activeInterval) {
          clearInterval(activeInterval);
        }
      }, 120000);
    };

    // Listen for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    return () => {
      if (activeInterval) {
        clearInterval(activeInterval);
      }
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.notification-dropdown')) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDropdown]);

  return (
    <div className={`relative notification-dropdown ${className}`}>
      {/* Notification Bell */}
      <button
        onClick={() => {
          console.log('🔔 Notification bell clicked, current state:', { showDropdown, userRole, unreadCount });
          setShowDropdown(!showDropdown);
        }}
        className="relative p-2 text-gray-600 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full transition-colors"
        aria-label="Notifications"
      >
        <BellIcon className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold animate-pulse">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}

      </button>

      {/* Notification Dropdown */}
      {showDropdown && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b bg-gray-50">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Mark all as read
                  </button>
                )}
                <button
                  onClick={() => setShowDropdown(false)}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-200 transition-colors"
                  aria-label="Close notifications"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
            {unreadCount > 0 && (
              <p className="text-sm text-gray-600 mt-1">{unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}</p>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">Loading notifications...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <BellIcon className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                <p>No notifications yet</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification._id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-4 border-b last:border-b-0 cursor-pointer hover:bg-gray-50 transition-colors ${
                    !notification.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 text-lg">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <p className={`text-sm font-medium text-gray-900 ${!notification.read ? 'font-semibold' : ''}`}>
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500">
                              {getTimeAgo(notification.createdAt)}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full border ${getPriorityColor(notification.priority)}`}>
                              {notification.priority}
                            </span>
                          </div>
                        </div>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="p-3 border-t bg-gray-50">
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    console.log('📋 View All notifications clicked, userRole:', userRole);
                    setShowDropdown(false);
                    // Navigate to role-specific notifications page
                    const notificationsPath = getNotificationsPath(userRole);
                    console.log('🔗 Navigating to notifications path:', notificationsPath);
                    router.push(notificationsPath);
                  }}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  View all notifications
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
