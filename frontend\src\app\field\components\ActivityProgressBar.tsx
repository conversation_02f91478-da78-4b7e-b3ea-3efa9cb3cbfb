import React from 'react';
import { CheckCircleIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/solid';

interface Activity {
  _id: string;
  title: string;
  status: 'completed' | 'ongoing' | 'pending' | 'overdue';
  progress: number;
  dueDate?: string;
  hasReport?: boolean;
  reportStatus?: 'submitted' | 'approved' | 'rejected' | 'pending';
}

interface ActivityProgressBarProps {
  activities: Activity[];
  totalActivities: number;
  reportedActivities: number;
}

const ActivityProgressBar: React.FC<ActivityProgressBarProps> = ({
  activities,
  totalActivities,
  reportedActivities
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-blue-500" />;
      case 'ongoing':
        return <ClockIcon className="w-5 h-5 text-yellow-500" />;
      case 'overdue':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-blue-500';
      case 'ongoing':
        return 'bg-yellow-500';
      case 'overdue':
        return 'bg-red-500';
      default:
        return 'bg-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'ongoing':
        return 'In Progress';
      case 'overdue':
        return 'Overdue';
      default:
        return 'Pending';
    }
  };

  const reportingPercentage = totalActivities > 0 ? Math.round((reportedActivities / totalActivities) * 100) : 0;

  if (!activities || activities.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Activity Progress</h3>
        <div className="text-center py-8 text-gray-500">
          No activities assigned yet
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-800">Activity Progress</h3>
        <div className="text-sm text-gray-600">
          <span className="font-medium text-blue-600">{reportedActivities}</span> of{' '}
          <span className="font-medium">{totalActivities}</span> activities reported
        </div>
      </div>

      {/* Overall Reporting Progress */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">Overall Reporting Progress</span>
          <span className="text-sm font-bold text-blue-600">{reportingPercentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className="bg-blue-500 h-3 rounded-full transition-all duration-300"
            style={{ width: `${reportingPercentage}%` }}
          ></div>
        </div>
      </div>

      <div className="space-y-4">
        {activities.map((activity) => (
          <div key={activity._id} className="border border-gray-100 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-3">
                {getStatusIcon(activity.status)}
                <h4 className="font-medium text-gray-800">{activity.title}</h4>
                {activity.hasReport && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    activity.reportStatus === 'approved' ? 'bg-green-100 text-green-800' :
                    activity.reportStatus === 'submitted' ? 'bg-yellow-100 text-yellow-800' :
                    activity.reportStatus === 'rejected' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {activity.reportStatus === 'approved' ? '✓ Approved' :
                     activity.reportStatus === 'submitted' ? '⏳ Pending' :
                     activity.reportStatus === 'rejected' ? '✗ Rejected' :
                     '📝 Reported'}
                  </span>
                )}
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                activity.status === 'completed' ? 'bg-green-100 text-green-800' :
                activity.status === 'ongoing' ? 'bg-blue-100 text-blue-800' :
                activity.status === 'overdue' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {getStatusText(activity.status)}
              </span>
            </div>

            <div className="mb-2">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Activity Progress</span>
                <span>{activity.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${getStatusColor(activity.status)}`}
                  style={{ width: `${activity.progress}%` }}
                ></div>
              </div>
            </div>

            <div className="flex justify-between items-center text-xs text-gray-500">
              {activity.dueDate && (
                <span>Due: {new Date(activity.dueDate).toLocaleDateString()}</span>
              )}
              {!activity.hasReport && (
                <span className="text-orange-600 font-medium">⚠ Report Needed</span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ActivityProgressBar;
