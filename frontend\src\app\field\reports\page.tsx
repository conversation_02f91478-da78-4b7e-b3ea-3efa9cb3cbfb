'use client';
import { useState, useEffect } from 'react';
import { ChevronUpIcon, ChevronDownIcon, PencilIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/solid';
import { api } from '@/services/api';
import { toast } from 'react-hot-toast';

interface Report {
  _id: string;
  activity: {
    _id: string;
    title: string;
    project?: {
      name: string;
    };
  };
  content: Array<{
    fieldName: string;
    entry: string;
    required: boolean;
  }> | string; // Support both array and string formats for backward compatibility
  submittedBy: {
    fullName: string;
  };
  createdAt: string;
  approved: boolean;
  approvedBy?: {
    fullName: string;
  };
  approvedAt?: string;
  rejectedBy?: {
    fullName: string;
  };
  rejectedAt?: string;
  rejectionReason?: string;
  status: 'pending' | 'approved' | 'rejected';
}

export default function ReportsPage() {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Fetch reports on component mount
  useEffect(() => {
    const fetchReports = async () => {
      setLoading(true);
      try {
        console.log('🔄 Fetching all field officer reports...');
        const response = await api.getAllReportsForFieldOfficer();
        console.log('✅ All reports loaded:', response);

        const reportsData = Array.isArray(response.data)
          ? response.data
          : response.data?.data || [];

        setReports(reportsData);
      } catch (err: any) {
        console.error('❌ Failed to load reports:', err);
        const errorMessage = err?.response?.data?.message || err?.message || 'Failed to load reports';
        setError(errorMessage);
        toast.error(`Error: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    fetchReports();
  }, []);

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }

    const sortedReports = [...reports].sort((a, b) => {
      let aValue, bValue;

      switch (field) {
        case 'title':
          aValue = a.activity?.title || '';
          bValue = b.activity?.title || '';
          break;
        case 'project':
          aValue = a.activity?.project?.name || '';
          bValue = b.activity?.project?.name || '';
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'status':
          aValue = a.approved ? 'approved' : (a.rejectedBy ? 'rejected' : 'pending');
          bValue = b.approved ? 'approved' : (b.rejectedBy ? 'rejected' : 'pending');
          break;
        default:
          return 0;
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      }
      return aValue < bValue ? 1 : -1;
    });

    setReports(sortedReports);
  };

  const handleView = (reportId: string) => {
    const report = reports.find(r => r._id === reportId);
    if (report) {
      toast.success(`Viewing report: ${report.activity?.title || 'Untitled'}`);
      // You can implement a modal or navigate to a detailed view here
    }
  };

  const handleEdit = (reportId: string) => {
    const report = reports.find(r => r._id === reportId);
    if (report && !report.approved && !report.rejectedBy) {
      toast.info(`Edit functionality for: ${report.activity?.title || 'Untitled'}`);
      // Implement edit functionality for pending reports only
    } else if (report?.approved) {
      toast.error('Cannot edit approved reports');
    } else if (report?.rejectedBy) {
      toast.error('Cannot edit rejected reports');
    }
  };

  const handleDelete = (reportId: string) => {
    const report = reports.find(r => r._id === reportId);
    if (report && !report.approved && !report.rejectedBy) {
      if (confirm('Are you sure you want to delete this report?')) {
        setReports(reports.filter(report => report._id !== reportId));
        toast.success('Report deleted successfully');
        // Here you would typically make an API call to delete the report
      }
    } else if (report?.approved) {
      toast.error('Cannot delete approved reports');
    } else if (report?.rejectedBy) {
      toast.error('Cannot delete rejected reports');
    }
  };

  if (loading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600"></div>
          <span className="ml-3 text-gray-600">Loading reports...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error Loading Reports</h3>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">My Reports</h1>
        <div className="text-sm text-gray-600">
          Total: {reports.length} reports
        </div>
      </div>
      {reports.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Reports Found</h3>
          <p className="text-gray-600">You haven't submitted any reports yet.</p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('title')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Activity Title</span>
                    {sortField === 'title' && (
                      sortDirection === 'asc' ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('project')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Project</span>
                    {sortField === 'project' && (
                      sortDirection === 'asc' ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('createdAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Submission Date</span>
                    {sortField === 'createdAt' && (
                      sortDirection === 'asc' ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Status</span>
                    {sortField === 'status' && (
                      sortDirection === 'asc' ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />
                    )}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {reports.map((report) => {
                const reportStatus = report.approved ? 'approved' : (report.rejectedBy ? 'rejected' : 'pending');
                return (
                  <tr key={report._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {report.activity?.title || 'Untitled Activity'}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {Array.isArray(report.content)
                          ? report.content[0]?.entry?.substring(0, 50) + '...'
                          : typeof report.content === 'string'
                            ? report.content.substring(0, 50) + '...'
                            : 'No content'
                        }
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {report.activity?.project?.name || 'No Project'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(report.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(report.createdAt).toLocaleTimeString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${reportStatus === 'approved' ? 'bg-blue-100 text-blue-800' :
                          reportStatus === 'rejected' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'}`}
                      >
                        {reportStatus === 'approved' ? '✓ Approved' :
                         reportStatus === 'rejected' ? '✗ Rejected' : '⏳ Pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleView(report._id)}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Report"
                        >
                          <EyeIcon className="w-5 h-5" />
                        </button>
                        {!report.approved && !report.rejectedBy && (
                          <>
                            <button
                              onClick={() => handleEdit(report._id)}
                              className="text-sky-600 hover:text-sky-900"
                              title="Edit Report"
                            >
                              <PencilIcon className="w-5 h-5" />
                            </button>
                            <button
                              onClick={() => handleDelete(report._id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete Report"
                            >
                              <TrashIcon className="w-5 h-5" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}