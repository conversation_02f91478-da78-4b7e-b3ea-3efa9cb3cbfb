"use client";
import React, { useState, useEffect } from "react";
import {
  ChartBarIcon,
  DocumentArrowDownIcon,
  CurrencyDollarIcon,
  ChartPieIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,

} from "@heroicons/react/24/outline";
import { Bar, Pie, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { api } from "@/services/api";
import apiUtil from "@/utils/api";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import html2canvas from 'html2canvas';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

interface ProjectBudgetData {
  id: string;
  name: string;
  totalBudget: number;
  usedBudget: number;
  remainingBudget: number;
  utilizationPercentage: number;
  status: 'on-track' | 'at-risk' | 'over-budget';
  manager: string;
  startDate: string;
  endDate: string;
  activities: number;
  completedActivities: number;
  monthlySpending: { month: string; amount: number }[];
  categoryBreakdown: { category: string; amount: number; percentage: number }[];
}

export default function BudgetMonitoringPage() {
  const [projects, setProjects] = useState<ProjectBudgetData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState<string>("");

  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [showDebug, setShowDebug] = useState(false);

  useEffect(() => {
    const fetchProjectBudgetData = async () => {
      try {
        // Fetch project budget monitoring data from the new endpoint
        const response = await apiUtil.get('/api/v1/accountant/budgets/monitoring');

        if (response.data && response.data.status === 'success') {
          setProjects(response.data.data || []);
        }
      } catch (error) {
        console.error('Error fetching budget data:', error);
        setToast({
          message: 'Failed to load budget data. Please ensure backend server is running.',
          type: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProjectBudgetData();
  }, []);

  // Debug function to check what projects exist
  const fetchDebugInfo = async () => {
    try {
      const response = await apiUtil.get('/api/v1/accountant/debug/projects');
      if (response.data && response.data.status === 'success') {
        setDebugInfo(response.data.data);
        setShowDebug(true);
      }
    } catch (error) {
      console.error('Error fetching debug info:', error);
      setToast({
        message: 'Failed to fetch debug information.',
        type: 'error'
      });
    }
  };



  // Calculate overall stats
  const overallStats = {
    totalProjects: projects.length,
    onTrack: projects.filter(p => (p.status || '') === 'on-track').length,
    atRisk: projects.filter(p => (p.status || '') === 'at-risk').length,
    overBudget: projects.filter(p => (p.status || '') === 'over-budget').length,
    totalBudget: projects.reduce((sum, p) => sum + (p.totalBudget || p.initialBudget || 0), 0),
    totalUsed: projects.reduce((sum, p) => sum + (p.usedBudget || 0), 0),
    averageUtilization: projects.length > 0 ?
      projects.reduce((sum, p) => sum + (p.utilizationPercentage || 0), 0) / projects.length : 0
  };

  // Filter projects based on selection
  const filteredProjects = selectedProject
    ? projects.filter(project => project.id === selectedProject)
    : projects;

  // PDF Export functionality
  const exportProjectPDF = async (projectId: string) => {
    if (!projectId) {
      setToast({
        message: "Please select a project to export.",
        type: "error"
      });
      return;
    }

    setIsExporting(true);
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) {
        throw new Error("Project not found");
      }

      const doc = new jsPDF();
      let yPosition = 20;

      // Header
      doc.setFontSize(20);
      doc.text('Project Financial Report', 20, yPosition);
      yPosition += 20;

      doc.setFontSize(12);
      doc.text(`Project: ${project.name}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Manager: ${project.manager}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Report Date: ${new Date().toLocaleDateString()}`, 20, yPosition);
      yPosition += 20;

      // Budget Summary
      doc.setFontSize(16);
      doc.text('Budget Summary', 20, yPosition);
      yPosition += 10;

      const budgetData = [
        ['Total Budget', formatCurrency(project.totalBudget || project.initialBudget || 0)],
        ['Used Budget', formatCurrency(project.usedBudget || 0)],
        ['Remaining Budget', formatCurrency((project.remainingBudget || project.totalBudget || project.initialBudget || 0) - (project.usedBudget || 0))],
        ['Utilization %', `${(project.utilizationPercentage || 0).toFixed(1)}%`],
        ['Status', (project.status || 'unknown').toUpperCase()]
      ];

      autoTable(doc, {
        head: [['Metric', 'Value']],
        body: budgetData,
        startY: yPosition,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [59, 130, 246] }
      });

      yPosition = (doc as any).lastAutoTable.finalY + 20;

      // Add Budget Utilization Chart
      try {
        doc.setFontSize(16);
        doc.text('Budget Utilization Chart', 20, yPosition);
        yPosition += 10;

        // Create a temporary canvas for the chart
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 200;
        const ctx = canvas.getContext('2d');

        if (ctx) {
          // Create a simple bar chart
          const chartData = {
            labels: ['Total Budget', 'Used Budget', 'Remaining Budget'],
            datasets: [{
              data: [project.totalBudget, project.usedBudget, project.remainingBudget],
              backgroundColor: ['#3B82F6', '#10B981', '#F59E0B'],
              borderColor: ['#2563EB', '#059669', '#D97706'],
              borderWidth: 1
            }]
          };

          // Simple bar chart drawing
          const maxValue = Math.max(project.totalBudget, project.usedBudget, project.remainingBudget);
          const barWidth = 80;
          const barSpacing = 40;
          const chartHeight = 120;
          const startX = 50;
          const startY = 150;

          // Draw bars
          chartData.labels.forEach((label, index) => {
            const value = chartData.datasets[0].data[index];
            const barHeight = (value / maxValue) * chartHeight;
            const x = startX + (index * (barWidth + barSpacing));
            const y = startY - barHeight;

            // Draw bar
            ctx.fillStyle = chartData.datasets[0].backgroundColor[index];
            ctx.fillRect(x, y, barWidth, barHeight);

            // Draw label
            ctx.fillStyle = '#000';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + barWidth/2, startY + 20);

            // Draw value
            ctx.fillText(formatCurrencyCompact(value), x + barWidth/2, y - 5);
          });

          // Convert canvas to image and add to PDF
          const chartImage = canvas.toDataURL('image/png');
          doc.addImage(chartImage, 'PNG', 20, yPosition, 160, 80);
          yPosition += 90;
        }
      } catch (error) {
        console.log('Chart generation failed:', error);
        yPosition += 20;
      }

      // Category Breakdown
      doc.setFontSize(16);
      doc.text('Budget Breakdown by Category', 20, yPosition);
      yPosition += 10;

      const categoryData = project.categoryBreakdown.map(cat => [
        cat.category,
        formatCurrency(cat.amount),
        `${cat.percentage}%`
      ]);

      autoTable(doc, {
        head: [['Category', 'Amount', 'Percentage']],
        body: categoryData,
        startY: yPosition,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [59, 130, 246] }
      });

      yPosition = (doc as any).lastAutoTable.finalY + 20;

      // Add Category Breakdown Pie Chart
      try {
        doc.setFontSize(16);
        doc.text('Category Distribution Chart', 20, yPosition);
        yPosition += 10;

        // Create pie chart canvas
        const pieCanvas = document.createElement('canvas');
        pieCanvas.width = 300;
        pieCanvas.height = 200;
        const pieCtx = pieCanvas.getContext('2d');

        if (pieCtx) {
          const centerX = 150;
          const centerY = 100;
          const radius = 60;
          let currentAngle = 0;
          const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

          project.categoryBreakdown.forEach((cat, index) => {
            const sliceAngle = (cat.percentage / 100) * 2 * Math.PI;

            // Draw slice
            pieCtx.beginPath();
            pieCtx.moveTo(centerX, centerY);
            pieCtx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            pieCtx.closePath();
            pieCtx.fillStyle = colors[index % colors.length];
            pieCtx.fill();

            // Draw label
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelX = centerX + Math.cos(labelAngle) * (radius + 20);
            const labelY = centerY + Math.sin(labelAngle) * (radius + 20);

            pieCtx.fillStyle = '#000';
            pieCtx.font = '10px Arial';
            pieCtx.textAlign = 'center';
            pieCtx.fillText(`${cat.category} (${cat.percentage}%)`, labelX, labelY);

            currentAngle += sliceAngle;
          });

          const pieImage = pieCanvas.toDataURL('image/png');
          doc.addImage(pieImage, 'PNG', 20, yPosition, 120, 80);
          yPosition += 90;
        }
      } catch (error) {
        console.log('Pie chart generation failed:', error);
        yPosition += 20;
      }

      // Monthly Spending
      doc.setFontSize(16);
      doc.text('Monthly Spending Trend', 20, yPosition);
      yPosition += 10;

      const monthlyData = project.monthlySpending.map(month => [
        month.month,
        formatCurrency(month.amount)
      ]);

      autoTable(doc, {
        head: [['Month', 'Amount Spent']],
        body: monthlyData,
        startY: yPosition,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [59, 130, 246] }
      });

      doc.save(`${project.name}_Financial_Report.pdf`);

      setToast({
        message: "Financial report exported successfully!",
        type: "success"
      });
    } catch (error) {
      console.error("Export failed:", error);
      setToast({
        message: "Failed to export report. Please try again.",
        type: "error"
      });
    } finally {
      setIsExporting(false);
      setTimeout(() => setToast(null), 3000);
    }
  };

  // Export All Projects PDF functionality
  const exportAllProjectsPDF = async () => {
    setIsExporting(true);
    try {
      const doc = new jsPDF();
      let yPosition = 20;

      // Header
      doc.setFontSize(20);
      doc.text('All Projects Financial Report', 20, yPosition);
      yPosition += 10;

      doc.setFontSize(12);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, yPosition);
      yPosition += 20;

      // Overall Statistics
      const totalBudget = projects.reduce((sum, p) => sum + (p.initialBudget || 0), 0);
      const totalUsed = projects.reduce((sum, p) => sum + (p.usedBudget || 0), 0);
      const overallUtilization = totalBudget > 0 ? ((totalUsed / totalBudget) * 100).toFixed(1) : '0';

      const overallStats = [
        ['Total Projects', projects.length.toString()],
        ['Total Budget', `MWK ${totalBudget.toLocaleString()}`],
        ['Total Used', `MWK ${totalUsed.toLocaleString()}`],
        ['Overall Utilization', `${overallUtilization}%`],
        ['On Track Projects', projects.filter(p => p.status === 'on-track').length.toString()],
        ['At Risk Projects', projects.filter(p => p.status === 'at-risk').length.toString()],
        ['Over Budget Projects', projects.filter(p => p.status === 'over-budget').length.toString()]
      ];

      autoTable(doc, {
        head: [['Metric', 'Value']],
        body: overallStats,
        startY: yPosition,
        styles: { fontSize: 10 },
        headStyles: { fillColor: [59, 130, 246] }
      });

      yPosition = (doc as any).lastAutoTable.finalY + 20;

      // Project Details Table
      doc.setFontSize(16);
      doc.text('Project Details', 20, yPosition);
      yPosition += 10;

      const projectData = projects.map(project => [
        project.name || 'Unnamed Project',
        `MWK ${(project.initialBudget || 0).toLocaleString()}`,
        `MWK ${(project.usedBudget || 0).toLocaleString()}`,
        `${(project.utilizationPercentage || 0).toFixed(1)}%`,
        (project.status || 'unknown').replace('-', ' ').toUpperCase()
      ]);

      autoTable(doc, {
        head: [['Project Name', 'Budget', 'Used', 'Utilization', 'Status']],
        body: projectData,
        startY: yPosition,
        styles: { fontSize: 9 },
        headStyles: { fillColor: [59, 130, 246] }
      });

      doc.save('All_Projects_Financial_Report.pdf');

      setToast({
        message: "All projects PDF exported successfully!",
        type: "success"
      });
    } catch (error) {
      console.error('Error exporting all projects PDF:', error);
      setToast({
        message: "Failed to export PDF. Please try again.",
        type: "error"
      });
    } finally {
      setIsExporting(false);
      setTimeout(() => setToast(null), 3000);
    }
  };

  // Utility functions
  const formatCurrency = (amount: number) => {
    return `MWK ${amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  // Compact currency format for charts (optional)
  const formatCurrencyCompact = (amount: number) => {
    if (amount >= 1000000000) {
      return `MWK ${(amount / 1000000000).toFixed(1)}B`;
    } else if (amount >= 1000000) {
      return `MWK ${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `MWK ${(amount / 1000).toFixed(1)}K`;
    } else {
      return `MWK ${amount.toLocaleString()}`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track': return 'text-green-600 bg-green-100';
      case 'at-risk': return 'text-yellow-600 bg-yellow-100';
      case 'over-budget': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'on-track': return <CheckCircleIcon className="w-5 h-5" />;
      case 'at-risk': return <ExclamationTriangleIcon className="w-5 h-5" />;
      case 'over-budget': return <ArrowTrendingDownIcon className="w-5 h-5" />;
      default: return <ChartBarIcon className="w-5 h-5" />;
    }
  };

  // Chart configurations
  const budgetUtilizationData = {
    labels: filteredProjects.map(p => p.name || 'Unnamed Project'),
    datasets: [
      {
        label: 'Total Budget',
        data: filteredProjects.map(p => p.totalBudget || p.initialBudget || 0),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'Used Budget',
        data: filteredProjects.map(p => p.usedBudget || 0),
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      }
    ],
  };

  const utilizationPercentageData = {
    labels: filteredProjects.map(p => p.name || 'Unnamed Project'),
    datasets: [
      {
        label: 'Utilization %',
        data: filteredProjects.map(p => p.utilizationPercentage || 0),
        backgroundColor: filteredProjects.map(p =>
          (p.status || '') === 'over-budget' ? 'rgba(239, 68, 68, 0.5)' :
          (p.status || '') === 'at-risk' ? 'rgba(245, 158, 11, 0.5)' :
          'rgba(16, 185, 129, 0.5)'
        ),
        borderColor: filteredProjects.map(p =>
          (p.status || '') === 'over-budget' ? 'rgba(239, 68, 68, 1)' :
          (p.status || '') === 'at-risk' ? 'rgba(245, 158, 11, 1)' :
          'rgba(16, 185, 129, 1)'
        ),
        borderWidth: 1,
      }
    ],
  };

  const statusDistributionData = {
    labels: ['On Track', 'At Risk', 'Over Budget'],
    datasets: [
      {
        data: [overallStats.onTrack, overallStats.atRisk, overallStats.overBudget],
        backgroundColor: [
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
        borderColor: [
          'rgba(16, 185, 129, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(239, 68, 68, 1)',
        ],
        borderWidth: 1,
      }
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading budget data...</p>
        </div>
      </div>
    );
  }

  // Show empty state if no projects
  if (projects.length === 0) {
    return (
      <>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <ChartBarIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Found</h3>
            <p className="text-gray-600 mb-4">There are currently no active projects with budget data to display.</p>
            <p className="text-sm text-gray-500 mb-4">Projects will appear here once they are created and have budget allocations.</p>
            <button
              onClick={fetchDebugInfo}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Check Database Status
            </button>
          </div>
        </div>

        {/* Debug Info Modal */}
        {showDebug && debugInfo && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6">
              <div className="flex justify-between items-start mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Database Status</h2>
                <button
                  onClick={() => setShowDebug(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Project Summary</h3>
                  <p>Total Projects: {debugInfo.totalProjects}</p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Status Breakdown</h3>
                  {debugInfo.statusBreakdown.map((status: any) => (
                    <p key={status._id}>{status._id || 'No Status'}: {status.count}</p>
                  ))}
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">All Projects</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-200">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-200 px-4 py-2 text-left">Name</th>
                          <th className="border border-gray-200 px-4 py-2 text-left">Status</th>
                          <th className="border border-gray-200 px-4 py-2 text-left">Budget</th>
                          <th className="border border-gray-200 px-4 py-2 text-left">Created</th>
                        </tr>
                      </thead>
                      <tbody>
                        {debugInfo.projects.map((project: any) => (
                          <tr key={project._id}>
                            <td className="border border-gray-200 px-4 py-2">{project.title}</td>
                            <td className="border border-gray-200 px-4 py-2">{project.status}</td>
                            <td className="border border-gray-200 px-4 py-2">MK {project.initialBudget?.toLocaleString() || 0}</td>
                            <td className="border border-gray-200 px-4 py-2">
                              {new Date(project.createdAt).toLocaleDateString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Toast Notification */}
        {toast && (
          <div className={`fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg ${
            toast.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
          }`}>
            {toast.message}
          </div>
        )}
      </>
    );
  }

  return (
    <>
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Budget Monitoring</h1>
          <p className="text-gray-600 mt-1">Monitor project costs and budget utilization. Select a project to view specific data or view all projects.</p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={selectedProject}
            onChange={(e) => setSelectedProject(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
          >
            <option value="">All Projects</option>
            {projects.map((project) => (
              <option key={project.id} value={project.id}>{project.name}</option>
            ))}
          </select>
          <button
            onClick={() => selectedProject ? exportProjectPDF(selectedProject) : exportAllProjectsPDF()}
            disabled={isExporting}
            className="px-4 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <DocumentArrowDownIcon className="w-4 h-4" />
            {isExporting ? 'Exporting...' : selectedProject ? 'Export Project PDF' : 'Export All Projects PDF'}
          </button>
        </div>
      </div>



      {/* Overview Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-gray-900">{overallStats.totalProjects}</p>
            </div>
            <div className="p-2 bg-blue-100 rounded-lg">
              <ChartBarIcon className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Budget</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(overallStats.totalBudget)}</p>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <CurrencyDollarIcon className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Used</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(overallStats.totalUsed)}</p>
            </div>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <ArrowTrendingUpIcon className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Utilization</p>
              <p className="text-2xl font-bold text-gray-900">{overallStats.averageUtilization.toFixed(1)}%</p>
            </div>
            <div className="p-2 bg-purple-100 rounded-lg">
              <ChartPieIcon className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Budget vs Used Chart */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget vs Used Amount</h3>
          <div className="h-80">
            {filteredProjects.length > 0 ? (
              <Bar data={budgetUtilizationData} options={chartOptions} />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <ChartBarIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No project data available</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Utilization Percentage Chart */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget Utilization Percentage</h3>
          <div className="h-80">
            {filteredProjects.length > 0 ? (
              <Bar data={utilizationPercentageData} options={{
                ...chartOptions,
                scales: {
                  y: {
                    beginAtZero: true,
                    max: 120,
                    ticks: {
                      callback: function(value) {
                        return value + '%';
                      }
                    }
                  }
                }
              }} />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <ChartBarIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No project data available</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status Distribution Pie Chart */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Status Distribution</h3>
          <div className="h-80">
            {projects.length > 0 ? (
              <Pie data={statusDistributionData} options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: 'bottom' as const,
                  },
                },
              }} />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <ChartPieIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No project data available</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Budget Health Overview */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget Health Overview</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-3">
                <CheckCircleIcon className="w-6 h-6 text-green-600" />
                <span className="font-medium text-green-900">On Track Projects</span>
              </div>
              <span className="text-2xl font-bold text-green-600">{overallStats.onTrack}</span>
            </div>
            <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
              <div className="flex items-center gap-3">
                <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
                <span className="font-medium text-yellow-900">At Risk Projects</span>
              </div>
              <span className="text-2xl font-bold text-yellow-600">{overallStats.atRisk}</span>
            </div>
            <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
              <div className="flex items-center gap-3">
                <ArrowTrendingDownIcon className="w-6 h-6 text-red-600" />
                <span className="font-medium text-red-900">Over Budget Projects</span>
              </div>
              <span className="text-2xl font-bold text-red-600">{overallStats.overBudget}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Project Details Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Project Budget Details</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Project Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Manager
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Budget
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Used Budget
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Remaining
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProjects.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <ChartBarIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">No projects found</p>
                      <p className="text-sm">No budget data available at the moment</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredProjects.map((project) => (
                  <tr key={project.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{project.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{project.manager}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{formatCurrency(project.totalBudget)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{formatCurrency(project.usedBudget)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{formatCurrency(project.remainingBudget)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900 mr-2">
                          {project.utilizationPercentage.toFixed(1)}%
                        </div>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              project.status === 'over-budget' ? 'bg-red-500' :
                              project.status === 'at-risk' ? 'bg-yellow-500' :
                              'bg-green-500'
                            }`}
                            style={{ width: `${Math.min(project.utilizationPercentage, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.status)}`}>
                        {getStatusIcon(project.status)}
                        {project.status.replace('-', ' ').toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => exportProjectPDF(project.id)}
                        className="text-sky-600 hover:text-sky-900 flex items-center gap-1"
                      >
                        <DocumentArrowDownIcon className="w-4 h-4" />
                        Export
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Toast Notification */}
      {toast && (
        <div className={`fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg ${
          toast.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`}>
          {toast.message}
        </div>
      )}
    </>
  );
}