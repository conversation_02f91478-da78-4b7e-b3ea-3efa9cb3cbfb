// app/layout.tsx  – Next.js 13+ / App Router
import React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "./theme-context";
import { Toaster } from 'react-hot-toast';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Sprodeta",
  description: "Sprodeta",
};

/**
 * Root layout, wraps every page.
 * Adds global light + dark colours to <body>
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`
          ${inter.className}
          min-h-screen
          bg-gray-50 text-gray-900       /* light  */
          dark:bg-gray-900 dark:text-gray-100 /* dark   */
          transition-colors duration-300
        `}
      >
        <ThemeProvider>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                style: {
                  background: '#10B981',
                },
              },
              error: {
                duration: 5000,
                style: {
                  background: '#EF4444',
                },
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  );
}
