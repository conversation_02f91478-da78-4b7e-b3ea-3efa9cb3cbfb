import axios from 'axios';

const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000';

export interface HealthCheckResult {
  status: 'success' | 'error';
  message: string;
  details?: any;
}

export const checkBackendHealth = async (): Promise<HealthCheckResult> => {
  try {
    const response = await axios.get(`${baseUrl}/health`, {
      timeout: 5000
    });

    return {
      status: 'success',
      message: 'Backend server is running',
      details: response.data
    };
  } catch (error: any) {
    if (error.code === 'ECONNREFUSED') {
      return {
        status: 'error',
        message: 'Backend server is not running. Please start the server.',
        details: { error: 'Connection refused', baseUrl }
      };
    } else if (error.code === 'ENOTFOUND') {
      return {
        status: 'error',
        message: 'Backend server host not found. Check your configuration.',
        details: { error: 'Host not found', baseUrl }
      };
    } else if (error.code === 'ETIMEDOUT') {
      return {
        status: 'error',
        message: 'Backend server is not responding. Check if it\'s running.',
        details: { error: 'Timeout', baseUrl }
      };
    } else {
      return {
        status: 'error',
        message: `Backend connection error: ${error.message}`,
        details: { error: error.message, baseUrl }
      };
    }
  }
};

export const checkApiEndpoints = async (): Promise<{[key: string]: HealthCheckResult}> => {
  const endpoints = [
    { name: 'districts', url: '/api/v1/district' },
    { name: 'roles', url: '/api/v1/roles' },
    { name: 'users', url: '/api/v1/user' }
  ];

  const results: {[key: string]: HealthCheckResult} = {};

  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`${baseUrl}${endpoint.url}`, {
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept 4xx as valid responses
      });

      if (response.status < 400) {
        results[endpoint.name] = {
          status: 'success',
          message: `${endpoint.name} endpoint is working`,
          details: { status: response.status, dataLength: Array.isArray(response.data) ? response.data.length : 'N/A' }
        };
      } else if (response.status === 401) {
        results[endpoint.name] = {
          status: 'success',
          message: `${endpoint.name} endpoint is working (authentication required)`,
          details: { status: response.status, message: 'Authentication required' }
        };
      } else {
        results[endpoint.name] = {
          status: 'error',
          message: `${endpoint.name} endpoint returned error`,
          details: { status: response.status, data: response.data }
        };
      }
    } catch (error: any) {
      results[endpoint.name] = {
        status: 'error',
        message: `${endpoint.name} endpoint failed: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  return results;
};

export const runFullHealthCheck = async () => {
  console.log('🏥 Running health check...');
  
  const backendHealth = await checkBackendHealth();
  console.log(`Backend Health: ${backendHealth.status === 'success' ? '✅' : '❌'} ${backendHealth.message}`);
  
  if (backendHealth.status === 'success') {
    const endpointResults = await checkApiEndpoints();
    
    console.log('\n📡 API Endpoints:');
    Object.entries(endpointResults).forEach(([name, result]) => {
      console.log(`${result.status === 'success' ? '✅' : '❌'} ${name}: ${result.message}`);
    });
  }
  
  return {
    backend: backendHealth,
    endpoints: backendHealth.status === 'success' ? await checkApiEndpoints() : {}
  };
};
