'use client';
import React, { useState, useEffect } from 'react';
import {
  UserGroupIcon,
  UserPlusIcon,
  ShieldCheckIcon,
  ClockIcon,
  DocumentTextIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import UserManagement from '../users/page';
import api from '@/utils/api';
import { useRouter } from 'next/navigation';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  totalRoles: number;
  recentActivities: any[];
}

export default function AdminDashboard() {
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    newUsersToday: 0,
    totalRoles: 0,
    recentActivities: []
  });
  const [loading, setLoading] = useState(true);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchDashboardStats();

    // Set up auto-refresh for active users count every 30 seconds
    const interval = setInterval(() => {
      fetchDashboardStats();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);

      // Use the proper admin dashboard stats endpoint
      const response = await api.get('/api/v1/admin/dashboard/stats');

      console.log('📊 Admin Dashboard API Response:', response.data);

      if (response.data && response.data.status === 'success') {
        const data = response.data.data;



        setStats({
          totalUsers: data.totalUsers || 0,
          activeUsers: data.activeUsers || 0,
          newUsersToday: data.newUsersToday || 0,
          totalRoles: data.totalRoles || 0,
          recentActivities: data.recentActivities || []
        });
      } else {
        setConnectionError('Invalid response format from server');
      }

      setLastUpdated(new Date());
    } catch (error: any) {
      // Check if it's a connection error
      if (error.code === 'ERR_NETWORK' || error.code === 'ERR_CONNECTION_REFUSED' || error.message?.includes('Network Error')) {
        setConnectionError("Backend server is not running. Please start the server at localhost:7000");
      } else {
        setConnectionError("Failed to load dashboard data. Please check your connection.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Error Banner */}
      {connectionError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Backend Connection Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{connectionError}</p>
                <div className="mt-2 p-3 bg-red-100 rounded">
                  <p><strong>Quick Fix:</strong></p>
                  <ol className="list-decimal list-inside mt-1 space-y-1">
                    <li>Open terminal and navigate to <code>backend-main</code> directory</li>
                    <li>Run: <code className="bg-red-200 px-1 rounded">npm start</code></li>
                    <li>Wait for "Server running on port 7000" message</li>
                    <li>Refresh this page</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header with Refresh Button */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">
            System overview and user statistics
            {lastUpdated && (
              <span className="text-sm text-gray-500 ml-2">
                • Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        <button
          onClick={fetchDashboardStats}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors shadow-sm"
        >
          <svg className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {loading ? 'Refreshing...' : 'Refresh Stats'}
        </button>
      </div>

      {/* Dashboard Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Total Users Card */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-blue-200 hover:border-blue-300 transform hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-semibold text-blue-700 uppercase tracking-wide">Total Users</h3>
              <div className="text-3xl font-bold text-blue-900 mt-2">
                {loading ? (
                  <div className="animate-pulse bg-blue-200 h-8 w-16 rounded"></div>
                ) : (
                  stats.totalUsers
                )}
              </div>
              <p className="text-xs text-blue-600 mt-2 font-medium">All registered users</p>
            </div>
            <div className="p-4 bg-blue-200 rounded-xl shadow-inner">
              <UserGroupIcon className="w-8 h-8 text-blue-700" />
            </div>
          </div>
        </div>

        {/* Active Users Card */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-blue-200 hover:border-blue-300 transform hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-semibold text-blue-700 uppercase tracking-wide">Active Users</h3>
              <div className="text-3xl font-bold text-blue-900 mt-2">
                {loading ? (
                  <div className="animate-pulse bg-blue-200 h-8 w-16 rounded"></div>
                ) : (
                  stats.activeUsers
                )}
              </div>
              <p className="text-xs text-blue-600 mt-2 font-medium">
                Currently logged in
                <span className="ml-2 inline-flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse inline-block"></span>
                  <span className="ml-1 text-xs">Live</span>
                </span>
              </p>
            </div>
            <div className="p-4 bg-blue-200 rounded-xl shadow-inner">
              <ShieldCheckIcon className="w-8 h-8 text-blue-700" />
            </div>
          </div>
        </div>

        {/* System Roles Card */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-blue-200 hover:border-blue-300 transform hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-semibold text-blue-700 uppercase tracking-wide">System Roles</h3>
              <div className="text-3xl font-bold text-blue-900 mt-2">
                {loading ? (
                  <div className="animate-pulse bg-blue-200 h-8 w-16 rounded"></div>
                ) : (
                  stats.totalRoles
                )}
              </div>
              <p className="text-xs text-blue-600 mt-2 font-medium">Available roles</p>
            </div>
            <div className="p-4 bg-blue-200 rounded-xl shadow-inner">
              <ClockIcon className="w-8 h-8 text-blue-700" />
            </div>
          </div>
        </div>
      </div>

    {/* Recent Activities */}
      <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-200 hover:border-gray-300">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
          <button
            onClick={() => router.push('/admin/logs')}
            className="text-sky-600 text-sm font-semibold hover:underline"
          >
            View All
          </button>
        </div>

        <div className="space-y-3">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            ))
          ) : stats.recentActivities && stats.recentActivities.length > 0 ? (
            stats.recentActivities.slice(0, 5).map((activity, index) => (
              <div key={activity._id || index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="w-8 h-8 bg-sky-100 rounded-full flex items-center justify-center">
                  <DocumentTextIcon className="w-4 h-4 text-sky-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.user?.fullName || activity.user?.name || 'Unknown User'}
                    </p>
                    <span className="text-xs text-gray-500">•</span>
                    <p className="text-xs text-gray-600 truncate">
                      {activity.action?.replace(/_/g, ' ') || 'Activity'}
                    </p>
                  </div>
                  <p className="text-xs text-gray-500 mt-1 truncate">
                    {activity.description || activity.action?.replace(/_/g, ' ') || 'No description available'}
                  </p>
                </div>
                <div className="text-xs text-gray-400 whitespace-nowrap">
                  {new Date(activity.createdAt).toLocaleDateString()}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <DocumentTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p className="text-sm">No recent activities found</p>
            </div>
          )}
        </div>
      </div>

      
    </div>
  );
}