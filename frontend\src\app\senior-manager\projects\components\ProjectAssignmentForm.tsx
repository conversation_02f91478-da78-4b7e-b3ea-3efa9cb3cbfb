"use client";

import React, { useState, useEffect } from "react";
import { api } from "@/services/api";

interface ProjectManager {
  _id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
}

interface ProjectAssignmentFormProps {
  projectId: string;
  projectTitle: string;
  onAssignmentComplete: () => void;
}

export default function ProjectAssignmentForm({ 
  projectId, 
  projectTitle, 
  onAssignmentComplete 
}: ProjectAssignmentFormProps) {
  const [projectManagers, setProjectManagers] = useState<ProjectManager[]>([]);
  const [selectedManagerId, setSelectedManagerId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProjectManagers = async () => {
      try {
        const response = await api.getProjectManagers();
        setProjectManagers(response.data || []);
      } catch (error) {
        console.error("Failed to fetch project managers:", error);
        alert("Failed to load project managers");
      } finally {
        setLoading(false);
      }
    };

    fetchProjectManagers();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Frontend validation
    if (!selectedManagerId || !selectedManagerId.trim()) {
      alert("Please select a project manager");
      return;
    }

    if (!projectId || !projectId.trim()) {
      alert("Invalid project ID");
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        projectId,
        projectManagerId: selectedManagerId
      };

      await api.assignProjectToManager(payload);
      alert("Project successfully assigned to project manager! The compiled project details and plan have been sent to the assigned manager.");
      onAssignmentComplete();
    } catch (error: any) {
      console.error("Failed to assign project:", error);

      // Handle validation errors from backend
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        const backendErrors = error.response.data.errors.join('\n');
        alert(`Validation failed:\n${backendErrors}`);
      } else if (error.response?.data?.message) {
        alert(`Error: ${error.response.data.message}`);
      } else {
        alert("An error occurred while assigning the project. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-8 bg-white shadow-lg rounded-xl border border-gray-100">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading project managers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-8 bg-white shadow-lg rounded-xl border border-gray-100">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Assign Project Manager</h2>
        <p className="text-gray-600">Assign <span className="font-semibold text-blue-600">{projectTitle}</span> to a project manager</p>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            Select Project Manager
          </label>
          <select
            value={selectedManagerId}
            onChange={(e) => setSelectedManagerId(e.target.value)}
            className="w-full border p-3 rounded-md"
            required
          >
            <option value="">Choose a project manager...</option>
            {projectManagers.map((manager) => (
              <option key={manager._id} value={manager._id}>
                {manager.fullName} ({manager.email})
              </option>
            ))}
          </select>
        </div>

        {selectedManagerId && (
          <div className="bg-blue-50 p-4 rounded-md">
            <h3 className="font-medium text-blue-900 mb-2">Assignment Details</h3>
            <p className="text-blue-800 text-sm">
              The selected project manager will receive:
            </p>
            <ul className="text-blue-800 text-sm mt-2 list-disc list-inside">
              <li>Complete project creation details</li>
              <li>Full project plan with activities, deliverables, and milestones</li>
              <li>Resource allocation and budget information</li>
              <li>Risk assessment and mitigation strategies</li>
              <li>Project assumptions and constraints</li>
              <li>Email notification with all compiled information</li>
              <li>In-app notification for immediate access</li>
            </ul>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => window.history.back()}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
          >
            Back
          </button>
          <button
            type="submit"
            disabled={isSubmitting || !selectedManagerId}
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? "Assigning..." : "Assign Project"}
          </button>
        </div>
      </form>

      {projectManagers.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No project managers available. Please contact the administrator.</p>
        </div>
      )}
    </div>
  );
}
