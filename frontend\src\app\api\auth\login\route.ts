import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log('🔐 Next.js API: Login request for:', body.email);

    const res = await axios.post(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000'}/api/v1/auth/login`, body, {
      headers: { 'Content-Type': 'application/json' },
      withCredentials: true,
    });

    const data = res.data;
    console.log('✅ Backend response:', {
      user: data.user?.fullName,
      role: data.user?.role?.name,
      hasToken: !!data.token
    });

    // Create response with user data and token
    const response = NextResponse.json({
      message: 'Login successful',
      user: data.user,
      token: data.token,
    });

    // Set token as HTTP-only cookie for security
    if (data.token) {
      response.cookies.set('token', data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 30 * 24 * 60 * 60, // 30 days
        path: '/',
      });
    }

    return response;
  } catch (error: any) {
    // Safe error logging
    if (error?.response?.data) {
      console.error('❌ Login API error:', error.response.data);
    } else if (error?.message) {
      console.error('❌ Login API error:', error.message);
    } else {
      console.error('❌ Login API error:', error);
    }

    // Safe error message extraction
    let message = 'Login failed';
    let status = 500;

    if (error?.response?.data?.message) {
      message = error.response.data.message;
    } else if (error?.message) {
      message = error.message;
    }

    if (error?.response?.status) {
      status = error.response.status;
    }

    return NextResponse.json({ message }, { status });
  }
}
